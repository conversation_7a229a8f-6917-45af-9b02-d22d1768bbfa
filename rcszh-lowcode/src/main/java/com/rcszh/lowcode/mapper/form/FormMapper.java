package com.rcszh.lowcode.mapper.form;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rcszh.lowcode.dto.form.FormMenuDto;
import com.rcszh.lowcode.entity.form.Form;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface FormMapper extends BaseMapper<Form> {
    /**
     * 获取所有分组
     *
     * @param group
     * @return
     */
    List<String> getAllGroup(String group);

    /**
     * 获取所有的表单信息（菜单专用）
     */
    List<FormMenuDto> getFormMenu();
}
