package com.rcszh.lowcode.schema;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Data
public class ViewConfigOptionsBaseSetting {

    /**
     * 数据过滤
     */
    private DataFilterConfig dataFilter;

    /**
     * TODO 基本信息
     */
    private String baseInfo;


    @Data
    public static class DataFilterConfig {

        /**
         * 流程数据
         * 1、我创建的 myCreated
         * 2、待我审的 myWaiting
         * 3、我已审的 myApproved
         */
        private String flowCondition;

        /**
         * 参数为空时忽略查询条件 nullIgnore、参数为空时不继续查询 nullNotQuery
         */
        private String filterType;

        private FilterConditionGroup conditionGroup;
    }

    public interface ICondition {
    }

    @Data
    public static class FilterConditionGroup implements ICondition {

        /**
         * 且、或
         */
        private String nestedType;

        /**
         * 条件 或 条件组
         */
        private List<ICondition> conditions;
    }

    @Data
    public static class FilterCondition implements ICondition {

        /**
         * 表单字段
         */
        private String field;

        /**
         * 查询类型 对应枚举 ViewFormListSelectTypeEnum
         */
        private String selectType;

        /**
         * 值类型:
         * 1、固定值(fixed)、
         * 2、入参(parameter)
         * a、query   url上的query
         * b、其他 TODO
         */
        private String valueType;

        /**
         * 值
         * 1、固定值
         * 2、表达式：query.projectId    表示取url上的query参数中的projectId
         */
        private String value;
    }

    private ViewConfigOptionsBaseSetting.FilterConditionGroup parseConditionGroup(Object conditionGroupObj) {
        if (Objects.isNull(conditionGroupObj)) {
            return null;
        }
        JSONObject conditionGroup = JSONUtil.parseObj(conditionGroupObj);
        ViewConfigOptionsBaseSetting.FilterConditionGroup filterConditionGroup = new ViewConfigOptionsBaseSetting.FilterConditionGroup();
        String nestedType = conditionGroup.getStr("nestedType");
        if (StrUtil.isNotBlank(nestedType)) {
            filterConditionGroup.setNestedType(nestedType);
        }
        List<ICondition> conditions = new ArrayList<>();
        List<Object> conditionList = conditionGroup.get("conditions", List.class);
        for (Object conditionObj : conditionList) {
            JSONObject jsonObject = JSONUtil.parseObj(conditionObj);
            String nt = jsonObject.getStr("nestedType");
            if (StrUtil.isNotBlank(nt)) {
                conditions.add(this.parseConditionGroup(conditionObj));
            } else {
                conditions.add(BeanUtil.toBean(conditionObj, FilterCondition.class));
            }
        }
        filterConditionGroup.setConditions(conditions);
        return filterConditionGroup;
    }

    private ViewConfigOptionsBaseSetting.DataFilterConfig parseDataFilter(JSONObject dataFilterObj) {
        ViewConfigOptionsBaseSetting.DataFilterConfig dataFilter = new ViewConfigOptionsBaseSetting.DataFilterConfig();
        String flowCondition = dataFilterObj.getStr("flowCondition");
        String filterType = dataFilterObj.getStr("filterType");
        Object conditionGroupObj = dataFilterObj.get("conditionGroup");

        if (StrUtil.isNotBlank(flowCondition)) {
            dataFilter.setFlowCondition(flowCondition);
        }
        if (StrUtil.isNotBlank(filterType)) {
            dataFilter.setFilterType(filterType);
        }
        FilterConditionGroup filterConditionGroup = this.parseConditionGroup(conditionGroupObj);
        if (Objects.nonNull(filterConditionGroup)) {
            dataFilter.setConditionGroup(filterConditionGroup);
        }
        return dataFilter;
    }


    public void parseFromStr(String jsonStr) {
        if (StrUtil.isBlank(jsonStr)) {
            return;
        }
        JSONObject setting = JSONUtil.parseObj(jsonStr);
        Object dataFilterObj = setting.get("dataFilter");
        if (dataFilterObj == null) {
            return;
        }
        DataFilterConfig dataFilterConfig = this.parseDataFilter(JSONUtil.parseObj(dataFilterObj));
        this.setDataFilter(dataFilterConfig);
    }
}
