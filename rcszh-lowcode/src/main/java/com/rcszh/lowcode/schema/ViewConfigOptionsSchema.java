package com.rcszh.lowcode.schema;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 视图配置项Json载体
 */
@Data
public class ViewConfigOptionsSchema {
    // 字段ID
    private String fieldId;
    // 字段编码
    private String fieldCode;
    // 字段名
    private String fieldName;
    /**
     * 字段类型
     * 主要用于显示配置，区分什么是标准字段什么是表格字段
     * 枚举：filed/table
     */
    private String fieldType;
    // 查询类型
    private String selectType;
    // 按钮类型
    private String buttonType;
    // 按钮名称
    private String buttonName;
    // 是否显示
    private String isShow;
    // 排序
    private Integer sort;
    // 排序类型 desc  asc
    private String sortType;
    // 条件
    private ConditionGroup conditionGroup;
    // 子项
    private List<ViewConfigOptionsSchema> children;

    /**
     * 构造方法
     */
    public ViewConfigOptionsSchema() {
        this.children = new ArrayList<>();
    }

    @Data
    public static class ConditionGroup {
        private String nestedType;
        private List<Condition> conditions;
    }

    @Data
    public static class Condition {
        private String field;
        private String selectType;
        private String valueType;
        private String value;
    }
}
