package com.rcszh.lowcode.schema;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class FormDesignInfoSchema {
    private List<FormDesignInfoSchema> children;
    private List<FormDesignInfoSchema> items;

    @JsonIgnore
    private Map<String,Object> otherFields = new LinkedHashMap<>();

    @JsonAnyGetter
    public Map<String,Object> getUnKnowField(){
        return otherFields;
    }
    @JsonAnySetter
    public void setUnKnowField(String key, Object value) {
        otherFields.put(key, value);
    }

}
