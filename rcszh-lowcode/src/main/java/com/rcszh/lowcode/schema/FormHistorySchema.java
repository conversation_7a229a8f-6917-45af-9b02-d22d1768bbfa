package com.rcszh.lowcode.schema;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rcszh.common.domain.BaseDomain;
import com.rcszh.lowcode.dto.form.FormTableDto;
import com.rcszh.lowcode.entity.form.Form;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class FormHistorySchema extends BaseDomain {
    private String id;
    private String formId;
    private Form formInfo;
    private List<FormTableDto> formTableInfo;

}
