package com.rcszh.lowcode.schema;

import com.rcszh.lowcode.dto.FormTableFieldDto;
import com.rcszh.lowcode.entity.form.Form;
import com.rcszh.lowcode.entity.form.FormTable;
import com.rcszh.lowcode.entity.form.FormTableField;
import lombok.Data;

import java.util.List;
@Data
public class FormInfoSchema {
    private Form form;
    private List<FormTable> formTables;
    private List<FormTableFieldDto> formTableFields;
    private List<String> deleteFieldIds;
}
