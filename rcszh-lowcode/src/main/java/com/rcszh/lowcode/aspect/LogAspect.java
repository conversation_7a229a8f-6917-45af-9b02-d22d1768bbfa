//package com.rcszh.lowcode.aspect;
//
//import org.aspectj.lang.JoinPoint;
//import org.aspectj.lang.annotation.After;
//import org.aspectj.lang.annotation.Aspect;
//import org.aspectj.lang.annotation.Before;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.stereotype.Component;
//
//@Aspect
//@Component
//public class LogAspect {
//    private static final Logger log = LoggerFactory.getLogger(LogAspect.class);
//
//    @Before("execution(* com.rcszh.lowcode.orm.ORM.*(..))")
//    public void beforeMethod(JoinPoint joinPoint) {
//        String methodName = joinPoint.getSignature().getName();
//        log.info("开始执行 {} 方法", methodName);
//    }
//
//    @After("execution(* com.rcszh.lowcode.orm.ORM.*(..))")
//    public void afterMethod(JoinPoint joinPoint) {
//        String methodName = joinPoint.getSignature().getName();
//        log.info("{} 方法执行结束", methodName);
//    }
//}
