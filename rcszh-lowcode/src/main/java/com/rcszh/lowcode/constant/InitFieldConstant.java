package com.rcszh.lowcode.constant;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.rcszh.common.constant.Constants;
import com.rcszh.lowcode.core.enums.UiComponentTypeEnum;
import com.rcszh.lowcode.entity.form.FormTableField;
import com.rcszh.lowcode.enums.FormTableFieldStatusEnum;
import com.rcszh.lowcode.enums.form_table_field.InitFieldTypeEnum;

import java.util.HashMap;

/**
 * 表单初始化字段常量
 */
public class InitFieldConstant {
    // 主键Id
    public static FormTableField ID(String formId, String formTableId) {
        FormTableField idField = new FormTableField();
        idField.setName(InitFieldTypeEnum.ID.getFieldName());
        idField.setLabel(InitFieldTypeEnum.ID.getFieldLabel());
        idField.setComponent(UiComponentTypeEnum.INPUT.getType());
        idField.setLabel("ID");
        idField.setFieldOrder(1);
        idField.setJdbcType(UiComponentTypeEnum.INPUT.getJdbcType());
        idField.setLength(32);
        idField.setFormId(formId);
        idField.setFormTableId(formTableId);
        idField.setStatus(FormTableFieldStatusEnum.CREATED.getStatus());
        idField.setFtfDisplay(Constants.N);
        idField.setFtfIsSystem(Constants.Y);
        JSONObject obj = JSONUtil.createObj();
        obj.putOpt("placeholder", "请输入");
        idField.setProps(obj.toString());
        return idField;
    }

    // 创建人
    public static FormTableField CREATOR(String formId, String formTableId) {
        FormTableField creatorField = new FormTableField();
        creatorField.setName(InitFieldTypeEnum.CREATOR.getFieldName());
        creatorField.setLabel(InitFieldTypeEnum.CREATOR.getFieldLabel());
        creatorField.setFieldOrder(2);
        creatorField.setFtfIsSystem(Constants.Y);
        creatorField.setFtfDisplay(Constants.N);
        creatorField.setFormId(formId);
        creatorField.setFormTableId(formTableId);
        creatorField.setComponent(UiComponentTypeEnum.DIALOG.getType());
        creatorField.setJdbcType(UiComponentTypeEnum.DIALOG.getJdbcType());
        creatorField.setLength(32);
        creatorField.setStatus(FormTableFieldStatusEnum.CREATED.getStatus());
        creatorField.setProps(getFieldProps2User());
        return creatorField;
    }

    // 创建人部门
    public static FormTableField CREATOR_DEPT(String formId, String formTableId) {
        FormTableField creatorDeptField = new FormTableField();
        creatorDeptField.setName(InitFieldTypeEnum.CREATOR_DEPT.getFieldName());
        creatorDeptField.setLabel(InitFieldTypeEnum.CREATOR_DEPT.getFieldLabel());
        creatorDeptField.setFieldOrder(3);
        creatorDeptField.setFtfIsSystem(Constants.Y);
        creatorDeptField.setFtfDisplay(Constants.N);
        creatorDeptField.setFormId(formId);
        creatorDeptField.setFormTableId(formTableId);
        creatorDeptField.setComponent(UiComponentTypeEnum.DIALOG.getType());
        creatorDeptField.setJdbcType(UiComponentTypeEnum.DIALOG.getJdbcType());
        creatorDeptField.setLength(UiComponentTypeEnum.DIALOG.getLength());
        creatorDeptField.setStatus(FormTableFieldStatusEnum.CREATED.getStatus());
        creatorDeptField.setProps(getFieldProps2Dept());
        return creatorDeptField;
    }

    // 创建时间
    public static FormTableField CREATE_TIME(String formId, String formTableId) {
        FormTableField createTimeField = new FormTableField();
        createTimeField.setName("create_time");
        createTimeField.setLabel("创建时间");
        createTimeField.setFieldOrder(4);
        createTimeField.setFtfIsSystem(Constants.Y);
        createTimeField.setFtfDisplay(Constants.N);
        createTimeField.setFormId(formId);
        createTimeField.setFormTableId(formTableId);
        createTimeField.setComponent(UiComponentTypeEnum.DATE_PICKER.getType());
        createTimeField.setJdbcType(UiComponentTypeEnum.DATE_PICKER.getJdbcType());
        createTimeField.setLength(UiComponentTypeEnum.DATE_PICKER.getLength());
        createTimeField.setStatus(FormTableFieldStatusEnum.CREATED.getStatus());
        createTimeField.setProps(getFieldProps2DateTime());
        return createTimeField;
    }

    // 拥有人
    public static FormTableField OWNER(String formId, String formTableId) {
        FormTableField ownerField = new FormTableField();
        ownerField.setName("owner");
        ownerField.setLabel("拥有者");
        ownerField.setFieldOrder(5);
        ownerField.setFtfIsSystem(Constants.Y);
        ownerField.setFtfDisplay(Constants.N);
        ownerField.setFormId(formId);
        ownerField.setFormTableId(formTableId);
        ownerField.setComponent(UiComponentTypeEnum.DIALOG.getType());
        ownerField.setJdbcType(UiComponentTypeEnum.DIALOG.getJdbcType());
        ownerField.setLength(UiComponentTypeEnum.DIALOG.getLength());
        ownerField.setStatus(FormTableFieldStatusEnum.CREATED.getStatus());
        ownerField.setProps(getFieldProps2User());
        return ownerField;
    }

    // 拥有人部门
    public static FormTableField OWNER_DEPT(String formId, String formTableId) {
        FormTableField ownerDeptField = new FormTableField();
        ownerDeptField.setName("owner_dept");
        ownerDeptField.setLabel("拥有人部门");
        ownerDeptField.setFieldOrder(6);
        ownerDeptField.setFtfIsSystem(Constants.Y);
        ownerDeptField.setFtfDisplay(Constants.N);
        ownerDeptField.setFormId(formId);
        ownerDeptField.setFormTableId(formTableId);
        ownerDeptField.setComponent(UiComponentTypeEnum.DIALOG.getType());
        ownerDeptField.setJdbcType(UiComponentTypeEnum.DIALOG.getJdbcType());
        ownerDeptField.setLength(UiComponentTypeEnum.DIALOG.getLength());
        ownerDeptField.setStatus(FormTableFieldStatusEnum.CREATED.getStatus());
        ownerDeptField.setProps(getFieldProps2Dept());
        return ownerDeptField;
    }

    // 更新人
    public static FormTableField UPDATER(String formId, String formTableId) {
        FormTableField updaterField = new FormTableField();
        updaterField.setName("updater");
        updaterField.setLabel("更新人");
        updaterField.setFieldOrder(7);
        updaterField.setFtfIsSystem(Constants.Y);
        updaterField.setFtfDisplay(Constants.N);
        updaterField.setFormId(formId);
        updaterField.setFormTableId(formTableId);
        updaterField.setComponent(UiComponentTypeEnum.DIALOG.getType());
        updaterField.setJdbcType(UiComponentTypeEnum.DIALOG.getJdbcType());
        updaterField.setLength(UiComponentTypeEnum.DIALOG.getLength());
        updaterField.setStatus(FormTableFieldStatusEnum.CREATED.getStatus());
        updaterField.setProps(getFieldProps2User());
        return updaterField;
    }

    // 更新时间
    public static FormTableField UPDATE_TIME(String formId, String formTableId) {
        FormTableField updateTimeField = new FormTableField();
        updateTimeField.setName("update_time");
        updateTimeField.setLabel("更新时间");
        updateTimeField.setFieldOrder(8);
        updateTimeField.setFtfIsSystem(Constants.Y);
        updateTimeField.setFtfDisplay(Constants.N);
        updateTimeField.setFormId(formId);
        updateTimeField.setFormTableId(formTableId);
        updateTimeField.setComponent(UiComponentTypeEnum.DATE_PICKER.getType());
        updateTimeField.setJdbcType(UiComponentTypeEnum.DATE_PICKER.getJdbcType());
        updateTimeField.setLength(UiComponentTypeEnum.DATE_PICKER.getLength());
        updateTimeField.setStatus(FormTableFieldStatusEnum.CREATED.getStatus());
        updateTimeField.setProps(getFieldProps2DateTime());
        return updateTimeField;
    }

    // 流程状态
    public static FormTableField FLOW_STATUS(String formId, String formTableId) {
        FormTableField flowStatusField = new FormTableField();
        flowStatusField.setName("flow_status");
        flowStatusField.setLabel("流程状态");
        flowStatusField.setFieldOrder(9);
        flowStatusField.setFtfIsSystem(Constants.Y);
        flowStatusField.setFtfDisplay(Constants.N);
        flowStatusField.setFormId(formId);
        flowStatusField.setFormTableId(formTableId);
//        flowStatusField.setFormTableName(formTableName);
        flowStatusField.setComponent(UiComponentTypeEnum.SELECT.getType());
        flowStatusField.setJdbcType(UiComponentTypeEnum.SELECT.getJdbcType());
        flowStatusField.setLength(UiComponentTypeEnum.SELECT.getLength());
        flowStatusField.setStatus(FormTableFieldStatusEnum.CREATED.getStatus());
        HashMap<Object, Object> props = new HashMap<>();
        props.put("mode", "dict");
        props.put("dictKey", "flow_status");
        flowStatusField.setProps(JSONUtil.parse(props).toString());
        return flowStatusField;
    }

    // 表单
    public static FormTableField FORM_ID(String formId, String formTableId) {
        FormTableField formIdField = new FormTableField();
        formIdField.setName("form_id");
        formIdField.setLabel("表单ID");
        formIdField.setFieldOrder(10);
        formIdField.setFtfIsSystem(Constants.Y);
        formIdField.setFtfDisplay(Constants.N);
        formIdField.setFormId(formId);
        formIdField.setFormTableId(formTableId);
//        formIdField.setFormTableName(formTableName);
        formIdField.setComponent(UiComponentTypeEnum.INPUT.getType());
        formIdField.setJdbcType(UiComponentTypeEnum.INPUT.getJdbcType());
        formIdField.setLength(UiComponentTypeEnum.INPUT.getLength());
        formIdField.setStatus(FormTableFieldStatusEnum.CREATED.getStatus());
        return formIdField;
    }


    private static String getFieldProps2User() {
        return "{\"placeholder\":\"请点击选择\",\"configJson\":{\"config\":{\"autoQuery\":\"Y\",\"field\":[{\"code\":\"nickName\",\"label\":\"员工姓名\"},{\"code\":\"userName\",\"label\":\"员工账号\"}],\"query\":[{\"code\":\"searchValue\",\"label\":\"关键字\",\"type\":\"text\"}],\"singleGrid\":\"Y\"},\"labelField\":\"nickName\",\"bindField\":\"userId\",\"title\":\"选择用户\",\"requestUrl\":\"basic/user/list\",\"rowKey\":\"userId\",\"model\":\"SysUser\",\"static\":{\"status\":\"0\"},\"displayType\":\"chooseDialog\"},\"disabled\":true}";
    }

    private static String getFieldProps2Dept() {
        return "{\"placeholder\":\"请点击选择\",\"clearable\":true,\"configJson\":{\"config\":{\"autoQuery\":\"Y\",\"field\":[{\"code\":\"deptName\",\"label\":\"名称\"},{\"code\":\"deptCode\",\"label\":\"编码\"}],\"query\":[{\"code\":\"deptName\",\"label\":\"名称\",\"type\":\"text\"}],\"singleGrid\":\"Y\"},\"labelField\":\"deptName\",\"bindField\":\"deptId\",\"title\":\"选择部门\",\"requestUrl\":\"basic/dept/findDeptPage\",\"rowKey\":\"deptId\",\"model\":\"SysDept\",\"displayType\":\"chooseDialog\"}}";
    }

    private static String getFieldProps2DateTime() {
        return "{\"type\":\"datetime\",\"placeholder\":\"请选择日期\",\"clearable\":false,\"valueFormat\":\"YYYY-MM-DD HH:mm:ss\"}";
    }

}
