package com.rcszh.lowcode.service.form;

import com.baomidou.mybatisplus.extension.service.IService;
import com.rcszh.lowcode.dto.FormTableFieldDto;
import com.rcszh.lowcode.entity.form.FormTable;
import com.rcszh.lowcode.entity.form.FormTableField;

import java.util.List;
import java.util.Map;

public interface IFormTableFieldService extends IService<FormTableField> {
    Map<String, List<FormTableFieldDto>> getFieldByFormIdToMap(String formId);

    /**
     * 通过formId获取
     */
    List<FormTableFieldDto> getFieldByFormIdToList(String formId);

    /**
     * 通过formTableId获取
     */
    List<FormTableFieldDto> getFieldByTable(String formTableId);

    /**
     * 通过formTableId获取有用的字段
     */
    List<FormTableFieldDto> getUsefulFieldByTable(String formTableId);

    /**
     * 保存或者更新表单字段
     * @param formTableFields 表单字段
     */
    void saveOrUpdateTableFields(List<FormTableFieldDto> formTableFields);

    /**
     * 更新或者保存表单字段
     */
    FormTableField saveOrUpdateTableField(FormTableFieldDto formTableField);

    void generateChildInitFields(FormTable mainTable, String formId, String formTableId);

    List<FormTableField> generateInitFields(String formId, String formTableId, String formTableName);

    void updateInitFields(String formId);

    int deleteById(String tableFieldId);

    /**
     * 批量删除字段
     * @param deleteFieldIds 需要被删除的字段id
     * @param isDoDelete 是否真的执行删除
     */
    void deleteFields(List<String> deleteFieldIds,boolean isDoDelete);

    /**
     * 通过字段id列表查询对应的表单字段
     *
     * @param showFieldIdList 字段id列表
     * @return 表单字段列表
     */
    List<FormTableFieldDto> getFieldByIdList(List<String> showFieldIdList);

    /**
     * 真实删除字段
     *
     * @param tableFieldId 字段ID
     */
    void realDeleteField(String tableFieldId);

    /**
     * 根据字段id查询字段
     * @param tableFieldId 字段ID
     * @return 表单字段
     */
    FormTableFieldDto getFieldById(String tableFieldId);

    /**
     * 查找对应表单下的所有系统内置字段
     * @param formId 表单id
     * @return
     */
    List<FormTableField> getSystemFiled(String formId);
}
