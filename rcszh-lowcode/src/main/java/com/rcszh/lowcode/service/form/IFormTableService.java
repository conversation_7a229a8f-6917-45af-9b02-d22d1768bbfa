package com.rcszh.lowcode.service.form;

import com.baomidou.mybatisplus.extension.service.IService;
import com.rcszh.lowcode.dto.form.FormTableDto;
import com.rcszh.lowcode.entity.form.FormTable;
import com.rcszh.lowcode.entity.form.FormTableField;

import java.util.List;

public interface IFormTableService extends IService<FormTable> {


    List<FormTable> getAllFormTable();

    void updateDataModeling(List<FormTable> formTables, List<FormTableField> formTableFields);

    FormTable createChildTable(FormTable formTable);

//    void initChildFormTable(FormTable formTable);

    void createFormTable(FormTable formTable, List<FormTableField> formTableFields);

    void deleteById(String id);

    void update(FormTable formTable);

    FormTable getTableById(String id);

    List<FormTable> getTableByFormId(String formId);
    // 根据表id查询
    List<FormTable> getTableByTableIdList(List<String> tableIdList);

    FormTable getFormTableByTableName(String tableName);

    void initMainFormTable(FormTable formTable);

    void batchUpdateFormTable(List<FormTableDto> formTables);

    /**
     * 查询主表
     */
    FormTable getMainTale(String formId);
}
