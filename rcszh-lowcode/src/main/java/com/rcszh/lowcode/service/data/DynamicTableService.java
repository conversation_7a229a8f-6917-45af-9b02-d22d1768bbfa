package com.rcszh.lowcode.service.data;


import com.rcszh.lowcode.dto.data.SelectConditionDto;
import com.rcszh.lowcode.dto.data.SelectConditionGroupDto;
import com.rcszh.lowcode.dto.data.SelectSortDto;
import com.rcszh.lowcode.enums.action.FormActionTypeEnum;
import com.rcszh.lowcode.enums.view_form.ViewFormListSelectTypeEnum;
import com.rcszh.lowcode.orm.entity.PageResult;
import com.rcszh.lowcode.orm.enums.SelectParamTypeEnum;
import com.rcszh.lowcode.orm.factory.ORMFactory;
import com.rcszh.lowcode.orm.factory.OrmConfig;
import com.rcszh.lowcode.orm.params.SelectParamBuilder;
import com.rcszh.lowcode.orm.params.SelectWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 动态数据库服务类（业务对象）
 */
@Service
public class DynamicTableService {
    public static final String ID_KEY = "id";

    public static final String IDS_KEY = "ids";
    /**
     * ORM工厂
     */
    @Autowired
    private ORMFactory ormFactory;

    /**
     * 操作真实库表数据：添加、更新、删除
     *
     * @param tableName   库表名称
     * @param action      动作类型
     * @param actionValue 数据载体
     */
    public void doActionToDynamicTable(String tableName, String action, Map<String, Object> actionValue) {
        if (FormActionTypeEnum.INSERT.getType().equals(action)) {
            addDynamicTableData(tableName, actionValue);
        } else if (FormActionTypeEnum.UPDATE.getType().equals(action)) {
            updateDynamicTableData(tableName, actionValue);
        } else if (FormActionTypeEnum.DELETE.getType().equals(action)) {
            deleteDynamicTableData(tableName, actionValue.get(ID_KEY).toString());
        } else if (FormActionTypeEnum.BATCH_DELETE.getType().equals(action)) {
            batchDeleteDynamicTableData(tableName, (List<String>) actionValue.get(IDS_KEY));
        } else {
            throw new RuntimeException("错误的ROM执行类型");
        }
    }


    private void loadSelectParams(SelectWrapper selectWrapper, SelectConditionGroupDto conditionGroupDto) {
        if (conditionGroupDto != null) {
            for (SelectConditionDto condition : conditionGroupDto.getConditions()) {
                ViewFormListSelectTypeEnum viewFormListSelectTypeEnum = Objects.requireNonNull(condition.getSelectType());
                SelectParamTypeEnum selectParamTypeEnum = viewFormListSelectTypeEnum.getSelectParamTypeEnum();
                selectWrapper.pushSelectParam(SelectParamBuilder.builder()
                        .column(condition.getField())
                        .type(selectParamTypeEnum)
                        .value(viewFormListSelectTypeEnum.covertValue(condition.getValue()))
                );
            }
        }
    }

    private void loadSelectSorts(SelectWrapper selectWrapper, List<SelectSortDto> selectSorts) {
        if (Objects.nonNull(selectSorts)) {
            for (SelectSortDto selectSort : selectSorts) {
                selectWrapper.pushSort(selectSort.getField(), selectSort.getSortType());
            }
        }
    }

    /**
     * @param tableName 业务对象数据库表名
     * @return 获取所有业务对象列表数据
     */
    public PageResult<Map<String, Object>> getRealTableDataPage(String tableName, Long pageNum, Long pageSize,
                                                                SelectConditionGroupDto conditionGroupDto, List<SelectSortDto> selectSorts) {
        // 拼接ORM所需要的查询参数
        SelectWrapper selectWrapper = SelectWrapper.create()
                .pageNum(pageNum)
                .pageSize(pageSize);
        this.loadSelectParams(selectWrapper, conditionGroupDto);
        this.loadSelectSorts(selectWrapper, selectSorts);
        return ormFactory.createORM(OrmConfig.config().tableName(tableName)).selectPageToMap(selectWrapper);
    }

    /**
     * @param tableName 业务对象数据库表名
     * @return 获取所有业务对象列表数据
     */
    public List<Map<String, Object>> getRealTableDataList(String tableName, SelectConditionGroupDto conditionGroupDto, List<SelectSortDto> selectSorts) {
        // 拼接ORM所需要的查询参数
        SelectWrapper selectWrapper = SelectWrapper.create();
        this.loadSelectParams(selectWrapper, conditionGroupDto);
        this.loadSelectSorts(selectWrapper, selectSorts);
        return ormFactory.createORM(OrmConfig.config().tableName(tableName)).selectListToMap(selectWrapper);
    }

    /**
     * 获取所有业务对象列表数据
     *
     * @param tableName         业务对象数据库表名
     * @param fields            字段
     * @param conditionGroupDto 条件
     * @param selectSorts       排序
     * @return 列表数据
     */
    public List<Map<String, Object>> getRealTableDataList(String tableName, List<String> fields, SelectConditionGroupDto conditionGroupDto, List<SelectSortDto> selectSorts) {
        // 拼接ORM所需要的查询参数
        SelectWrapper selectWrapper = SelectWrapper.create();
        this.loadSelectParams(selectWrapper, conditionGroupDto);
        this.loadSelectSorts(selectWrapper, selectSorts);
        return ormFactory.createORM(OrmConfig.config().tableName(tableName).selectFields(fields)).selectListToMap(selectWrapper);
    }



    /**
     * 添加业务对象数据
     *
     * @param tableName 数据表
     * @param tableInfo 数据信息
     */
    public void addDynamicTableData(String tableName, Map<String, Object> tableInfo) {
        ormFactory.createORM(OrmConfig.config().tableName(tableName)).insertByMap(tableInfo);
    }

    /**
     * 删除业务对象
     *
     * @param tableName 业务对象数据库表名
     */
    public void deleteDynamicTableData(String tableName, String fieldId) {
        ormFactory.createORM(OrmConfig.config().tableName(tableName)).deleteById(fieldId);
    }

    /**
     * 批量删除业务对象
     *
     * @param tableName 业务对象数据库表名
     * @param fieldIds  字段
     */
    public void batchDeleteDynamicTableData(String tableName, List<String> fieldIds) {
        ormFactory.createORM(OrmConfig.config().tableName(tableName)).deleteByIds(fieldIds);
    }

    /**
     * 删除子表数据
     * @param mainTableName 主表名
     * @param childTableName 子表名
     * @param mainRowId 主表真实数据Id
     */
    public void deleteChildData(String mainTableName, String childTableName, String mainRowId) {
        SelectWrapper selectWrapper = SelectWrapper.create();
        selectWrapper.eq(mainTableName+"_"+ID_KEY,mainRowId);
        ormFactory.createORM(OrmConfig.config().tableName(childTableName)).delete(selectWrapper);
    }

    /**
     * 批量删除子表数据
     * @param mainTableName 主表名
     * @param childTableName 子表名
     * @param mainRowIds 主表真实数据Id列表
     */
    public void batchDeleteChildData(String mainTableName, String childTableName, List<String> mainRowIds) {
        SelectWrapper selectWrapper = SelectWrapper.create();
        selectWrapper.in(mainTableName+"_"+ID_KEY, mainRowIds);
        ormFactory.createORM(OrmConfig.config().tableName(childTableName)).delete(selectWrapper);
    }

    /**
     * 获取业务对象
     *
     * @param tableName 业务对象数据库表名
     * @param fieldId   业务对象数据库Id
     */
    public Map<String, Object> getDynamicTableDataById(String tableName, String fieldId) {
        return ormFactory.createORM(OrmConfig.config().tableName(tableName)).selectByIdToMap(fieldId);
    }

    public Map<String, Object> getDynamicTableOne(String tableName, SelectConditionGroupDto conditionGroupDto) {
        SelectWrapper selectWrapper = SelectWrapper.create();
        this.loadSelectParams(selectWrapper, conditionGroupDto);
        return ormFactory.createORM(OrmConfig.config().tableName(tableName)).selectOneToMap(selectWrapper);
    }

    /**
     * 更新业务对象
     *
     * @param tableName 业务对象数据库表名
     * @param tableInfo 要更新的业务对象数据
     */
    public void updateDynamicTableData(String tableName, Map<String, Object> tableInfo) {
        ormFactory.createORM(OrmConfig.config().tableName(tableName)).update(tableInfo);
    }

    public List<Map<String, Object>> getDynamicTableDataListByMainTableId(String mainTableName, String childTableName, Object mainId) {
        SelectWrapper selectWrapper = SelectWrapper.create()
                .eq(mainTableName + "_id", mainId);
        return ormFactory.createORM(OrmConfig.config().tableName(childTableName)).selectListToMap(selectWrapper);
    }

}
