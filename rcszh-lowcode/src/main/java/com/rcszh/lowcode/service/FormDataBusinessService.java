package com.rcszh.lowcode.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.rcszh.activiti.domain.WfApplyOrg;
import com.rcszh.activiti.domain.WfTemplate;
import com.rcszh.activiti.domain.dto.WfFlowStartDto;
import com.rcszh.activiti.domain.vo.WfDataPermItemVo;
import com.rcszh.activiti.domain.vo.WfHiProcessInstanceVo;
import com.rcszh.activiti.enums.OwnerType;
import com.rcszh.activiti.enums.PublishStatus;
import com.rcszh.activiti.service.IWfApplyOrgService;
import com.rcszh.activiti.service.IWfExtService;
import com.rcszh.activiti.service.IWfRuntimeService;
import com.rcszh.activiti.service.IWfTemplateService;
import com.rcszh.base.common.core.domain.model.LoginUser;
import com.rcszh.base.common.exception.ServiceException;
import com.rcszh.base.common.utils.SecurityUtils;
import com.rcszh.base.common.utils.spring.SpringUtils;
import com.rcszh.cache.CacheUtil;
import com.rcszh.cache.SimpleObjCacheDto;
import com.rcszh.cache.service.CacheService;
import com.rcszh.common.constant.Constants;
import com.rcszh.common.domain.dto.OrderKeyInfoDto;
import com.rcszh.common.util.OrderUtil;
import com.rcszh.lowcode.core.UiComponentFactory;
import com.rcszh.lowcode.core.enums.UiComponentTypeEnum;
import com.rcszh.lowcode.core.registry.ApiRegistry;
import com.rcszh.lowcode.dto.FormTableFieldDto;
import com.rcszh.lowcode.dto.data.*;
import com.rcszh.lowcode.dto.excel.ExportExcelParam;
import com.rcszh.lowcode.entity.form.Form;
import com.rcszh.lowcode.entity.form.FormTable;
import com.rcszh.lowcode.entity.form.FormTableField;
import com.rcszh.lowcode.entity.form_init.FormInitRule;
import com.rcszh.lowcode.entity.view.ViewForm;
import com.rcszh.lowcode.entity.view.ViewFormConfig;
import com.rcszh.lowcode.enums.FlowStatusEnum;
import com.rcszh.lowcode.enums.FormTableTypeEnum;
import com.rcszh.lowcode.enums.action.FormActionTypeEnum;
import com.rcszh.lowcode.enums.excel.ExportExcelParamTypeEnum;
import com.rcszh.lowcode.enums.form_table_field.InitFieldTypeEnum;
import com.rcszh.lowcode.enums.view_form.ViewFormListSelectTypeEnum;
import com.rcszh.lowcode.enums.view_form.ViewFormTypeEnum;
import com.rcszh.lowcode.feign_api.MessageTemplateInterface;
import com.rcszh.lowcode.feign_api.UserTemplateInterface;
import com.rcszh.lowcode.feign_api.entity.FormUser;
import com.rcszh.lowcode.feign_api.entity.message.FormMsgLog;
import com.rcszh.lowcode.feign_api.entity.message.SendMessageParam;
import com.rcszh.lowcode.orm.entity.PageResult;
import com.rcszh.lowcode.schema.ViewConfigOptionsSchema;
import com.rcszh.lowcode.service.data.DynamicTableService;
import com.rcszh.lowcode.service.form.IFormService;
import com.rcszh.lowcode.service.form.IFormTableFieldService;
import com.rcszh.lowcode.service.form.IFormTableService;
import com.rcszh.lowcode.service.form_init.IFormInitRuleService;
import com.rcszh.lowcode.service.view.IViewFormConfigService;
import com.rcszh.lowcode.service.view.IViewFormService;
import com.rcszh.lowcode.utils.DataExportUtil;
import com.rcszh.lowcode.utils.excel_export.EasyExcelExportUtil;
import com.rcszh.system.service.ISysUserService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.script.Bindings;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import java.io.IOException;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class FormDataBusinessService {

    @Resource
    private DynamicTableService dynamicTableService;
    @Resource
    private IViewFormService viewFormService;

    @Autowired
    private IFormInitRuleService formInitRuleService;

    @Autowired
    private CacheService cacheService;

    @Autowired
    private IFormService formService;

    @Autowired
    private ISysUserService sysUserService;

    @Resource
    private IFormTableFieldService formTableFieldService;
    @Resource
    private IViewFormConfigService viewFormConfigService;

    @Resource
    private IFormTableService formTableService;

    @Autowired
    private IWfRuntimeService wfRuntimeService;

    @Autowired
    private IWfExtService wfExtService;

    @Autowired
    private IWfTemplateService wfTemplateService;

    @Autowired
    private IWfApplyOrgService wfApplyOrgService;

    @Autowired
    private CacheUtil cacheUtil;

    @Autowired
    private ApplicationContext applicationContext;

    private Map<String, Object> getInitFieldData(String formId) {
        Map<String, Object> result = new HashMap<>();
        result.put("form_id", formId);

        LoginUser loginUser = SecurityUtils.getLoginUser();

        result.put("creator", loginUser.getUserId());
        result.put("create_time", new Date());
        result.put("creator_dept", loginUser.getDeptId());

        result.put("owner", loginUser.getUserId());
        result.put("owner_dept", loginUser.getDeptId());

        return result;
    }

    private Map<String, Object> fillBaseFieldData(String formId, Map<String, Object> data) {
        Map<String, Object> initFieldData = this.getInitFieldData(formId);
        // 如果已有creator、create_time、creator_dept字段，覆盖掉initFieldData的值，以传进来的为准
        initFieldData.putAll(data);

        LoginUser loginUser = SecurityUtils.getLoginUser();

        // 暂时以最后一次提交作为单据拥有者 TODO
        initFieldData.put("owner", loginUser.getUserId());
        initFieldData.put("owner_dept", loginUser.getDeptId());

        initFieldData.put("update_time", new Date());
        initFieldData.put("updater", loginUser.getUserId());
        return initFieldData;
    }

    public static Map<String, Object> deepBeanToMap(Object bean) {
        if (bean == null) {
            return null;
        }

        Map<String, Object> map = BeanUtil.beanToMap(bean);
        Map<String, Object> deepMap = new HashMap<>();

        for (Map.Entry<String, Object> entry : map.entrySet()) {
            Object value = entry.getValue();
            if (value != null && !isSimpleValueType(value)) {
                value = deepBeanToMap(value);
            }
            deepMap.put(entry.getKey(), value);
        }

        return deepMap;
    }

    private static boolean isSimpleValueType(Object value) {
        return value instanceof String
                || value instanceof Number
                || value instanceof Boolean
                || value instanceof Date
                || value.getClass().isPrimitive();
    }


    public ModelingDataViewResDto initFormData(String formId, Map<String, Object> params) {
        ModelingDataViewResDto result = new ModelingDataViewResDto();
        List<FormTable> formTables = formTableService.list(new LambdaQueryWrapper<FormTable>()
                .eq(FormTable::getFormId, formId)
        );
        List<FormTableField> tableFields = formTableFieldService.list(new LambdaQueryWrapper<FormTableField>()
                .eq(FormTableField::getFormId, formId)
        );

        // init数据
        Map<String, Object> formData = new HashMap<>(this.getInitFieldData(formId));

        this.fillDataByInitRule(params, formData, formTables, tableFields);

        Map<String, Object> formComponentData = UiComponentFactory.transformData2Component(tableFields, formData);
        FormTable mainTable = formTables.stream().filter(item -> item.getType().equals(FormTableTypeEnum.MAIN.getType())).findFirst().get();
        List<FormTable> childTables = formTables.stream().filter(item -> FormTableTypeEnum.CHILD.getType().equals(item.getType())).toList();
        Map<String, List<FormTableField>> fieldGroup = tableFields.stream().collect(Collectors.groupingBy(item -> item.getFormTableId()));
        for (FormTable childTable : childTables) {
            if (FormTableTypeEnum.MAIN.getType().equals(childTable.getType())) {
                continue;
            }
            List<Map<String, Object>> dbRows = dynamicTableService.getDynamicTableDataListByMainTableId(mainTable.getTableName(), childTable.getTableName(), formData.get("id"));
            UiComponentFactory.transformDataList2Component(fieldGroup.get(childTable.getId()), dbRows);
            formComponentData.put(childTable.getTableName(), dbRows);
        }
        result.setFormData(formComponentData);
        result.setFormId(formId);

        boolean resubmit = true;
        String flowStatus = null;
        WfTemplate flowTemplate = null;
        List<WfDataPermItemVo> permItemVos = new ArrayList<>();
        ModelingOperationAuthDto operationAuthDto = new ModelingOperationAuthDto();
        if (Objects.nonNull(formComponentData.get("flow_status"))) {
            Map<String, Object> statusObj = (Map<String, Object>) formComponentData.get("flow_status");
            result.setFlowStatus(statusObj);
            flowStatus = (String) statusObj.get("id");
        }
        WfHiProcessInstanceVo wfHiProcessInstanceVo;
        if (Objects.nonNull(formData.get("id"))) {
            wfHiProcessInstanceVo = wfRuntimeService.getLatestInstanceByBusinessKey(formData.get("id").toString());
            if (wfHiProcessInstanceVo != null) {
                result.setFlowInstanceId(wfHiProcessInstanceVo.getId());
                permItemVos = this.getCurrentUserDataPerms(wfHiProcessInstanceVo, SecurityUtils.getUserIdStr());
                if (Arrays.asList(FlowStatusEnum.running.name(), FlowStatusEnum.passed.name()).contains(flowStatus)) {
                    flowTemplate = wfTemplateService.getByProcessDefinitionId(wfHiProcessInstanceVo.getProcessDefinitionId());
                    resubmit = false;
                    operationAuthDto.setCanSave(false);
                    operationAuthDto.setCanSubmit(false);
                }
            }
        }
        if (Objects.isNull(flowTemplate)) {
            flowTemplate = wfRuntimeService.getCurrentFlowTemplate(formId, false);
        }
        if (flowTemplate != null) {
            result.setFlowTemplateId(flowTemplate.getTemplateId());

            Map<String, Object> processProperties = wfRuntimeService.getProcessProperties(flowTemplate.getActDefinitionId());
            if (Objects.nonNull(processProperties.get("resubmit")) && Constants.N.equals(processProperties.get("resubmit"))) {
                resubmit = false;
                operationAuthDto.setCanSave(false);
                operationAuthDto.setCanSubmit(false);
            }
        }

        Map<String, Object> fieldsAuth = this.getFieldsAuth2(formTables, tableFields, flowStatus, permItemVos);
        result.setFieldsAuth(fieldsAuth);
        result.setOperationAuth(operationAuthDto);
        return result;
    }

    private void fillDataByInitRule(Map<String, Object> params, Map<String, Object> formData, List<FormTable> formTables, List<FormTableField> tableFields) {
        String ruleCode = (String) params.get("ruleCode");
        if (StrUtil.isNotBlank(ruleCode)) {
            FormInitRule formInitRule = formInitRuleService.getByCode(ruleCode);
            if (Objects.nonNull(formInitRule) && "dataSource".equals(formInitRule.getType())) {
                JSONObject jsonOptions = formInitRule.getJsonOptions();
                if (Objects.nonNull(jsonOptions)) {
                    String className = jsonOptions.getStr("class");
                    String method = jsonOptions.getStr("method");
                    JSONArray args = jsonOptions.getJSONArray("args");
                    JSONArray query = jsonOptions.getJSONArray("query");
                    List<Object> argsList = new ArrayList<>();
                    if (CollUtil.isNotEmpty(args)) {
                        for (Object arg : args) {
                            argsList.add(params.get((String) arg));
                        }
                    }
                    try {
                        Class<?> clazz = Class.forName(className);
                        Object bean = SpringUtils.getBean(clazz);
                        for (Method clazzMethod : clazz.getMethods()) {
                            if (clazzMethod.getName().equals(method)) {
                                Class<?>[] parameterTypes = clazzMethod.getParameterTypes();

                                List<Object> typeArgs = new ArrayList<>();
                                for (int i = 0; i < parameterTypes.length; i++) {
                                    Object convert = Convert.convert(parameterTypes[i], argsList.get(i));
                                    typeArgs.add(convert);
                                }
                                Object invokeResult = clazzMethod.invoke(bean, typeArgs.toArray());
                                Map<String, Object> formData2 = deepBeanToMap(invokeResult);
                                formData2.put("$ruleCode", ruleCode);
                                formData.putAll(formInitRuleService.transformData(formData2));

                                // 如果有查询条件，查询数据
                                if (CollUtil.isNotEmpty(query)) {
                                    SelectConditionGroupDto conditionGroupDto = new SelectConditionGroupDto();
                                    List<SelectConditionDto> conditionDtos = conditionGroupDto.getConditions();
                                    for (Object item : query) {
                                        if (item instanceof String queryField) {
                                            SelectConditionDto conditionDto = new SelectConditionDto();
                                            conditionDto.setField(queryField);
                                            conditionDto.setSelectType(ViewFormListSelectTypeEnum.EQ);
                                            conditionDto.setValue(formData.get(queryField));
                                            conditionDtos.add(conditionDto);
                                        } else {
                                            JSONObject itemObj = (JSONObject) item;
                                            conditionDtos.addAll(viewFormService.transformFieldConditions(itemObj));
                                        }

                                    }
                                    FormTable mainTable = formTables.stream().filter(item -> item.getType().equals(FormTableTypeEnum.MAIN.getType())).findFirst().get();
                                    Map<String, Object> dynamicTableOneData = dynamicTableService.getDynamicTableOne(mainTable.getTableName(), conditionGroupDto);

                                    if (Objects.nonNull(dynamicTableOneData)) {
                                        formData.putAll(dynamicTableOneData);


                                    }
                                }
                                break;
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                }

            }
        }
    }

    private String getFlowTitle(String ObjDataType, Map<String, Object> variables) {
        WfTemplate template = wfRuntimeService.getCurrentFlowTemplate(ObjDataType, true);
        Map<String, Object> processExtensionProperties = wfExtService.getProcessExtensionProperties(template.getActDefinitionId());
        String flowTitle = (String) processExtensionProperties.get("flowTitle");
        if (StrUtil.isBlank(flowTitle)) {
            // 默认标题：${formName}-${projectName}-${ownerName}-${createTime}
            Map<String, Object> currentRecord = (Map<String, Object>) variables.get("currentRecord");
            if (Objects.nonNull(currentRecord.get("project_id"))) {
                flowTitle = "${actCommon.getFormName(currentRecord.form_id)}-${actCommon.getProjectFieldValue(currentRecord.project_id, \"name\")}-${actCommon.getUserFieldValue(currentRecord.owner, \"nickName\")}-${actDate.formatDate(currentRecord.create_time)}";
            } else {
                flowTitle = "${actCommon.getFormName(currentRecord.form_id)}-${actCommon.getUserFieldValue(currentRecord.owner, \"nickName\")}-${actDate.formatDate(currentRecord.create_time)}";
            }
        }
        // 执行表达式
        ScriptEngineManager factory = new ScriptEngineManager();
        ScriptEngine engine = factory.getEngineByName("juel");
        Bindings bindings = engine.createBindings();
        bindings.putAll(variables);
        Arrays.stream(applicationContext.getBeanDefinitionNames()).forEach(beanName -> {
            bindings.put(beanName, applicationContext.getBean(beanName));
        });
        try {
            String eval = (String) engine.eval(flowTitle, bindings);
            return eval;
        } catch (Exception e) {
            String message = StrUtil.format("流程配置错误，流程标题表达式：{}， 错误: {}", flowTitle, e.getMessage());
            throw new ServiceException(message);
        }
    }

    private String submitFlow(String businessKey, String objDataType, Long operatorId, Map<String, Object> variables, Map<String, List<String>> selfSelectVariables) {
        WfFlowStartDto wfFlowStartDto = new WfFlowStartDto();
        wfFlowStartDto.setBusinessKey(businessKey);
        wfFlowStartDto.setObjDataType(objDataType);
        wfFlowStartDto.setApplicantId(operatorId);
        wfFlowStartDto.setVariables(variables);
        wfFlowStartDto.setSelfSelectVariables(selfSelectVariables);
        String flowTitle = this.getFlowTitle(objDataType, variables);
        wfFlowStartDto.setTitle(flowTitle);

        return wfRuntimeService.start(wfFlowStartDto);
    }

    private String saveOrUpdate(Map<String, Object> jdbcData, FormTable formTable) {
        String id = (String) jdbcData.get("id");
        if (StrUtil.isBlank(id)) {
            id = IdWorker.getIdStr();
            jdbcData.put("id", id);
            if (FormTableTypeEnum.MAIN.getType().equals(formTable.getType())) {
                // 初始化 flow_status
                jdbcData.put("flow_status", FlowStatusEnum.draft.name());
            }
            dynamicTableService.doActionToDynamicTable(formTable.getTableName(), FormActionTypeEnum.INSERT.getType(), jdbcData);
        } else {
            dynamicTableService.doActionToDynamicTable(formTable.getTableName(), FormActionTypeEnum.UPDATE.getType(), jdbcData);
        }
        return id;
    }

    /**
     * 排除子表字段
     *
     * @param formTables 表单表
     * @param data       数据
     * @return
     */
    private Map<String, Object> excludeChildTableFields(List<FormTable> formTables, Map<String, Object> data) {
        Map<String, Object> result = new HashMap<>();
        for (Map.Entry<String, Object> entry : data.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            if (CollUtil.isNotEmpty(formTables)) {
                boolean isChildTable = formTables.stream().anyMatch(item -> item.getTableName().equals(key));
                if (!isChildTable) {
                    result.put(key, value);
                }
            } else {
                result.put(key, value);
            }
        }
        return result;
    }

    private String genCacheKey(String formId) {
        return "form:no:" + formId;
    }

    private String genSerialNumber(String formId, String prefix) {
        String key = this.genCacheKey(formId);
        Long num = cacheService.incr(key);
        return prefix + DateUtil.format(new Date(), "yyyyMMdd") + StrUtil.padPre(num + "", 4, "0");

    }


    private void initAutoData4Field(List<FormTableField> formTableFields, Map<String, Object> data) {
        for (FormTableField formTableField : formTableFields) {
            if ("SerialNumber".equals(formTableField.getComponent())) {
                Object value = data.get(formTableField.getName());
                if (Objects.isNull(value)) {
                    JSONObject optionsJson = formTableField.getOptionsJson();
                    String startWithCode = optionsJson.getStr("startWithCode");
                    String serialNumber = this.genSerialNumber(formTableField.getFormId(), startWithCode);
                    data.put(formTableField.getName(), serialNumber);
                }
            }
        }
    }

    public Map<String, Object> getFormJdbcDataFromFront(String formId, Map<String, Object> data) {
        List<FormTable> formTables = formTableService.list(new LambdaQueryWrapper<FormTable>().eq(FormTable::getFormId, formId));
        FormTable mainTable = formTables.stream().filter(item -> item.getType().equals(FormTableTypeEnum.MAIN.getType())).findFirst().get();
        List<FormTableField> tableFields = formTableFieldService.list(new LambdaQueryWrapper<FormTableField>().eq(FormTableField::getFormId, formId));
        Map<String, List<FormTableField>> fieldGroup = tableFields.stream().collect(Collectors.groupingBy(FormTableField::getFormTableId));
        // 主表数据
        Map<String, Object> jdbcData = this.excludeChildTableFields(formTables, data);
        jdbcData = UiComponentFactory.transformData2Jdbc(tableFields, jdbcData);

        // 从表数据
        List<FormTable> childTables = formTables.stream().filter(item -> item.getType().equals(FormTableTypeEnum.CHILD.getType())).toList();
        for (FormTable childTable : childTables) {
            Object childDataList = data.get(childTable.getTableName());
            List<FormTableField> formTableFields = fieldGroup.get(childTable.getId());
            if (Objects.nonNull(childDataList)) {
                List<Map<String, Object>> list = (List<Map<String, Object>>) childDataList;
                List<Map<String, Object>> jdbcChildDataList = new ArrayList<>();
                for (Map<String, Object> item : list) {
                    Map<String, Object> childJdbcData = UiComponentFactory.transformData2Jdbc(formTableFields, item);
                    if (Objects.nonNull(jdbcData.get("id"))) {
                        childJdbcData.put(mainTable.getForeignKey(), jdbcData.get("id"));
                    }
                    jdbcChildDataList.add(childJdbcData);
                }
                jdbcData.put(childTable.getTableName(), jdbcChildDataList);
            }
        }

        return jdbcData;
    }

    public String updateRowFormData(String formId, Map<String, Object> data, boolean transform) {
        List<FormTable> formTables = formTableService.list(new LambdaQueryWrapper<FormTable>().eq(FormTable::getFormId, formId));
        FormTable mainTable = formTables.stream().filter(item -> item.getType().equals(FormTableTypeEnum.MAIN.getType())).findFirst().get();
        List<FormTableField> tableFields = formTableFieldService.list(new LambdaQueryWrapper<FormTableField>().eq(FormTableField::getFormId, formId));
        Map<String, List<FormTableField>> fieldGroup = tableFields.stream().collect(Collectors.groupingBy(FormTableField::getFormTableId));
        Map<String, Object> jdbcData = this.excludeChildTableFields(formTables, data);
        if (transform) {
            jdbcData = UiComponentFactory.transformData2Jdbc(tableFields, jdbcData);
        }
        // 主表数据
        String mainId = this.saveOrUpdate(jdbcData, mainTable);
        // 从表数据
        List<FormTable> childTables = formTables.stream().filter(item -> item.getType().equals(FormTableTypeEnum.CHILD.getType())).toList();
        for (FormTable childTable : childTables) {
            Object childDataList = data.get(childTable.getTableName());
            List<FormTableField> formTableFields = fieldGroup.get(childTable.getId());
            if (Objects.nonNull(childDataList)) {
                List<Map<String, Object>> list = (List<Map<String, Object>>) childDataList;
                for (Map<String, Object> item : list) {
                    Map<String, Object> childJdbcData = item;
                    if (transform) {
                        childJdbcData = UiComponentFactory.transformData2Jdbc(formTableFields, item);
                    }
                    childJdbcData.put(mainTable.getForeignKey(), mainId);
                    // TODO 待优化
                    this.saveOrUpdate(childJdbcData, childTable);
                }
            }
        }
        return mainId;
    }

    /**
     * 保存表单数据
     * @param formId 表单Id
     * @param data 表单数据
     * @param transform 是否需要转换
     * @return
     */
    public String saveFormData(String formId, Map<String, Object> data, boolean transform) {
        // 拿到表和字段信息
        List<FormTable> formTables = formTableService.list(new LambdaQueryWrapper<FormTable>().eq(FormTable::getFormId, formId));
        FormTable mainTable = formTables.stream().filter(item -> item.getType().equals(FormTableTypeEnum.MAIN.getType())).findFirst().get();
        List<FormTableField> tableFields = formTableFieldService.list(new LambdaQueryWrapper<FormTableField>().eq(FormTableField::getFormId, formId));
        Map<String, List<FormTableField>> fieldGroup = tableFields.stream().collect(Collectors.groupingBy(FormTableField::getFormTableId));
        // 保存主表
        Map<String, Object> jdbcData = this.excludeChildTableFields(formTables, data);
        if (transform) {
            jdbcData = UiComponentFactory.transformData2Jdbc(tableFields, jdbcData);
        }

        this.initAutoData4Field(tableFields, jdbcData);

        jdbcData = this.fillBaseFieldData(formId, jdbcData);

        String mainId = this.saveOrUpdate(jdbcData, mainTable);

        // 保存从表
        List<FormTable> childTables = formTables.stream().filter(item -> item.getType().equals(FormTableTypeEnum.CHILD.getType())).toList();
        // 查询子表数据
        for (FormTable childTable : childTables) {
            Object childDataList = data.get(childTable.getTableName());
            List<FormTableField> formTableFields = fieldGroup.get(childTable.getId());
            // 先删除子表的所有数据
            List<Map<String, Object>> childList = dynamicTableService.getDynamicTableDataListByMainTableId(mainTable.getTableName(), childTable.getTableName(), mainId);
            if (CollUtil.isNotEmpty(childList)) {
                dynamicTableService.batchDeleteDynamicTableData(childTable.getTableName(), childList.stream().map(item -> item.get("id").toString()).collect(Collectors.toList()));
            }
            if (Objects.nonNull(childDataList)) {
                List<Map<String, Object>> list = (List<Map<String, Object>>) childDataList;
                for (Map<String, Object> item : list) {
                    Map<String, Object> childJdbcData = item;
                    if (transform) {
                        childJdbcData = UiComponentFactory.transformData2Jdbc(formTableFields, item);
                    }
                    childJdbcData.put(mainTable.getForeignKey(), mainId);
                    childJdbcData = this.fillBaseFieldData(formId, childJdbcData);
                    childJdbcData.put("id", null);
                    // TODO 待优化
                    this.saveOrUpdate(childJdbcData, childTable);
                }
            }
        }
        return mainId;
    }

    public void submitApproval(String formId, Long operatorId, String mainId, Map<String, List<String>> selfSelectVariables) {
        Map<String, Object> variables = new HashMap<>();
        variables.put("formId", formId);
        variables.put("applicantId", operatorId);
        UserTemplateInterface userService = ApiRegistry.getApi(UserTemplateInterface.class);
        FormUser sysUser = userService.getUserById(operatorId);
        if (sysUser != null) {
            variables.put("applicantDeptId", sysUser.getDeptId());
        }
        Map<String, Object> formData = this.getFormData(formId, mainId);
        variables.put("currentRecord", formData);
        this.submitFlow(mainId, formId, operatorId, variables, selfSelectVariables);
        Form form = formService.getById(formId);
        List<String> approverIds = wfRuntimeService.getApproverIdsByBusinessKey(mainId);
        String owner = (String) formData.get("owner");
        SimpleObjCacheDto cacheUser = cacheUtil.findUserById(Convert.toLong(owner));
        JSONObject params = new JSONObject();
        params.set("formId", formId);
        params.set("formName", form.getName());
        params.set("businessKey", mainId);
        params.set("ownerName", cacheUser.getName());
        MessageTemplateInterface bean = ApiRegistry.getApi(MessageTemplateInterface.class);
        SendMessageParam sendMessageParam = new SendMessageParam();
        sendMessageParam.setReceiverUserIds(approverIds.stream().map(com.rcszh.base.common.core.text.Convert::toLong).collect(Collectors.toList()));
        sendMessageParam.setParams(params);
        bean.sendWaitMessage(sendMessageParam);
    }

    @Transactional(rollbackFor = Exception.class)
    public String saveFormData(ModelingDataReqDto modelingDataReqDto) {
        String mainId = this.saveFormData(modelingDataReqDto.getFormId(), modelingDataReqDto.getData(), true);
        if ("submit".equals(modelingDataReqDto.getAction())) {
            this.submitApproval(modelingDataReqDto.getFormId(), modelingDataReqDto.getOperatorId(), mainId, modelingDataReqDto.getSelfSelectVariables());
        }
        return mainId;
    }

    public void checkOrderKeyLegality(String boeNo, String key) {
        OrderKeyInfoDto orderKeyInfoDto = OrderUtil.parseOrderKey(key);
        if (Objects.isNull(orderKeyInfoDto)) {
            throw new ServiceException("非法请求", 1001);
        }
        if ("user".equals(orderKeyInfoDto.getType())
                && boeNo.equals(orderKeyInfoDto.getOrderNo())
                && SecurityUtils.getUserIdStr().equals(orderKeyInfoDto.getValue())) {
            return;
        }
        if ("msg".equals(orderKeyInfoDto.getType()) && boeNo.equals(orderKeyInfoDto.getOrderNo())) {
            MessageTemplateInterface bean = ApiRegistry.getApi(MessageTemplateInterface.class);
            FormMsgLog msgLog = bean.getFormMsgLogById(orderKeyInfoDto.getValue());
            if (Objects.nonNull(msgLog)) {
                JSONObject params = JSONUtil.parseObj(msgLog.getParam());
                List<Long> userIds = params.getJSONArray("receiverUserIds").stream().map(com.rcszh.base.common.core.text.Convert::toLong).toList();
                if (userIds.contains(SecurityUtils.getUserId())) {
                    return;
                }
            }
        }

        throw new ServiceException("非法请求", 1001);
    }


    public ModelingDataViewResDto getFormDataViewInfo(ModelingDataViewReqDto modelingDataViewReqDto) {
        // TODO 初版，只查询主表
        List<FormTable> formTables = formTableService.list(new LambdaQueryWrapper<FormTable>()
                .eq(FormTable::getFormId, modelingDataViewReqDto.getFormId())
        );
        List<FormTableField> tableFields = formTableFieldService.list(new LambdaQueryWrapper<FormTableField>()
                .eq(FormTableField::getFormId, modelingDataViewReqDto.getFormId())
        );

        Map<String, Object> mainData = this.getFormData(formTables, tableFields, modelingDataViewReqDto.getRowId(), true);
        ModelingDataViewResDto result = new ModelingDataViewResDto();

        String flowStatus = null;
        if (Objects.nonNull(mainData.get("flow_status"))) {
            Map<String, Object> flowStatusObj = (Map<String, Object>) mainData.get("flow_status");
            flowStatus = (String) flowStatusObj.get("id");
            result.setFlowStatus(flowStatusObj);
        }

        // 默认可以重新提交
        boolean resubmit = true;
        WfTemplate flowTemplate = null;
        List<WfDataPermItemVo> permItemVos = new ArrayList<>();
        ModelingOperationAuthDto operationAuthDto = new ModelingOperationAuthDto();
        WfHiProcessInstanceVo wfHiProcessInstanceVo = wfRuntimeService.getLatestInstanceByBusinessKey(mainData.get("id").toString());
        if (wfHiProcessInstanceVo != null) {
            result.setFlowInstanceId(wfHiProcessInstanceVo.getId());
            permItemVos = this.getCurrentUserDataPerms(wfHiProcessInstanceVo, SecurityUtils.getUserIdStr());
            if (Arrays.asList(FlowStatusEnum.running.name(), FlowStatusEnum.passed.name()).contains(flowStatus)) {
                flowTemplate = wfTemplateService.getByProcessDefinitionId(wfHiProcessInstanceVo.getProcessDefinitionId());
                resubmit = false;
                operationAuthDto.setCanSave(false);
                operationAuthDto.setCanSubmit(false);
            }
        }
        if (Objects.isNull(flowTemplate)) {
            flowTemplate = wfRuntimeService.getCurrentFlowTemplate(modelingDataViewReqDto.getFormId(), false);
        }

        if (flowTemplate != null) {
            result.setFlowTemplateId(flowTemplate.getTemplateId());

            Map<String, Object> processProperties = wfRuntimeService.getProcessProperties(flowTemplate.getActDefinitionId());
            if (Objects.nonNull(processProperties.get("resubmit")) && Constants.N.equals(processProperties.get("resubmit"))) {
                resubmit = false;
                operationAuthDto.setCanSave(false);
                operationAuthDto.setCanSubmit(false);
            }
        }
        result.setFormId(modelingDataViewReqDto.getFormId());
        result.setFormData(mainData);

        Map<String, Object> fieldAuths = this.getFieldsAuth2(formTables, tableFields, flowStatus, permItemVos);
        result.setFieldsAuth(fieldAuths);
        result.setOperationAuth(operationAuthDto);
        return result;
    }

    /**
     * 如果流程未结束，查询数据权限设置
     *
     * @param wfHiProcessInstanceVo
     * @return
     */
    private List<WfDataPermItemVo> getCurrentUserDataPerms(WfHiProcessInstanceVo wfHiProcessInstanceVo, String userId) {
        List<WfDataPermItemVo> permItemVos = new ArrayList<>();
        if (Objects.isNull(wfHiProcessInstanceVo.getEndTime())) {
            Map<String, Object> nodeProperties = wfRuntimeService.getCurrentFlowNodePropertiesByUserId(wfHiProcessInstanceVo.getId(), userId);
            if (Constants.Y.equals(nodeProperties.get("enabledDataPerm"))) {
                String dataPerm = (String) nodeProperties.get("dataPerm");
                if (Objects.nonNull(dataPerm)) {
                    List<Object> dataPermList = JSONUtil.parseArray(dataPerm);
                    for (Object d : dataPermList) {
                        WfDataPermItemVo dataPermItemVo = BeanUtil.toBean(d, WfDataPermItemVo.class);
                        permItemVos.add(dataPermItemVo);
                    }

                }
            }
        }
        return permItemVos;
    }

    public Map<String, Object> getFormData(String formId, String rowId) {
        List<FormTable> formTables = formTableService.list(new LambdaQueryWrapper<FormTable>()
                .eq(FormTable::getFormId, formId)
        );
        List<FormTableField> tableFields = formTableFieldService.list(new LambdaQueryWrapper<FormTableField>()
                .eq(FormTableField::getFormId, rowId)
        );
        return this.getFormData(formTables, tableFields, rowId, false);
    }

    private Map<String, Object> getFormData(List<FormTable> formTables, List<FormTableField> tableFields, String rowId, boolean transform) {
        Map<String, Object> result;
        FormTable mainTable = formTables.stream().filter(item -> item.getType().equals(FormTableTypeEnum.MAIN.getType())).findFirst().get();
        Map<String, Object> mainData = dynamicTableService.getDynamicTableDataById(mainTable.getTableName(), rowId);
        if (Objects.isNull(mainData)) {
            throw new RuntimeException("数据不存在");
        }

        Map<String, List<FormTableField>> fieldGroup = tableFields.stream().collect(Collectors.groupingBy(FormTableField::getFormTableId));
        if (transform) {
            result = UiComponentFactory.transformData2Component(fieldGroup.get(mainTable.getId()), mainData);
        } else {
            result = mainData;
        }

        List<FormTable> childTables = formTables.stream().filter(item -> FormTableTypeEnum.CHILD.getType().equals(item.getType())).toList();
        for (FormTable childTable : childTables) {
            if (FormTableTypeEnum.MAIN.getType().equals(childTable.getType())) {
                continue;
            }
            List<Map<String, Object>> dbRows = dynamicTableService.getDynamicTableDataListByMainTableId(mainTable.getTableName(), childTable.getTableName(), mainData.get("id"));
            if (transform) {
                UiComponentFactory.transformDataList2Component(fieldGroup.get(childTable.getId()), dbRows);
            }
            result.put(childTable.getTableName(), dbRows);
        }
        return result;
    }

    /**
     * 获取字段权限
     *  单据状态 draft: 按表单配置
     *  单据状态 running: 如果有配置权限，按流程节点配置；否则按表单配置（但全部不可编辑）
     *  单据状态 passed：按表单配置（但全部不可编辑）
     *  单据状态 refused: 如果流程中配置了不可重新提交，按表单配置（但全部不可编辑）；否则按表单配置
     *  单据状态 canceled: 如果流程中配置了不可重新提交，按表单配置（但全部不可编辑）；否则按表单配置
     * TODO 后续后端能解析动态表达式后，改回使用这个方法；（有后端提供是否显示、编辑、必填）
     * 其中：如果按表单配置，则，不返回字段权限配置；
     */
    private Map<String, Object> getFieldsAuth(List<FormTable> formTables, List<FormTableField> tableFields, String flowStatus, boolean resubmit, List<WfDataPermItemVo> permItemVos) {
        // 草稿、驳回、撤回状态下，都可以编辑；但是如果流程中配置了不允许重新提交，则不允许编辑
        boolean canEdit = Objects.isNull(flowStatus) || Arrays.asList(FlowStatusEnum.draft.name(), FlowStatusEnum.refused.name(), FlowStatusEnum.canceled.name()).contains(flowStatus);
        if (Objects.nonNull(flowStatus)) {
            if (Arrays.asList(FlowStatusEnum.running.name(), FlowStatusEnum.passed.name()).contains(flowStatus)) {
                canEdit = false;
            } else if (!resubmit) {
                canEdit = false;
            }
        }

        Map<String, Object> fieldsAuth = new HashMap<>();
        // 流程节点中未开启数据权限，不返回权限配置；使用表单上的配置
        if (CollUtil.isEmpty(permItemVos) && canEdit) {
            return fieldsAuth;
        }
        Map<String, List<FormTableField>> fieldGroup = tableFields.stream().collect(Collectors.groupingBy(FormTableField::getFormTableId));

        Map<String, WfDataPermItemVo> permItemVoMap = permItemVos.stream().collect(Collectors.toMap(WfDataPermItemVo::getFiledCode, t -> t, (k1, k2) -> k1));
        for (FormTable formTable : formTables) {
            ModelingTableAuth tableAuth = new ModelingTableAuth();
            List<FormTableField> formTableFieldList = fieldGroup.get(formTable.getId());
            // 排查布局组件
            formTableFieldList = formTableFieldList.stream().filter(t -> StrUtil.isNotBlank(t.getJdbcType())).toList();
            Map<String, ModelingFieldAuth> fieldAuthMap = new HashMap<>();
            tableAuth.setFields(fieldAuthMap);
            if (FormTableTypeEnum.CHILD.getType().equals(formTable.getType())) {
                if (!canEdit) {
                    WfDataPermItemVo wfDataPermItemVo = permItemVoMap.get(formTable.getTableName());
                    if (Objects.nonNull(wfDataPermItemVo)) {
                        List<WfDataPermItemVo> children = wfDataPermItemVo.getChildren();
                        for (WfDataPermItemVo child : children) {
                            switch (child.getFiledCode()) {
                                case "canAddRow":
                                    tableAuth.setCanAddRow(child.getEditable());
                                    break;
                                case "canDeleteRow":
                                    tableAuth.setCanDeleteRow(child.getEditable());
                                    break;
                                case "canExport":
                                    tableAuth.setCanExport(child.getEditable());
                                    break;
                                case "canImport":
                                    tableAuth.setCanImport(child.getEditable());
                                    break;
                            }
                        }
                    } else {
                        tableAuth.setCanAddRow(false);
                        tableAuth.setCanDeleteRow(false);
                        tableAuth.setCanExport(false);
                        tableAuth.setCanImport(false);
                    }
                } else {
                    tableAuth.setCanAddRow(true);
                    tableAuth.setCanDeleteRow(true);
                    tableAuth.setCanExport(true);
                    tableAuth.setCanImport(true);
                }

                // 从表字段，从表暂时没有精确到字段的权限控制
                for (FormTableField formTableField : formTableFieldList) {
                    JSONObject optionsJson = formTableField.getOptionsJson();
                    ModelingFieldAuth fieldAuth = new ModelingFieldAuth();
                    if (!canEdit) {
                        fieldAuth.setEditable(false);
                        fieldAuth.setVisible(!optionsJson.getBool("hidden", false));
                        fieldAuth.setRequired(optionsJson.getBool("required", false));
                    } else {
                        // 字段原有属性
                        JSONObject propsJson = formTableField.getPropsJson();
                        fieldAuth.setEditable(!propsJson.getBool("disabled", false));
                        fieldAuth.setVisible(!optionsJson.getBool("hidden", false));
                        fieldAuth.setRequired(optionsJson.getBool("required", false));
                    }
                    fieldAuthMap.put(formTableField.getName(), fieldAuth);
                }
            } else {
                // 主表字段
                for (FormTableField formTableField : formTableFieldList) {
                    JSONObject optionsJson = formTableField.getOptionsJson();
                    ModelingFieldAuth fieldAuth = new ModelingFieldAuth();
                    if (!canEdit) {
                        WfDataPermItemVo wfDataPermItemVo = permItemVoMap.get(formTableField.getName());
                        if (Objects.nonNull(wfDataPermItemVo)) {
                            fieldAuth.setEditable(wfDataPermItemVo.getEditable());
                            fieldAuth.setVisible(wfDataPermItemVo.getVisible());
                            fieldAuth.setRequired(wfDataPermItemVo.getRequired());
                        } else {
                            fieldAuth.setEditable(false);
                            fieldAuth.setVisible(!optionsJson.getBool("hidden", false));
                            fieldAuth.setRequired(optionsJson.getBool("required", false));
                        }
                    } else {
                        // 字段原有属性
                        JSONObject propsJson = formTableField.getPropsJson();
                        fieldAuth.setEditable(!propsJson.getBool("disabled", false));
                        fieldAuth.setVisible(!optionsJson.getBool("hidden", false));
                        fieldAuth.setRequired(optionsJson.getBool("required", false));
                    }

                    fieldAuthMap.put(formTableField.getName(), fieldAuth);
                }
            }

            fieldsAuth.put(formTable.getTableName(), tableAuth);
        }
        return fieldsAuth;
    }

    /**
     * 单据状态 running: 如果有配置权限，返回按流程节点配置；
     * 其他情况，交给前端处理
     */
    private Map<String, Object> getFieldsAuth2(List<FormTable> formTables, List<FormTableField> tableFields, String flowStatus, List<WfDataPermItemVo> permItemVos) {
        Map<String, Object> fieldsAuth = new HashMap<>();
        if (!FlowStatusEnum.running.name().equals(flowStatus) || CollUtil.isEmpty(permItemVos)) {
            return fieldsAuth;
        }


        Map<String, List<FormTableField>> fieldGroup = tableFields.stream().collect(Collectors.groupingBy(FormTableField::getFormTableId));

        Map<String, WfDataPermItemVo> permItemVoMap = permItemVos.stream().collect(Collectors.toMap(WfDataPermItemVo::getFiledCode, t -> t, (k1, k2) -> k1));
        for (FormTable formTable : formTables) {
            ModelingTableAuth tableAuth = new ModelingTableAuth();
            List<FormTableField> formTableFieldList = fieldGroup.get(formTable.getId());
            // 排查布局组件
            formTableFieldList = formTableFieldList.stream().filter(t -> StrUtil.isNotBlank(t.getJdbcType())).toList();
            Map<String, ModelingFieldAuth> fieldAuthMap = new HashMap<>();
            tableAuth.setFields(fieldAuthMap);
            if (FormTableTypeEnum.CHILD.getType().equals(formTable.getType())) {
                WfDataPermItemVo wfDataPermItemVo = permItemVoMap.get(formTable.getTableName());
                if (Objects.nonNull(wfDataPermItemVo)) {
                    List<WfDataPermItemVo> children = wfDataPermItemVo.getChildren();
                    for (WfDataPermItemVo child : children) {
                        switch (child.getFiledCode()) {
                            case "canAddRow":
                                tableAuth.setCanAddRow(child.getEditable());
                                break;
                            case "canDeleteRow":
                                tableAuth.setCanDeleteRow(child.getEditable());
                                break;
                            case "canExport":
                                tableAuth.setCanExport(child.getEditable());
                                break;
                            case "canImport":
                                tableAuth.setCanImport(child.getEditable());
                                break;
                        }
                    }
                } else {
                    tableAuth.setCanAddRow(false);
                    tableAuth.setCanDeleteRow(false);
                    tableAuth.setCanExport(false);
                    tableAuth.setCanImport(false);
                }
            } else {
                // 主表字段
                for (FormTableField formTableField : formTableFieldList) {
                    JSONObject optionsJson = formTableField.getOptionsJson();
                    ModelingFieldAuth fieldAuth = new ModelingFieldAuth();
                    WfDataPermItemVo wfDataPermItemVo = permItemVoMap.get(formTableField.getName());
                    if (Objects.nonNull(wfDataPermItemVo)) {
                        fieldAuth.setEditable(wfDataPermItemVo.getEditable());
                        fieldAuth.setVisible(wfDataPermItemVo.getVisible());
                        fieldAuth.setRequired(wfDataPermItemVo.getRequired());
                    } else {
                        fieldAuth.setEditable(false);
                        fieldAuth.setVisible(!optionsJson.getBool("hidden", false));
                        fieldAuth.setRequired(optionsJson.getBool("required", false));
                    }
                    fieldAuthMap.put(formTableField.getName(), fieldAuth);
                }
            }

            fieldsAuth.put(formTable.getTableName(), tableAuth);
        }
        return fieldsAuth;
    }


    /**
     * 查询列表数据
     *
     * @param reqDto 查询条件
     */
    public PageResult<Map<String, Object>> pageFormData(ModelingDataListReqDto reqDto) {
        ViewForm viewForm = getViewForm(reqDto.getFormId(), reqDto.getViewFormId());
        // TODO 子表数据查询,子表的数据可以用懒加载的方式，没必要一开始就查子表
        // 通过表单查询表单数据库表
        List<FormTable> formTables = formTableService.list(new LambdaQueryWrapper<FormTable>()
                .eq(FormTable::getFormId, viewForm.getFormId())
        );
        // 主表
        FormTable mainTable = formTables.stream().filter(item -> item.getType().equals(FormTableTypeEnum.MAIN.getType())).findFirst().orElseThrow();

        SelectConditionGroupDto conditionGroup = viewFormService.buildSelectConditionGroup(viewForm, reqDto.getParams(), reqDto.getConditions(), reqDto.getBaseDataFilter());
        List<SelectSortDto> selectSorts = viewFormService.buildSelectSort(viewForm, reqDto.getSorts());
        // 查询信息
        PageResult<Map<String, Object>> result = dynamicTableService.getRealTableDataPage(
                mainTable.getTableName(),
                reqDto.getPageNum(),
                reqDto.getPageSize(),
                conditionGroup,
                selectSorts
        );
        // 查询总数
        List<FormTableField> tableFields = formTableFieldService.list(new LambdaQueryWrapper<FormTableField>()
                .eq(FormTableField::getFormId, reqDto.getFormId())
                .eq(FormTableField::getFormTableId, mainTable.getId())
        );
        // 组件工具类
        List<Map<String, Object>> rows = result.getRows();

        for (Map<String, Object> row : rows) {
            row.put("key", OrderUtil.generateOrderKey((String) row.get("id")));
        }

        UiComponentFactory.transformDataList2Component(tableFields, rows);
        // TODO 子表数据查询
        return result;
    }

    private ViewForm getViewForm(String formId, String viewFormId) {
        ViewForm viewForm = null;
        if (StrUtil.isNotBlank(viewFormId)) {
            viewForm = viewFormService.getById(viewFormId);
        } else if (StrUtil.isNotBlank(formId)) {
            // 取默认列表视图
            viewForm = viewFormService.getOne(new LambdaQueryWrapper<ViewForm>()
                    .eq(ViewForm::getFormId, formId)
                    .eq(ViewForm::getSystemType, "builtin")
                    .eq(ViewForm::getType, ViewFormTypeEnum.LIST.getType())
                    .last("limit 1"));
        }
        if (Objects.isNull(viewForm)) {
            throw new ServiceException("表单视图不存在");
        }
        return viewForm;
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteFormDataByIds(String formId, List<String> rowIds) {
        List<FormTable> formTables = formTableService.list(new LambdaQueryWrapper<FormTable>()
                .eq(FormTable::getFormId, formId)
        );
        FormTable mainTable = formTables.stream().filter(item -> item.getType().equals(FormTableTypeEnum.MAIN.getType())).findFirst().get();

        WfTemplate flowTemplate = wfRuntimeService.getCurrentFlowTemplate(formId, false);

        // 有配置流程的，需要校验；同时删除流程记录
        if (Objects.nonNull(flowTemplate)) {
            // 校验不允许删除的单据
            SelectConditionGroupDto conditionGroupDto = new SelectConditionGroupDto();
            conditionGroupDto.setNestedType("$and");
            SelectConditionDto selectConditionDto = new SelectConditionDto();
            selectConditionDto.setField("id");
            selectConditionDto.setSelectType(ViewFormListSelectTypeEnum.IN);
            selectConditionDto.setValue(rowIds);
            conditionGroupDto.setConditions(Collections.singletonList(selectConditionDto));
            List<Map<String, Object>> dataList = dynamicTableService.getRealTableDataList(mainTable.getTableName(), conditionGroupDto, null);
            for (Map<String, Object> one : dataList) {
                if (FlowStatusEnum.running.name().equals(one.get("flow_status"))) {
                    throw new RuntimeException(StrUtil.format("数据[{}]审批中的不允许删除", one.get("id")));
                }
                if (FlowStatusEnum.passed.name().equals(one.get("flow_status"))) {
                    throw new RuntimeException(StrUtil.format("数据[{}]已审批的不允许删除", one.get("id")));
                }
                if (FlowStatusEnum.refused.name().equals(one.get("flow_status"))) {
                    throw new RuntimeException(StrUtil.format("数据[{}]已驳回的不允许删除", one.get("id")));
                }
            }
            // 删除对应审批流程
            wfRuntimeService.deleteProcessInstanceByBusinessKeys(rowIds);
        }

        // 删除数据
        Map<String, Object> map = new HashMap<>();
        map.put("ids", rowIds);
        dynamicTableService.doActionToDynamicTable(mainTable.getTableName(), FormActionTypeEnum.BATCH_DELETE.getType(), map);
        // 删除明细表数据
        List<FormTable> childTables = formTables.stream().filter(i -> i.getType().equals(FormTableTypeEnum.CHILD.getType())).toList();
        for (FormTable formTable : childTables) {
            dynamicTableService.batchDeleteChildData(mainTable.getTableName(), formTable.getTableName(), rowIds);
        }
    }

    /**
     * 动态导出
     */
    public void exportListData(HttpServletResponse response, ExportExcelParam request) {
        String type = request.getType();
        String formId = request.getFormId();
        List<ExportExcelParam.ExportFiled> exportFiledList = request.getExportFiledList();
        // 拿到要导出的字段
        // {main:[],child:[]}
        Map<String, List<ExportExcelParam.ExportFiled>> collect = exportFiledList.stream().collect(Collectors.groupingBy(ExportExcelParam.ExportFiled::getType));
        List<FormTable> formTables = formTableService.getTableByFormId(formId);
        FormTable mainTable = formTables.stream().filter(i -> i.getType().equals(FormTableTypeEnum.MAIN.getType())).findFirst().orElseThrow();
        Map<String, FormTable> tableMap = formTables.stream().collect(Collectors.toMap(FormTable::getId, i -> i));
        EasyExcelExportUtil util = new EasyExcelExportUtil();
        // 查询表单所有的字段，方便后续code->name的映射
        List<FormTableFieldDto> formAllFields = formTableFieldService.getFieldByFormIdToList(formId);
        Map<String,FormTableFieldDto> fieldNameMap = formAllFields.stream().collect(Collectors.toMap(i -> i.getFormTableId()+"_"+i.getName(),i -> i));
        if (type.equals(ExportExcelParamTypeEnum.ALL.getType())) {
            // 查询所有数据
            for (String key : collect.keySet()) {
                // 遍历所有的表
                String tableName;
                if (key.equals(FormTableTypeEnum.MAIN.getType())) {
                    // 查询信息
                    tableName = mainTable.getTableName();
                    List<String> selectColumn = collect.get(key).stream().map(ExportExcelParam.ExportFiled::getField).collect(Collectors.toList());
                    // 复制一份
//                    List<String> selectColumnCopy = new ArrayList<>(selectColumn);
                    selectColumn.add(InitFieldTypeEnum.ID.getFieldName());
                    List<Map<String, Object>> list = dynamicTableService.getRealTableDataList(tableName,selectColumn, null, null);
                    // 将字段code转为name
                    List<FormTableFieldDto> selectNameColumn = selectColumn.stream().map(i -> fieldNameMap.get(mainTable.getId() + "_" + i)).collect(Collectors.toList());
                    util.mainTableColumn(selectNameColumn);
                    util.mainTableData(list);
                }else {
                    // 子表
                    List<ExportExcelParam.ExportFiled> subExportField = collect.get(key);
                    for (ExportExcelParam.ExportFiled child : subExportField) {
                        String formTableId = child.getField();
                        // 拿着数据行id查询子表
                        FormTable formTable = tableMap.get(formTableId);
                        util.subColumnMapping(formTable);
                        tableName = formTable.getTableName();
                        // 查询明细表字段
                        List<FormTableFieldDto> formTableFieldDtoList = formTableFieldService.getFieldByTable(formTableId);
                        FormTableFieldDto IdField = formTableFieldDtoList.stream().filter(i -> i.getName().equals(InitFieldTypeEnum.ID.getFieldName())).findFirst().orElseThrow();
                        List<FormTableFieldDto> field = formTableFieldDtoList
                                .stream()
                                // 只要可见的
                                .filter(i -> Constants.Y.equals(i.getFtfDisplay()))
                                // 过滤明细表字段
                                .filter(i -> !i.getComponent().equals(UiComponentTypeEnum.FORM_LIST.getType()))
                                .collect(Collectors.toList());
                        field.add(IdField);
                        util.subColumn(tableName,field);
                        List<Map<String, Object>> list = dynamicTableService.getRealTableDataList(tableName,null, null, null);
                        String mainTableName = mainTable.getTableName();
                        util.subData(mainTableName,tableName,list);
                    }
                }
            }
            try {
                util.response(response).exportExcel();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        } else if (type.equals(ExportExcelParamTypeEnum.PART.getType())) {

        } else {
            throw new ServiceException("不支持的导出类型");
        }
    }

    /**
     * 删除垃圾数据
     * 单据已经删除，但是审批流还存在
     */
    public void deleteRubbishData() {
        List<WfTemplate> wfTemplates = wfTemplateService.list(new LambdaQueryWrapper<WfTemplate>()
                .eq(WfTemplate::getPublishStatus, PublishStatus.PUBLISHED.getValue())
        );
        List<WfApplyOrg> wfApplyOrgs = wfApplyOrgService.list(new LambdaQueryWrapper<WfApplyOrg>()
                .eq(WfApplyOrg::getOwnerType, OwnerType.TEMPLATE.getValue())
        );
        Map<String, WfApplyOrg> applyOrgMap = wfApplyOrgs.stream().collect(Collectors.toMap(WfApplyOrg::getOwnerId, t -> t, (t1, t2) -> t1));
        List<FormTable> formTables = formTableService.list(new LambdaQueryWrapper<FormTable>()
                .eq(FormTable::getType, FormTableTypeEnum.MAIN.getType())
        );
        Map<String, FormTable> formTableMap = formTables.stream().collect(Collectors.toMap(FormTable::getFormId, t -> t, (t1, t2) -> t1));

        for (WfTemplate wfTemplate : wfTemplates) {
            WfApplyOrg wfApplyOrg = applyOrgMap.get(wfTemplate.getTemplateId());
            if (Objects.isNull(wfApplyOrg)) {
                continue;
            }
            String formId = wfApplyOrg.getObjDataType();
            FormTable formTable = formTableMap.get(formId);
            if (Objects.isNull(formTable)) {
                continue;
            }
            SelectConditionGroupDto conditionGroupDto = new SelectConditionGroupDto();
            List<Map<String, Object>> dataList = dynamicTableService.getRealTableDataList(formTable.getTableName(), conditionGroupDto, null);
            List<String> rowIds = dataList.stream().map(item -> Convert.toStr(item.get("id"))).toList();
            wfRuntimeService.deleteInvalidProcessInstance(wfTemplate.getActDefinitionId(), rowIds);
        }
    }

    public Map<String, Object> pageFormChildData(ModelingDataListReqDto reqDto) {
        ViewForm viewForm = getViewForm(reqDto.getFormId(), reqDto.getViewFormId());
        List<FormTable> tableList = formTableService.list(new LambdaQueryWrapper<FormTable>()
                .eq(FormTable::getFormId, reqDto.getFormId())
        );
        FormTable mainTable = tableList.stream().filter(i -> i.getType().equals(FormTableTypeEnum.MAIN.getType())).findFirst().orElseThrow();
        List<FormTable> childTable = tableList.stream().filter(i -> i.getType().equals(FormTableTypeEnum.CHILD.getType())).toList();
        // 根据主表id查询对应的数据
        reqDto.getConditions().put("$and",List.of(Map.of(mainTable.getTableName() + "_id", Map.of("$eq",reqDto.getMainTableId()))));
        reqDto.setBaseDataFilter(false);
        SelectConditionGroupDto conditionGroup = viewFormService.buildSelectConditionGroup(viewForm, reqDto.getParams(), reqDto.getConditions(), reqDto.getBaseDataFilter());
        List<SelectSortDto> selectSorts = viewFormService.buildSelectSort(viewForm, reqDto.getSorts());
        // 查询信息
        Map<String, Object> result = new HashMap<>();
        for (FormTable formTable : childTable) {
            String id = formTable.getId();
            String tableName = formTable.getTableName();
            List<FormTableField> tableFields = formTableFieldService.getFieldByTable(id).stream().map(FormTableFieldDto::coverToEntity).collect(Collectors.toList());
            List<Map<String, Object>> rows = dynamicTableService.getRealTableDataList(tableName, conditionGroup, selectSorts);
            UiComponentFactory.transformDataList2Component(tableFields, rows);
            result.put(formTable.getId(),rows);
        }
        return result;
    }
}

