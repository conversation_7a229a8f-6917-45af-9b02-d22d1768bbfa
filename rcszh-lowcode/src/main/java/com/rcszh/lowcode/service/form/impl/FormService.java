package com.rcszh.lowcode.service.form.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rcszh.base.common.exception.ServiceException;
import com.rcszh.base.common.utils.PageUtils;
import com.rcszh.base.common.utils.SecurityUtils;
import com.rcszh.common.constant.Constants;
import com.rcszh.lowcode.dto.FormTableFieldDto;
import com.rcszh.lowcode.dto.form.ExportFormData;
import com.rcszh.lowcode.dto.form.FormMenuDto;
import com.rcszh.lowcode.dto.form.FormTableDto;
import com.rcszh.lowcode.dto.form.ImportFormData;
import com.rcszh.lowcode.entity.dto.FormInfo;
import com.rcszh.lowcode.entity.dto.design_info.FormDesignDataItem;
import com.rcszh.lowcode.entity.dto.design_info.FormDesignInfo;
import com.rcszh.lowcode.entity.form.Form;
import com.rcszh.lowcode.entity.form.FormHistory;
import com.rcszh.lowcode.entity.form.FormTable;
import com.rcszh.lowcode.entity.form.FormTableField;
import com.rcszh.lowcode.enums.FormTableFieldStatusEnum;
import com.rcszh.lowcode.enums.FormTableStatusEnum;
import com.rcszh.lowcode.enums.FormTableTypeEnum;
import com.rcszh.lowcode.enums.form.FormStatusEnum;
import com.rcszh.lowcode.enums.form.SaveFormTypeEnum;
import com.rcszh.lowcode.mapper.FormTableMapper;
import com.rcszh.lowcode.mapper.form.FormMapper;
import com.rcszh.lowcode.schema.FormInfoSchema;
import com.rcszh.lowcode.service.form.IFormHistoryService;
import com.rcszh.lowcode.service.form.IFormService;
import com.rcszh.lowcode.service.form.IFormTableFieldService;
import com.rcszh.lowcode.service.form.IFormTableService;
import com.rcszh.lowcode.service.form_init.IFormInitRuleService;
import com.rcszh.lowcode.service.view.impl.ViewFormService;
import com.rcszh.lowcode.utils.FormTableFieldUtil;
import com.rcszh.lowcode.utils.FormUtil;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;


/**
 * 表单
 */
@Service
public class FormService extends ServiceImpl<FormMapper, Form> implements IFormService {
    @Resource
    private IFormTableService formTableService;
    @Resource
    private IFormTableFieldService formTableFieldService;
    @Resource
    private IFormInitRuleService formInitRuleService;
    @Resource
    private ViewFormService viewFormService;
    @Resource
    private FormMapper formMapper;
    @Resource
    private FormUtil formUtil;
    @Resource
    private IFormHistoryService formHistoryService;
    @Autowired
    private FormTableMapper formTableMapper;

    @Override
    public List<String> getFormGroup(Form form) {
        List<String> allGroup = formMapper.getAllGroup(form.getGroupName());

        return allGroup;
    }


    /**
     * 首次创建表单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Form createForm(FormInfo formInfo, Boolean isImport) {
        // 创建表单信息
        Form form = formInfo.getForm();
        if (form == null) {
            throw new RuntimeException();
        }
        // 不能出现同名表单
        Form oldForm = formMapper.selectOne(new LambdaQueryWrapper<Form>().eq(Form::getName, form.getName()));
        if (oldForm != null) {
            throw new RuntimeException("已存在相同名称的表单");
        }
        // 不能出现同编码表单
        Form oldForm2 = formMapper.selectOne(new LambdaQueryWrapper<Form>().eq(Form::getCode, form.getCode()));
        if (oldForm2 != null) {
            throw new RuntimeException("已存在相同编码的表单");
        }
        // 不能出现相同名称的数据库表
        FormTable formTableByTableName = formTableService.getFormTableByTableName(form.getCode());
        if (formTableByTableName != null) {
            throw new RuntimeException("系统中已存在相同编码的数据表");
        }
        // 创建状态
        form.setFormStatus(FormStatusEnum.CREATED.getStatus());
        form.fillBaseFields();
        formMapper.insert(form);
        // 创建表单对应的库表信息
        Assert.notNull(formInfo.getFormTables());
        // 首次创建只会生成一个主表
        FormTable formTable = formInfo.getFormTables().getFirst();
        String tableName = formTable.getTableName();
        FormTable oldFormTable = formTableService.getFormTableByTableName(tableName);
        if (oldFormTable != null) {
            throw new RuntimeException("表单编码已存在");
        }
        formTable.setFormId(form.getId());
        formTable.setName(form.getName());
        formTable.setStatus(FormTableStatusEnum.CREATED.getStatus());
        formTable.setType(FormTableTypeEnum.MAIN.getType());
        formTable.fillBaseFields();
        formTableService.initMainFormTable(formTable);
        // 创建对应库表的字段
        List<FormTableField> initFormTableFields = new ArrayList<>();
        List<FormTableField> initFields = formTableFieldService.generateInitFields(form.getId(), formTable.getId(), formTable.getTableName());
        if (!isImport) {
            // 非导入的情况下才会初始化字段
            initFormTableFields = initFields;
        }
        FormHistory formHistory = new FormHistory();
        formHistory.setFormId(form.getId());
        formHistory.setFormInfo(JSONUtil.toJsonStr(form));
        formHistory.setFormTableInfo(JSONUtil.toJsonStr(List.of(formTable)));
        formHistory.setFormTableFiledInfo(JSONUtil.toJsonStr(initFormTableFields));
        formHistoryService.saveOrUpdateHistory(formHistory);
        return form;
    }


    /**
     * 获取整个表单信息
     */
    @Override
    public FormInfo getFormInfoById(String formId) {
        FormInfo formInfo = new FormInfo();
        // 获取表单数据
        Form form = formMapper.selectById(formId);
        // 获取表单对应数据库信息
        List<FormTableDto> formTables = formTableService.getTableByFormId(formId).stream().map(FormTableDto::convertToDto).collect(Collectors.toList());
        // 获取表单字段信息
        Map<String, List<FormTableFieldDto>> tableFields = formTableFieldService.getFieldByFormIdToList(formId)
                .stream()
                .filter(item -> Objects.equals(item.getFtfDisplay(),Constants.Y))
                .collect(Collectors.groupingBy(FormTableFieldDto::getFormTableId));
        for (FormTableDto formTable : formTables) {
            List<FormTableFieldDto> formTableFieldDtos = tableFields.getOrDefault(formTable.getId(),new ArrayList<>());
            formTable.setChildren(formTableFieldDtos);
        }
        formInfo.setFormTables(formTables);
        formInfo.setForm(form);
        return formInfo;
    }

    /**
     * 获取所有的表单
     */
    @Override
    public List<Form> getAllForm(Form form) {
        LambdaQueryWrapper<Form> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(form.getName()), Form::getName, form.getName());
        queryWrapper.eq(StrUtil.isNotBlank(form.getType()), Form::getType, form.getType());
        queryWrapper.eq(StrUtil.isNotBlank(form.getCode()), Form::getCode, form.getCode());
        queryWrapper.eq(StrUtil.isNotBlank(form.getGroupName()), Form::getGroupName, form.getGroupName());
        queryWrapper.eq(StrUtil.isNotBlank(form.getFormStatus()), Form::getFormStatus, form.getFormStatus());
        queryWrapper.orderByDesc(Form::getUpdateTime);
        return formMapper.selectList(queryWrapper);
    }

    @Override
    public List<Form> getPageForm(Form form) {
        PageUtils.startPage();
        return getAllForm(form);
    }

    @Override
    public List<Form> findByIds(List<String> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<Form> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(Form::getId, ids);
        return this.list(wrapper);
    }

    /**
     * 发布表单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void releaseForm(FormDesignInfo formDesignInfo) {
        String formId = formDesignInfo.getFormId();
        // 保存
        FormInfoSchema formInfoSchema = formUtil.covertFormRequestToInfo(SaveFormTypeEnum.SAVE_FORM_DESIGN, null, formDesignInfo);
        // 补齐初始化字段
        formInfoSchema = saveForm(SaveFormTypeEnum.SAVE_FORM_DESIGN, formInfoSchema,true);
        // 真实创建字段
        // 这里得查出来，因为前端传递的数据和数据库字段数据不一致
        List<FormTable> formTables = formTableMapper.selectList(new LambdaQueryWrapper<FormTable>().eq(FormTable::getFormId, formId));
        List<FormTableField> formTableFields = formTableFieldService.list(
                new LambdaQueryWrapper<FormTableField>().eq(FormTableField::getFormId, formId)
        );
        formTableService.updateDataModeling(formTables, formTableFields);
        // 更新表单状态
        Form form = formInfoSchema.getForm();
        form.setFormStatus(FormStatusEnum.PUBLISH.getStatus());
        updateById(form);
        // 正式生成库表后再更新字段状态
        // 只要有一个表未发布，则所有表都发布
        formTables.forEach(item -> item.setStatus(FormTableStatusEnum.PUBLISHED.getStatus()));
        formTableFields.forEach(item -> item.setStatus(FormTableFieldStatusEnum.PUBLISHED.getStatus()));
        formTableService.updateBatchById(formTables);
        formTableFieldService.updateBatchById(formTableFields);
        // 生成视图
        viewFormService.createDefaultViewForm(formInfoSchema);
        // 删除历史
        formHistoryService.remove(new LambdaQueryWrapper<FormHistory>().eq(FormHistory::getFormId, formId));
    }

    /**
     * 通过id查询
     */
    @Override
    public Form getFormById(String formId) {
        return formMapper.selectById(formId);
    }









    /**
     * 获取表单前端配置信息
     *
     * @param formId 表单
     * @return
     */
    @Override
    public HashMap<String, Object> getFormDesignInfo(String formId) {
        // 表单
        Form form = formMapper.selectById(formId);
        if (Objects.isNull(form)) {
            throw new ServiceException("表单不存在");
        }
        List<FormTable> formTables = formTableService.list(new LambdaQueryWrapper<FormTable>()
                .eq(FormTable::getFormId, formId)
        );
        List<FormTableField> tableFields = formTableFieldService.list(new LambdaQueryWrapper<FormTableField>()
                .eq(FormTableField::getFormId, formId)
                .eq(FormTableField::getFtfDisplay, Constants.Y)
                .orderByAsc(FormTableField::getComponentOrder)
                .orderByAsc(FormTableField::getId)
        );
        return formUtil.covertDesignInfo(formId, form, formTables, tableFields);
    }

    /**
     * 获取上一次保存后的表单设计信息
     */
    @Override
    public HashMap<String, Object> getHistoryDesignInfo(String formId) {
        FormHistory formHistory = formHistoryService.getOneByFormId(formId);
        if (formHistory == null) {
            return getFormDesignInfo(formId);
        }
        return formUtil.covertDesignInfo(
                formId,
                JSONUtil.parse(formHistory.getFormInfo()).toBean(Form.class),
                JSONUtil.parseArray(formHistory.getFormTableInfo()).toList(FormTable.class),
                JSONUtil.parseArray(formHistory.getFormTableFiledInfo()).toList(FormTableField.class)
        );
    }

    /**
     * 导出表单
     *
     * @param formId 表单id
     * @return
     */
    @Override
    public String exportForm(String formId) {
        ExportFormData exportFormData = new ExportFormData();
        // 拿到表单信息
        Form form = this.getById(formId);
        // 拿到数据表信息
        List<FormTable> formTables = formTableService.list(new LambdaQueryWrapper<FormTable>()
                .eq(FormTable::getFormId, formId)
        );
        // 拿到字段信息
        List<FormTableField> tableFields = formTableFieldService.list(new LambdaQueryWrapper<FormTableField>()
                .in(FormTableField::getFormTableId, formTables.stream().map(FormTable::getId).toList())
        );
        exportFormData.setForm(form);
        exportFormData.setFormTables(formTables);
        exportFormData.setFormTableFields(tableFields);
        return JSONUtil.toJsonStr(exportFormData);
    }

    @Transactional(rollbackFor = Exception.class)
    public FormInfo importForm(ImportFormData importFormData) {
        String formJson = importFormData.getFormJson();
        ExportFormData data = JSONUtil.parse(formJson).toBean(ExportFormData.class);
        Form form = data.getForm();
        List<FormTable> formTables = data.getFormTables();
        List<FormTableField> formTableFields = data.getFormTableFields();
        assert form != null;
        assert formTables != null;
        assert formTableFields != null;
        // TODO 这里需要加一个校验，如果有了新的字段就需要报错，防止后期补充了字段但是这里没有校验
        // 第一步：模拟创建表单
        FormInfo formInfo = new FormInfo();
        form.setId(null);
        formInfo.setForm(form);
        AtomicReference<String> oldMainTableId = new AtomicReference<>();
        List<FormTableDto> newFormTable = formTables.stream()
                .filter(item -> item.getType().equals(FormTableTypeEnum.MAIN.getType()))
                .map(FormTableDto::convertToDto)
                .peek(formTableDto -> {
                    // 移除非必要的字段
                    formTableDto.setFormId(null);
                    formTableDto.setChildren(null);
                    oldMainTableId.set(formTableDto.getId());
                    formTableDto.setId(null);
                })
                .collect(Collectors.toList());
        formInfo.setFormTables(newFormTable);
        // 首次创建表单逻辑
        Form newForm = createForm(formInfo, true);
        // 第二步：模拟创建明细表
        List<FormTable> oldChildTables = formTables.stream().filter(item -> item.getType().equals(FormTableTypeEnum.CHILD.getType())).toList();
        List<FormTable> tables = new ArrayList<>();
        Map<String, String> oldTableIdToNewMap = new HashMap<>();
        for (FormTable oldChildTable : oldChildTables) {
            FormTable formTable = new FormTable();
            BeanUtil.copyProperties(oldChildTable, formTable);
            String oldTableId = formTable.getId();
            formTable.setId(null);
            formTable.setFormId(newForm.getId());
            FormTable childTable = formTableService.createChildTable(formTable);
            tables.add(childTable);
            oldTableIdToNewMap.put(oldTableId, childTable.getId());
            // 更新了
        }
        // 第三步：模拟创建字段
        formInfo = new FormInfo();
        formInfo.setForm(newForm);
        FormTable mainTable = formTableService.getOne(new LambdaQueryWrapper<FormTable>()
                .eq(FormTable::getType, FormTableTypeEnum.MAIN.getType())
                .eq(FormTable::getFormId, newForm.getId())
        );
        tables.add(mainTable);
        oldTableIdToNewMap.put(oldMainTableId.get(), mainTable.getId());
        // 根据表进行分组
        Map<String, List<FormTableFieldDto>> fieldMap = formTableFields.stream()
                .map(FormTableFieldDto::coverToDto)
                .collect(Collectors.groupingBy(item -> oldTableIdToNewMap.get(item.getFormTableId())));
        newFormTable = tables.stream()
                .map(FormTableDto::convertToDto)
                .collect(Collectors.toList());
//        Map<String, String> fileIdMap = new HashMap<>();
        for (FormTableDto formTableDto : newFormTable) {
            List<FormTableFieldDto> fields = fieldMap.get(formTableDto.getId());
            // 将父组件id 和 父组件code 关联
            HashMap<String, String> parentMapper = new HashMap<>();
            // 拿到所有的父组件
            Set<FormTableFieldDto> parentIdSet = fields.stream().filter(item -> item.getParentId() != null).collect(Collectors.toSet());
            parentIdSet.forEach(item -> {
                String parentId = item.getParentId();
                FormTableFieldDto parent = fields.stream().filter(field -> field.getId().equals(parentId)).findFirst().orElseThrow();
                parentMapper.put(parent.getId(), parent.getName());
            });
            fields.forEach(item -> {
                item.setDesignKey(null);
                item.setFormId(newForm.getId());
                item.setFormTableId(formTableDto.getId());
                item.setParentId(parentMapper.get(item.getParentId()));
//                fileIdMap.put(item.getId(), item.getFormTableId() + "-" + item.getName());
                item.setId(null);
                item.setStatus(FormTableFieldStatusEnum.CREATED.getStatus());
            });
            formTableDto.setChildren(fields);
        }
        formInfo.setFormTables(newFormTable);
        FormInfoSchema formInfoSchema = formUtil.covertFormRequestToInfo(SaveFormTypeEnum.SAVE_FORM_TABLE, formInfo, null);
        saveForm(SaveFormTypeEnum.SAVE_FORM_TABLE, formInfoSchema, false);
        // 添加字段
        return formInfo;
    }


    @Override
    public List<HashMap<String, Object>> getFormFieldsById(String formId) {
        List<FormTable> formTables = formTableService.list(new LambdaQueryWrapper<FormTable>()
                .eq(FormTable::getFormId, formId)
        );
        Map<String, FormTable> formTableMap = formTables.stream().collect(Collectors.toMap(FormTable::getId, t -> t, (k1, k2) -> k1));
        List<FormTableField> tableFields = formTableFieldService.list(new LambdaQueryWrapper<FormTableField>()
                .eq(FormTableField::getFormId, formId)
        );
        for (FormTableField tableField : tableFields) {
            FormTable formTable = formTableMap.get(tableField.getFormTableId());
            tableField.setFormTableName(formTable.getTableName());
        }
        return formUtil.transformFormDesignFieldToMap(tableFields);
    }

    @Override
    public void deleteForm(String formId) {
        Form form = this.getById(formId);
        if (Objects.isNull(form)) {
            throw new ServiceException("表单不存在");
        }
        formTableFieldService.remove(new LambdaQueryWrapper<FormTableField>()
               .eq(FormTableField::getFormId, formId)
        );
        formTableService.remove(new LambdaQueryWrapper<FormTable>()
              .eq(FormTable::getFormId, formId)
        );
        viewFormService.deleteViewFormByFormId(formId);
        // 移除历史
        formHistoryService.remove(new LambdaQueryWrapper<FormHistory>().eq(FormHistory::getFormId, formId));
        this.removeById(formId);
    }

    /**
     * 保存表单操作
     *
     * @param type           操作类型
     * @param formInfoSchema       表单
     * @param isRelease     是否发布
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public FormInfoSchema saveForm(SaveFormTypeEnum type, FormInfoSchema formInfoSchema, Boolean isRelease) {
        if (!isRelease) {
            // 只要不是发布就做保存历史操作
            formHistoryService.saveHistoryByFormSchema(formInfoSchema);
            return null;
        }
        if (type == SaveFormTypeEnum.SAVE_FORM_DESIGN) {
            // 表单设计保存
            formMapper.updateById(formInfoSchema.getForm());
            // 保存表单表 formTable
            formTableService.updateBatchById(formInfoSchema.getFormTables());
            // 删除字段
            formTableFieldService.deleteFields(formInfoSchema.getDeleteFieldIds(), true);
            // 保存字段
            formTableFieldService.saveOrUpdateTableFields(formInfoSchema.getFormTableFields());
        } else {
            // 表单模型保存
            Form form = formInfoSchema.getForm();
            // 更新表单
            formMapper.updateById(form);
            // 删除表和字段
            formTableFieldService.deleteFields(formInfoSchema.getDeleteFieldIds(), true);
            // 找是否存在当前保存表以外其他的表，存在则需要删除，这个流程一般不会走（除非发生数据错误）
            List<FormTable> formTables = formTableService.list(new LambdaQueryWrapper<FormTable>()
                    .in(FormTable::getFormId, form.getId())
                    .notIn(FormTable::getId, formInfoSchema.getFormTables().stream().map(FormTable::getId).toList())
            );
            if (CollUtil.isNotEmpty(formTables)) {
                List<String> formTableIds = formTables.stream().map(FormTable::getId).toList();
                List<FormTableField> publishedTableFields = formTableFieldService.list(new LambdaQueryWrapper<FormTableField>()
                        .in(FormTableField::getFormId, form.getId())
                        .in(FormTableField::getFormTableId, formTableIds)
                        .in(FormTableField::getStatus, Arrays.asList(FormTableFieldStatusEnum.PUBLISHED.getStatus(), FormTableFieldStatusEnum.DEACTIVATED.getStatus()))
                );
                Map<String, List<FormTableField>> publishedTableFieldGroup = publishedTableFields.stream().collect(Collectors.groupingBy(FormTableField::getFormTableId));
                for (FormTable formTable : formTables) {
                    if (FormTableTypeEnum.MAIN.getType().equals(formTable.getType())) {
                        throw new ServiceException("主表不能被删除");
                    }
                    List<FormTableField> formTableFields = publishedTableFieldGroup.get(formTable.getId());
                    if (CollUtil.isNotEmpty(formTableFields)) {
                        throw new ServiceException(StrUtil.format("[{}]表下存在已发布的字段,不能删除", formTable.getName()));
                    }
                }
                formTableService.removeByIds(formTableIds);
                // 移除所有的字段
                List<String> filedIdList = formTableFieldService.list(new LambdaQueryWrapper<FormTableField>().in(FormTableField::getFormTableId, formTableIds)).stream().map(FormTableField::getId).toList();
                if (CollUtil.isNotEmpty(filedIdList)) {
                    formTableFieldService.removeByIds(filedIdList);
                }
            }

            Map<String, List<FormTableFieldDto>> fields = new HashMap<>();
            for (FormTableFieldDto formTableField : formInfoSchema.getFormTableFields()) {
                List<FormTableFieldDto> list = fields.getOrDefault(formTableField.getFormTableId(), new ArrayList<>());
                list.add(formTableField);
                fields.put(formTableField.getFormTableId(), list);
            }
            String fieldStatus = form.getFormStatus().equals(FormStatusEnum.PUBLISH.getStatus()) ? FormTableFieldStatusEnum.PUBLISHED.getStatus() : FormTableFieldStatusEnum.CREATED.getStatus();
            for (Map.Entry<String, List<FormTableFieldDto>> entry : fields.entrySet()) {
                String tableId = entry.getKey();
                // 找到字段对应的表
                FormTable formTable = formInfoSchema.getFormTables().stream().filter(item -> item.getId().equals(tableId)).findFirst().orElse(null);
                if (formTable == null) {
                    throw new RuntimeException("字段表格不正确");
                }
                int fieldOrder = 1;
                for (FormTableFieldDto formTableFieldDto : entry.getValue()) {
                    formTableFieldDto.setFieldOrder(fieldOrder++);
                }
                // 字段生成
                formTableFieldService.saveOrUpdateTableFields(entry.getValue());
            }
        }
        // 保存历史
//        formHistoryService.saveHistoryByFormId(formInfoSchema.getForm().getId());
        return formInfoSchema;
    }


    /**
     * 获取上一次保存后的表单信息
     */
    @Override
    public FormInfo getVersionFormInfo(String formId) {
        return getFormInfoById(formId);
//        FormHistory formHistory =  formHistoryService.getOneByFormId(formId);
//        if (Objects.isNull(formHistory)) {
//            return getFormInfoById(formId);
//        }
//        FormInfo formInfo = new FormInfo();
//        // 获取表单数据
//        Form form = JSONUtil.parse(formHistory.getFormInfo()).toBean(Form.class);
//        List<FormTableDto> list = JSONUtil.parseArray(formHistory.getFormTableInfo()).toList(FormTableDto.class);
//        // 获取表单对应数据库信息 || 获取表单字段信息
//        List<FormTableFieldDto> formTables = JSONUtil.parseArray(formHistory.getFormTableFiledInfo()).toList(FormTableFieldDto.class);
//        Map<String, List<FormTableFieldDto>> collect = formTables.stream().collect(Collectors.groupingBy(FormTableFieldDto::getFormTableId));
//        for (FormTableDto formTable : list) {
//            List<FormTableFieldDto> formTableFieldDtos = collect.getOrDefault(formTable.getId(),new ArrayList<>());
//            formTable.setChildren(formTableFieldDtos);
//        }
//        formInfo.setFormTables(list);
//        formInfo.setForm(form);
//        return formInfo;
    }

    public List<FormMenuDto> getFormMenu() {
        return formMapper.getFormMenu();
    }

    @Override
    public void updateFormInfo(FormInfo formInfo) {
        // 更新表单基础信息
        Form form = formInfo.getForm();
        Form oldForm = formMapper.selectById(form.getId());
        oldForm.setName(form.getName());
        oldForm.setGroupName(form.getGroupName());
        formMapper.updateById(oldForm);
    }
}
