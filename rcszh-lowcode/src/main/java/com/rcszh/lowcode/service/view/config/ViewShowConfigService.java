package com.rcszh.lowcode.service.view.config;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rcszh.common.constant.Constants;
import com.rcszh.lowcode.entity.form.FormTableField;
import com.rcszh.lowcode.entity.view.ViewForm;
import com.rcszh.lowcode.entity.view.ViewFormConfig;
import com.rcszh.lowcode.enums.view_form.ViewConfigOptionFieldTypeEnum;
import com.rcszh.lowcode.enums.view_form.ViewFormConfigTypeEnum;
import com.rcszh.lowcode.schema.ViewConfigOptionsSchema;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


public class ViewShowConfigService {

    public static ViewFormConfig createOrUpdateShowViewConfig(ViewForm viewForm,
                                              ViewFormConfig showViewFormConfig,
                                              ViewFormConfigTypeEnum configType) {
        if (showViewFormConfig == null) {
            showViewFormConfig = new ViewFormConfig();
            showViewFormConfig.setViewFormId(viewForm.getId());
            showViewFormConfig.setViewFormType(viewForm.getType());
            showViewFormConfig.setType(configType.getType());
            showViewFormConfig.setName(configType.getName());
            showViewFormConfig.setSystemType(viewForm.getSystemType());
            showViewFormConfig.setOptions(JSONUtil.toJsonStr(new ArrayList<>()));
            showViewFormConfig.fillBaseFields();
        }
        return showViewFormConfig;
    }

}
