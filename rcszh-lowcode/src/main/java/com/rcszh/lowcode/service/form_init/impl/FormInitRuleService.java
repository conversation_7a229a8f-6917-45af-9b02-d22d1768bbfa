package com.rcszh.lowcode.service.form_init.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rcszh.lowcode.entity.form_init.FormInitRule;
import com.rcszh.lowcode.entity.form_init.FormInitRuleField;
import com.rcszh.lowcode.mapper.FormInitRuleFieldMapper;
import com.rcszh.lowcode.mapper.FormInitRuleMapper;
import com.rcszh.lowcode.service.form_init.IFormInitRuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.beans.PropertyDescriptor;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class FormInitRuleService extends ServiceImpl<FormInitRuleMapper, FormInitRule> implements IFormInitRuleService {

    @Autowired
    private FormInitRuleMapper formInitRuleMapper;

    @Autowired
    private FormInitRuleFieldMapper formInitRuleFieldMapper;

    @Override
    public int saveFormInitRule(FormInitRule formInitRule) {
        int insert = formInitRuleMapper.insert(formInitRule);
        if (CollUtil.isNotEmpty(formInitRule.getFields())) {
            for (FormInitRuleField formInitRuleField : formInitRule.getFields()) {
                formInitRuleField.setInitRuleId(formInitRule.getId());
                formInitRuleField.setFormId(formInitRule.getFormId());
                formInitRuleField.fillBaseFields();
                formInitRuleFieldMapper.insert(formInitRuleField);
            }
        }
        return insert;
    }

    @Override
    public int updateFormInitRule(FormInitRule formInitRule) {
        formInitRuleFieldMapper.delete(new LambdaQueryWrapper<FormInitRuleField>().eq(FormInitRuleField::getInitRuleId, formInitRule.getId()));
        if (CollUtil.isNotEmpty(formInitRule.getFields())) {
            for (FormInitRuleField formInitRuleField : formInitRule.getFields()) {
                formInitRuleField.setInitRuleId(formInitRule.getId());
                formInitRuleField.setFormId(formInitRule.getFormId());
                formInitRuleField.fillBaseFields();
                formInitRuleFieldMapper.insert(formInitRuleField);
            }
        }
        return formInitRuleMapper.updateById(formInitRule);
    }

    private Map<String, Object> transformFields(FormInitRule formInitRule, List<FormInitRuleField> initRuleFields, Map<String, Object> inputData) {
        Map<String, Object> result = this.transformFields2(formInitRule, initRuleFields, inputData);
        for (Map.Entry<String, Object> entry : inputData.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            if (value instanceof List) {
                List<Map<String, Object>>  list = (List)value;
                if (CollUtil.isNotEmpty(list)) {
                    Map<String, Object> firstItem = list.get(0);
                    String ruleCode = (String)firstItem.get("$ruleCode");
                    if (StrUtil.isNotBlank(ruleCode)) {
                        FormInitRule initRule = formInitRuleMapper.selectOne(new LambdaQueryWrapper<FormInitRule>().eq(FormInitRule::getCode, ruleCode));
                        List<FormInitRuleField> fields = formInitRuleFieldMapper.selectList(new LambdaQueryWrapper<FormInitRuleField>().eq(FormInitRuleField::getInitRuleId, initRule.getId()));
                        List<Map<String, Object>> newItems = new ArrayList<>();
                        for (Map<String, Object> item : list) {
                            newItems.add(this.transformFields(initRule, fields, item));
                        }
                        String outputField = initRule.getOutputField();
                        if (StrUtil.isBlank(outputField)) {
                            outputField = key;
                        }
                        result.put(outputField, newItems);
                    }
                }
            } else {
                String ruleCode = BeanUtil.getProperty(value, "$ruleCode");
                if (StrUtil.isNotBlank(ruleCode)) {
                    FormInitRule initRule = formInitRuleMapper.selectOne(new LambdaQueryWrapper<FormInitRule>().eq(FormInitRule::getCode, ruleCode));
                    List<FormInitRuleField> fields = formInitRuleFieldMapper.selectList(new LambdaQueryWrapper<FormInitRuleField>().eq(FormInitRuleField::getInitRuleId, initRule.getId()));
                    result.put(key, this.transformFields(initRule, fields, (Map<String, Object>)value));
                }
            }
        }
        return result;
    }
    private Map<String, Object> transformFields2(FormInitRule formInitRule, List<FormInitRuleField> initRuleFields, Map<String, Object> inputData) {
        Map<String, Object> result = new HashMap<>();
        for (FormInitRuleField initRuleField : initRuleFields) {
            Object value = BeanUtil.getProperty(inputData, initRuleField.getSourceCode());
            if (Objects.nonNull(value)) {
                result.put(initRuleField.getTargetCode(), value);
            }
        }
        return result;
    }

    @Override
    public Map<String, Object> transformData(Map<String, Object> inputData) {
        String ruleCode = (String) inputData.get("$ruleCode");
        FormInitRule formInitRule = formInitRuleMapper.selectOne(new LambdaQueryWrapper<FormInitRule>().eq(FormInitRule::getCode, ruleCode));
        List<FormInitRuleField> initRuleFields = formInitRuleFieldMapper.selectList(new LambdaQueryWrapper<FormInitRuleField>().eq(FormInitRuleField::getInitRuleId, formInitRule.getId()));
        return this.transformFields(formInitRule, initRuleFields, inputData);
    }

    @Override
    public FormInitRule getByCode(String ruleCode) {
        return formInitRuleMapper.selectOne(new LambdaQueryWrapper<FormInitRule>().eq(FormInitRule::getCode, ruleCode));
    }

    @Override
    public int removeInitRuleByFormId(String formId) {
        List<FormInitRule> formInitRules = formInitRuleMapper.selectList(new LambdaQueryWrapper<FormInitRule>().eq(FormInitRule::getFormId, formId));
        if (CollUtil.isEmpty(formInitRules)) {
            return 0;
        }
        List<String> ruleIds = formInitRules.stream().map(FormInitRule::getId).toList();
        formInitRuleFieldMapper.delete(new LambdaQueryWrapper<FormInitRuleField>().in(FormInitRuleField::getInitRuleId, ruleIds));
        return formInitRuleMapper.deleteBatchIds(ruleIds);
    }
}
