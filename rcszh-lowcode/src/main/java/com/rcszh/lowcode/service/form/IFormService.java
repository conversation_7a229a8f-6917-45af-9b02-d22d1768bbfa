package com.rcszh.lowcode.service.form;

import com.baomidou.mybatisplus.extension.service.IService;
import com.rcszh.lowcode.dto.form.FormMenuDto;
import com.rcszh.lowcode.dto.form.ImportFormData;
import com.rcszh.lowcode.entity.dto.FormInfo;
import com.rcszh.lowcode.entity.dto.design_info.FormDesignInfo;
import com.rcszh.lowcode.entity.form.Form;
import com.rcszh.lowcode.enums.form.SaveFormTypeEnum;
import com.rcszh.lowcode.schema.FormInfoSchema;

import java.util.HashMap;
import java.util.List;

public interface IFormService extends IService<Form> {
    /**
     * 创建表单
     *
     * @param formInfo 表单信息
     * @return 表单id
     */
    Form createForm(FormInfo formInfo, Boolean isImport);


    FormInfo getFormInfoById(String formId);

    List<Form> getAllForm(Form form);

    List<Form> getPageForm(Form form);

    List<Form> findByIds(List<String> ids);

    void releaseForm(FormDesignInfo formDesignInfo);

    Form getFormById(String formId);

    HashMap<String, Object> getFormDesignInfo(String formId);

    List<HashMap<String, Object>> getFormFieldsById(String formId);
    /**
     * 删除表单
     */
    void deleteForm(String formId);
    /**
     * 保存表单
     * @param isRelease 是否是发布
     *
     */
    FormInfoSchema saveForm(SaveFormTypeEnum type, FormInfoSchema formInfoSchema, Boolean isRelease);
    /**
     * 获取上一次保存后的表单信息
     */
    FormInfo getVersionFormInfo(String formId);
    /**
     * 获取上一次保存后的表单设计信息
     */
    HashMap<String, Object> getHistoryDesignInfo(String formId);
    /**
     * 导出表单
     */
    String exportForm(String formId);
    /**
     * 导入表单
     */
    FormInfo importForm(ImportFormData formJson);
    /**
     * 获取表单分组
     */
    List<String> getFormGroup(Form form);
    /**
     * 绑定菜单时查询表单相关信息（菜单专用）
     */
    List<FormMenuDto> getFormMenu();
    /**
     * 更新表单信息
     */
    void updateFormInfo(FormInfo form);
}
