package com.rcszh.lowcode.service;

import com.rcszh.lowcode.entity.Application;
import com.rcszh.lowcode.mapper.ApplicationMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class ApplicationService {

    @Resource
    private ApplicationMapper applicationMapper;

    /**
     * 获取所有的应用
     */
    public Object findAllApplication() {
        return applicationMapper.selectList(null);
    }

    /**
     * 保存应用
     * @param application
     */
    public void saveOneApplication(Application application) {
        applicationMapper.insert(application);
    }
}
