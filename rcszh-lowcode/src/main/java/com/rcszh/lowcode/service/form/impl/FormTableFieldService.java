package com.rcszh.lowcode.service.form.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rcszh.base.common.exception.ServiceException;
import com.rcszh.common.constant.Constants;
import com.rcszh.lowcode.core.UiComponentFactory;
import com.rcszh.lowcode.core.enums.UiComponentTypeEnum;
import com.rcszh.lowcode.dto.FormTableFieldDto;
import com.rcszh.lowcode.entity.FormDataField;
import com.rcszh.lowcode.entity.form.FormBaseFormTableField;
import com.rcszh.lowcode.entity.form.FormTable;
import com.rcszh.lowcode.entity.form.FormTableField;
import com.rcszh.lowcode.enums.FormTableFieldStatusEnum;
import com.rcszh.lowcode.enums.FormTableTypeEnum;
import com.rcszh.lowcode.enums.form.FormOptionsComponentEnum;
import com.rcszh.lowcode.mapper.FormTableFieldMapper;
import com.rcszh.lowcode.mapper.FormTableMapper;
import com.rcszh.lowcode.orm.factory.ORMFactory;
import com.rcszh.lowcode.orm.factory.OrmConfig;
import com.rcszh.lowcode.service.form.IFormTableFieldService;
import com.rcszh.lowcode.utils.FormTableFieldUtil;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class FormTableFieldService extends ServiceImpl<FormTableFieldMapper, FormTableField> implements IFormTableFieldService {
    private static final List<String> filterList = new ArrayList<>();

    static {
        filterList.add(FormOptionsComponentEnum.TEXT.getComponent());
    }

    @Resource
    private FormTableFieldMapper formTableFieldMapper;

    @Autowired
    private FormTableMapper formTableMapper;

    @Autowired
    private ORMFactory ormFactory;

    /**
     * 基础的 queryWrapper
     * 默认就需要查询 当前字段配置的表单id
     */
    private LambdaQueryWrapper<FormDataField> getBaseLambdaQueryWrapper(String formDataTableId) {
        LambdaQueryWrapper<FormDataField> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FormDataField::getFromDataTableId, formDataTableId);
        return queryWrapper;
    }

    @Override
    public Map<String, List<FormTableFieldDto>> getFieldByFormIdToMap(String formId) {
        List<FormTableField> formTableFields = formTableFieldMapper.selectList(new LambdaQueryWrapper<FormTableField>()
                .eq(FormTableField::getFormId, formId)
                .orderByAsc(FormTableField::getFieldOrder)
                .orderByAsc(FormTableField::getId)
        );
        return formTableFields.stream()
                .map(FormTableFieldDto::coverToDto)
                .collect(Collectors.groupingBy(FormTableFieldDto::getFormTableId));
    }

    @Override
    public List<FormTableFieldDto> getFieldByFormIdToList(String formId) {
        return formTableFieldMapper.selectList(new LambdaQueryWrapper<FormTableField>()
                .eq(FormTableField::getFormId, formId)
                .orderByAsc(FormTableField::getFieldOrder)
                .orderByAsc(FormTableField::getId)
        ).stream().map(FormTableFieldDto::coverToDto).collect(Collectors.toList());
    }

    /**
     * 通过表单表id获取库表字段
     */
    @Override
    public List<FormTableFieldDto> getFieldByTable(String formTableId) {
        return formTableFieldMapper.selectList(new LambdaQueryWrapper<FormTableField>().eq(FormTableField::getFormTableId, formTableId))
                .stream()
                .map(FormTableFieldDto::coverToDto)
                .collect(Collectors.toList());
    }

    /**
     * 获取有用的字段，即真实存在于数据库中的字段
     */
    @Override
    public List<FormTableFieldDto> getUsefulFieldByTable(String formTableId) {
        return formTableFieldMapper.selectList(new LambdaQueryWrapper<FormTableField>()
                        .eq(FormTableField::getFormTableId, formTableId)
                        .isNotNull(FormTableField::getJdbcType)
                )
                .stream()
                .map(FormTableFieldDto::coverToDto)
                .collect(Collectors.toList());
    }

    /**
     * 生成数据库字段配置信息，同时生成真实库表字段
     * TODO 更新索引的行为移除，只能通过接口一个个的更新
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveOrUpdateTableFields(List<FormTableFieldDto> formTableFields) {
        Map<String, List<FormTableFieldDto>> tableMap = formTableFields.stream().collect(Collectors.groupingBy(FormBaseFormTableField::getFormTableId));
        for (String table : tableMap.keySet()) {
            List<FormTableFieldDto> fieldDtos = tableMap.get(table);
            // 更新真实的数据库字段
            Map<String, List<FormTableFieldDto>> collect = fieldDtos.stream().collect(Collectors.groupingBy(item -> item.getId() == null ? "添加" : "更新"));
            List<FormTableFieldDto> updateList = Optional.ofNullable(collect.get("更新")).orElse(new ArrayList<>());
            List<FormTableFieldDto> insertList = Optional.ofNullable(collect.get("添加")).orElse(new ArrayList<>());
            // 处理新增数据
            List<FormTableField> realInsertList = new ArrayList<>();
            // 添加逻辑
            // 得先找到明细表的对应的父字段，对其Id赋值
            Map<String, List<FormTableFieldDto>> parentGroup = insertList.stream().filter(item -> item.getParentId() != null).collect(Collectors.groupingBy(FormTableFieldDto::getParentId));
            for (String parentId : parentGroup.keySet()) {
                Optional<FormTableFieldDto> parentOptional = insertList.stream().filter(item -> item.getName().equals(parentId)).findFirst();
                if (parentOptional.isPresent()) {
                    FormTableFieldDto parentField = parentOptional.get();
                    String idStr = IdWorker.getIdStr();
                    parentField.setId(idStr);
                    List<FormTableFieldDto> childField = parentGroup.get(parentId);
                    childField.forEach(item -> item.setParentId(idStr));
                } else {
                    // 说明新添加的字段在旧的父容器中
                    parentOptional = updateList.stream().filter(item -> item.getId().equals(parentId)).findFirst();
                    if (parentOptional.isEmpty()) {
                        throw new ServiceException("父容器字段不存在,请检查容器字段：" + parentId);
                    }
                }
            }
            for (FormTableFieldDto formTableField : insertList) {
                FormTableField newField = formTableField.coverToEntity();
                newField.setStatus(FormTableFieldStatusEnum.CREATED.getStatus());
                newField.fillBaseFields();
                realInsertList.add(newField);
            }
            // 处理更新数据
            Map<String, FormTableField> realInsertMap = realInsertList.stream().collect(Collectors.toMap(FormTableField::getName, i -> i));
            List<FormTableField> realUpdateList = new ArrayList<>();
            // 更新逻辑
            List<String> updateFieldIdList = updateList.stream().map(FormTableFieldDto::getFormTableId).toList();
            Map<String, FormTableField> oldFieldMap = updateFieldIdList.isEmpty() ? new HashMap<>() : list(new LambdaQueryWrapper<FormTableField>()
                    .in(FormTableField::getFormTableId, updateFieldIdList)
            ).stream().collect(Collectors.toMap(FormTableField::getId, item -> item));
            for (FormTableFieldDto formTableField : updateList) {
                FormTableField newField = formTableField.coverToEntity();
                newField.setStatus(newField.getStatus() != null ? newField.getStatus() : FormTableFieldStatusEnum.CREATED.getStatus());
                FormTableField oldFormTableField = oldFieldMap.get(formTableField.getId());
                checkFormTableField(newField, oldFormTableField);
                newField.fillBaseFields();
                // 处理父组件
                if (newField.getParentId() != null) {
                    FormTableField parentField = oldFieldMap.get(newField.getParentId());
                    if (parentField == null) {
                        // 说明父组件是新添加的
                        FormTableField field = realInsertMap.get(newField.getParentId());
                        if (field == null) {
                            throw new ServiceException("父容器字段不存在,请检查容器字段：" + newField.getParentId());
                        }
                        String idStr = field.getId() == null ? IdWorker.getIdStr() : field.getId();
                        field.setId(idStr);
                        newField.setParentId(idStr);
                    }
                }
                realUpdateList.add(newField);
            }
            // 执行更新
            updateBatchById(realUpdateList);
            saveBatch(realInsertList);
        }
    }

    private void checkFormTableField(FormTableField newField, FormTableField oldField) {
        if (StrUtil.isBlank(newField.getFormId())) {
            throw new ServiceException("表单id不能为空");
        }
        if (StrUtil.isBlank(newField.getFormTableId())) {
            throw new ServiceException("表id不能为空");
        }
        UiComponentTypeEnum type = UiComponentTypeEnum.getByType(newField.getComponent());
        if (type == null) {
            throw new ServiceException("目前尚不支持该组件：" + newField.getComponent());
        }
        if (oldField != null && oldField.getStatus().equals(FormTableFieldStatusEnum.PUBLISHED.getStatus())) {
            if (!oldField.getName().equals(newField.getName())) {
                throw new RuntimeException("已发布字段" + oldField.getLabel() + "编码不能被修改");
            }
            if (!oldField.getComponent().equals(newField.getComponent())) {
                throw new RuntimeException("已发布字段" + oldField.getLabel() + "组件类型不能被修改");
            }
            if (!newField.getStatus().equals(oldField.getStatus())) {
                throw new RuntimeException("已发布字段[" + oldField.getLabel() + "]状态不能被修改");
            }
        }
        String regex = "^[a-z][a-z0-9_]*$";
        String fieldName = newField.getName();
        if (!fieldName.matches(regex)) {
            throw new RuntimeException(StrUtil.format("数据字段编码{}名称不规范", fieldName));
        }
    }

    /**
     * 保存或更新表单字段
     * @param formTableField 表单字段
     * @return
     */
    @Override
    public FormTableField saveOrUpdateTableField(FormTableFieldDto formTableField) {
        // 更新真实的数据库字段
        FormTableField field = formTableField.coverToEntity();
        field.setStatus(field.getStatus() != null ? field.getStatus() : FormTableFieldStatusEnum.CREATED.getStatus());

        String formTableId = field.getFormTableId();
        FormTable formTable = formTableMapper.selectById(formTableId);
        if (formTableField.getId() != null) {
            // 更新逻辑
            FormTableField oldFormTableField = Optional.ofNullable(formTableFieldMapper.selectById(formTableField.getId())).orElseThrow();
            checkFormTableField(field, oldFormTableField);
            field.fillBaseFields();
            String oldIsOpenIndex = oldFormTableField.getIsOpenIndex();
            formTableFieldMapper.updateById(field);
            if(field.getStatus().equals(FormTableFieldStatusEnum.PUBLISHED.getStatus())){
                if (!Objects.equals(oldIsOpenIndex,field.getIsOpenIndex())) {
                    // 说明状态发生了变更
                    if (Constants.Y.equals(field.getIsOpenIndex())){
                        ormFactory.createORM().createTableIndex(formTable.getTableName(), field.getName());
                    }else{
                        ormFactory.createORM().removeTableIndex(formTable.getTableName(), field.getName());
                    }
                }
            }
        } else {
            // 若是新增字段那么需要更新其状态
            checkFormTableField(field, null);
            field.fillBaseFields();
            if (StrUtil.isBlank(field.getFtfDisplay())) {
                field.setFtfDisplay(Constants.Y);
            }
            if (StrUtil.isBlank(field.getFtfIsSystem())) {
                field.setFtfIsSystem(Constants.N);
            }
            if(field.getStatus().equals(FormTableFieldStatusEnum.PUBLISHED.getStatus())){
                if (Constants.Y.equals(field.getIsOpenIndex())) {
                    ormFactory.createORM().createTableIndex(formTable.getTableName(), field.getName());
                }
            }
            formTableFieldMapper.insert(field);
        }
        return field;
    }

    /**
     * 生成初始化字段
     * 当表单创建时默认自带的字段
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void generateChildInitFields(FormTable mainTable, String formId, String formTableId) {
//        ((FormTableFieldService)AopContext.currentProxy()).generateChildInitFields(mainTable, formId, formTableId);
        generateInitFields(formId, formTableId,mainTable.getTableName());
        // 创建关联字段，好让主表和明细表能进行关联查询
        FormTableField formTableField = new FormTableField();
        formTableField.setFormId(formId);
        formTableField.setFormTableId(formTableId);
        // todo 这里的设置也是不可控制的，比如我修改了 ORM 那边的规则，忘记改这里的规则就会有问题
        formTableField.setName(mainTable.getForeignKey());
        formTableField.setComponent(UiComponentTypeEnum.FIXED_INPUT.getType());
        formTableField.setLabel("主表外键");
        formTableField.setJdbcType(UiComponentTypeEnum.FIXED_INPUT.getJdbcType());
        formTableField.setLength(UiComponentTypeEnum.FIXED_INPUT.getLength());
        formTableField.setFtfDisplay(Constants.N);
        formTableField.setFtfIsSystem(Constants.Y);
        formTableField.setStatus(FormTableFieldStatusEnum.CREATED.getStatus());
        formTableField.fillBaseFields();
        HashMap<Object, Object> options = new HashMap<>();
        formTableField.setOptions(JSONUtil.parse(options).toString());
        formTableFieldMapper.insert(formTableField);
    }



    /**
     * 生成初始化字段
     * 当表单创建时默认自带的字段
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<FormTableField> generateInitFields(String formId, String formTableId, String formTableName) {
        List<FormTableField> initFields = FormTableFieldUtil.getInitFields(formId, formTableId);
        saveBatch(initFields);
        return initFields;
    }

    @Override
    public void updateInitFields(String formId) {
        FormTable mainTable = formTableMapper.selectOne(new LambdaQueryWrapper<FormTable>()
                .eq(FormTable::getFormId, formId)
                .eq(FormTable::getType, FormTableTypeEnum.MAIN.getType())
        );

        List<FormTableField> formTableFields = formTableFieldMapper.selectList(new LambdaQueryWrapper<FormTableField>()
                .eq(FormTableField::getFormId, formId)
                .eq(FormTableField::getFormTableId, mainTable.getId())
        );

        Map<String, FormTableField> tableFieldMap = formTableFields.stream().collect(Collectors.toMap(FormTableField::getName, t -> t, (t1, t2) -> t1));
        List<FormTableField> initFields = FormTableFieldUtil.getInitFields(formId, mainTable.getId());
        for (FormTableField initField : initFields) {
            FormTableField formTableField = tableFieldMap.get(initField.getName());
            if (Objects.isNull(formTableField)) {
                initField.fillBaseFields();
                formTableFieldMapper.insert(initField);
            } else {
                BeanUtil.copyProperties(initField, formTableField);
                formTableField.fillBaseFields();
                formTableFieldMapper.updateById(formTableField);
            }
        }
    }

    @Override
    public int deleteById(String tableFieldId) {
        FormTableField formTableField = formTableFieldMapper.selectById(tableFieldId);
        if(Objects.isNull(formTableField)) {
            throw new ServiceException("字段不存在");
        }
        if (FormTableFieldStatusEnum.PUBLISHED.getStatus().equals(formTableField.getStatus())) {
            throw new ServiceException("已发布字段不能删除");
        }
        return formTableFieldMapper.deleteById(tableFieldId);
    }

    /**
     * 批量删除字段
     * @param deleteFieldIds 需要被删除的字段id
     * @param isDoDelete 是否真的执行删除
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteFields(List<String> deleteFieldIds,boolean isDoDelete) {
        if (CollUtil.isEmpty(deleteFieldIds)) {
            return;
        }
        List<FormTableField> delTableFields = list(new LambdaQueryWrapper<FormTableField>()
                .in(FormTableField::getId, deleteFieldIds)
        );
        if (CollUtil.isNotEmpty(delTableFields)) {
            for (FormTableField delTableField : delTableFields) {
                // 已发布的db字段不能删除，布局字段可以删除
                if (StrUtil.isNotBlank(delTableField.getJdbcType())) {
                    if (FormTableFieldStatusEnum.PUBLISHED.getStatus().equals(delTableField.getStatus())) {
                        throw new ServiceException(StrUtil.format("[{}]字段已发布,不能被删除", delTableField.getName()));
                    }
                    if (FormTableFieldStatusEnum.DEACTIVATED.getStatus().equals(delTableField.getStatus())) {
                        throw new ServiceException(StrUtil.format("[{}]字段停用状态,不能被删除", delTableField.getName()));
                    }
                    if (Constants.Y.equals(delTableField.getFtfIsSystem())) {
                        throw new ServiceException(StrUtil.format("[{}]字段是系统内置字段,不能被删除", delTableField.getName()));
                    }
                }
            }
            if (isDoDelete){
                // 真实删除
                removeByIds(delTableFields.stream().map(FormTableField::getId).toList());
            }
        }
    }

    /**
     * 通过字段id列表查询对应的表单字段
     *
     * @param showFieldIdList 字段id列表
     * @return 表单字段列表
     */
    @Override
    public List<FormTableFieldDto> getFieldByIdList(List<String> showFieldIdList) {
        if (showFieldIdList == null || showFieldIdList.isEmpty()) {
            return new ArrayList<>();
        }
        return list(new LambdaQueryWrapper<FormTableField>().in(FormTableField::getId, showFieldIdList))
                .stream()
                .map(FormTableFieldDto::coverToDto)
                .collect(Collectors.toList());
    }

    /**
     * 真实删除字段
     *
     * @param tableFieldId 字段id
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void realDeleteField(String tableFieldId) {
        FormTableField formTableField = formTableFieldMapper.selectById(tableFieldId);
        if (Objects.isNull(formTableField)) {
            throw new ServiceException("字段不存在");
        }
        String id = formTableField.getId();
        // 判断组件是否有子类
        List<FormTableField> childField = formTableFieldMapper.selectList(new LambdaQueryWrapper<FormTableField>().eq(FormTableField::getParentId, id));
        if (CollUtil.isNotEmpty(childField)) {
            throw new ServiceException("该字段有子字段，不能删除");
        }
        // 移除字段
        formTableFieldMapper.deleteById(tableFieldId);
        String formTableId = formTableField.getFormTableId();
        FormTable formTable = formTableMapper.selectById(formTableId);
        String formTableName = formTable.getTableName();
        if (Objects.equals(formTableField.getStatus(), FormTableFieldStatusEnum.PUBLISHED.getStatus())
                && UiComponentFactory.create(formTableField).isJdbcField()
        ) {
            // 移除数据库字段
            ormFactory.createORM(OrmConfig.config().tableName(formTableName)).removeTableColumn(formTableField.getName());
        }
    }

    @Override
    public FormTableFieldDto getFieldById(String tableFieldId) {
        FormTableField formTableField = formTableFieldMapper.selectById(tableFieldId);
        if (Objects.isNull(formTableField)) {
            return null;
        }
        return FormTableFieldDto.coverToDto(formTableField);
    }

    @Override
    public List<FormTableField> getSystemFiled(String formId) {
        return list(new LambdaQueryWrapper<FormTableField>()
                .eq(FormTableField::getFormId,formId)
                .eq(FormTableField::getFtfIsSystem,Constants.Y));
    }
}
