package com.rcszh.lowcode.service.view;

import com.baomidou.mybatisplus.extension.service.IService;
import com.rcszh.lowcode.entity.view.ViewForm;
import com.rcszh.lowcode.entity.view.ViewFormConfig;

import java.util.List;

public interface IViewFormConfigService extends IService<ViewFormConfig> {


    void createOrUpdateViewPageConfig(ViewForm viewForm);
    /**
     * @apiNote 创建或更新列表页配置
     */
    void createOrUpdateListPageConfig(String mainTableId,ViewForm viewForm);

    List<ViewFormConfig> findAllConfigById(String viewFormId);

    /**
     * @apiNote 条件查询对应的视图配置
     * @param viewFormType 视图配置类型
     * @param viewFormId 视图id
     * @return
     */
    List<ViewFormConfig> findConfigByViewFormTypeAndId(String viewFormType,String viewFormId);

    int updateViewFormConfig(ViewFormConfig viewFormConfig);
}
