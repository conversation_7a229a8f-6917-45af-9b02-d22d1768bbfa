package com.rcszh.lowcode.service.view.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rcszh.common.constant.Constants;
import com.rcszh.common.enums.ResultTypeEnum;
import com.rcszh.common.exception.DataFilterException;
import com.rcszh.lowcode.dto.data.SelectConditionDto;
import com.rcszh.lowcode.dto.data.SelectConditionGroupDto;
import com.rcszh.lowcode.dto.data.SelectSortDto;
import com.rcszh.lowcode.dto.view.ViewFormDto;
import com.rcszh.lowcode.entity.form.Form;
import com.rcszh.lowcode.entity.form.FormTable;
import com.rcszh.lowcode.entity.view.ViewForm;
import com.rcszh.lowcode.entity.view.ViewFormConfig;
import com.rcszh.lowcode.enums.FormTableTypeEnum;
import com.rcszh.lowcode.enums.SystemTypeEnum;
import com.rcszh.lowcode.enums.view_form.*;
import com.rcszh.lowcode.mapper.form.ViewFormMapper;
import com.rcszh.lowcode.schema.FormInfoSchema;
import com.rcszh.lowcode.schema.ViewConfigOptionsBaseSetting;
import com.rcszh.lowcode.service.view.IViewFormService;
import com.rcszh.lowcode.utils.ViewFormUtil;
import com.rcszh.base.common.exception.ServiceException;
import com.rcszh.base.common.utils.SecurityUtils;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
public class ViewFormService extends ServiceImpl<ViewFormMapper, ViewForm> implements IViewFormService {
    @Resource
    private ViewFormMapper viewFormMapper;
    @Resource
    private ViewFormUtil viewFormUtil;
    @Resource
    private ViewFormConfigService viewFormConfigService;

    /**
     * 查询表单下的所有视图配置
     */
    @Override
    public List<ViewForm> findByFormId(String formId, ViewForm viewForm) {
        LambdaQueryWrapper<ViewForm> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ViewForm::getFormId, formId);
        queryWrapper.eq(StrUtil.isNotBlank(viewForm.getType()), ViewForm::getType, viewForm.getType());
        return viewFormMapper.selectList(queryWrapper);
    }

    /**
     * 创建视图配置
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createViewFormConfig(String mainTableId,ViewForm viewForm) {
        viewFormMapper.insert(viewForm);
        String type = viewForm.getType();

        if (ViewFormTypeEnum.VIEW.getType().equals(type)) {
            // 查看视图
            viewFormConfigService.createOrUpdateViewPageConfig(viewForm);
        } else if (ViewFormTypeEnum.LIST.getType().equals(type)) {
            // 列表视图
            viewFormConfigService.createOrUpdateListPageConfig(mainTableId,viewForm);
        }
    }

    /**
     * 查询对应类型的view表单配置
     * 默认查看内置
     */
    @Override
    public ViewFormDto findConfigByType(String formId, String type) {
        ViewFormDto result = new ViewFormDto();
        // 拿到对应的视图
        ViewForm viewForm = viewFormMapper.selectOne(new LambdaQueryWrapper<ViewForm>()
                .eq(ViewForm::getFormId, formId)
                .eq(ViewForm::getType, type)
                .eq(ViewForm::getSystemType, SystemTypeEnum.BUILT_IN.getType()));
        if (Objects.isNull(viewForm)) {
            throw new ServiceException("视图不存在");
        }
        // 拿到视图对应的配置
        List<ViewFormConfig> viewFormConfigs = viewFormConfigService.findAllConfigById(viewForm.getId());
        BeanUtil.copyProperties(viewForm, result);
        result.setConfigs(viewFormConfigs);
        return result;
    }

    @Override
    public ViewFormDto getViewFormConfigById(String viewFormId) {
        ViewFormDto result = new ViewFormDto();
        // 拿到对应的视图
        ViewForm viewForm = viewFormMapper.selectById(viewFormId);
        // 拿到视图对应的配置
        List<ViewFormConfig> viewFormConfigs = viewFormConfigService.findAllConfigById(viewForm.getId());
        BeanUtil.copyProperties(viewForm, result);
        result.setConfigs(viewFormConfigs);
        return result;
    }

    /**
     * 新增视图
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addViewForm(String mainTableId,ViewForm viewForm) {
        // 用户自定义
        viewForm.setSystemType(SystemTypeEnum.CUSTOM.getType());
        // 不启用
        viewForm.setStatus(Constants.Y);
        // 创建
        createViewFormConfig(mainTableId,viewForm);
    }

    @Override
    public int deleteVIewFormById(String viewFormId) {
        ViewForm viewForm = viewFormMapper.selectById(viewFormId);
        if (viewForm == null) {
            throw new RuntimeException("视图不存在");
        }
        if (SystemTypeEnum.BUILT_IN.getType().equals(viewForm.getSystemType())) {
            throw new RuntimeException("系统内置视图，不能删除");
        }
        viewFormConfigService.remove(new LambdaQueryWrapper<ViewFormConfig>().eq(ViewFormConfig::getViewFormId, viewFormId));
        return viewFormMapper.deleteById(viewFormId);
    }

    public int deleteViewFormByFormId(String formId) {
        List<ViewForm> viewForms = viewFormMapper.selectList(new LambdaQueryWrapper<ViewForm>().eq(ViewForm::getFormId, formId));
        if (CollUtil.isEmpty(viewForms)) {
            return 0;
        }
        List<String> viewFormIds = viewForms.stream().map(ViewForm::getId).toList();
        viewFormConfigService.remove(new LambdaQueryWrapper<ViewFormConfig>().in(ViewFormConfig::getViewFormId, viewFormIds));

        return viewFormMapper.deleteBatchIds(viewFormIds);
    }


    @Override
    public void createDefaultViewForm(FormInfoSchema formInfoSchema) {
        Form form = formInfoSchema.getForm();
        List<ViewForm> viewForms = viewFormMapper.selectList(new LambdaQueryWrapper<ViewForm>().eq(ViewForm::getFormId, form.getId()));
        // 第一次发布时是没有视图配置的
        if (CollUtil.isEmpty(viewForms)) {
            // 添加查看视图
            ViewForm viewForm = viewFormUtil.createDefaultViewTypeViewForm(form);
            viewFormMapper.insert(viewForm);
            viewForms.add(viewForm);
            // 添加列表视图
            ViewForm listViewForm = viewFormUtil.createDefaultListTypeViewForm(form);
            viewFormMapper.insert(listViewForm);
            viewForms.add(listViewForm);
        }
        FormTable mainTable = formInfoSchema.getFormTables().stream()
                .filter(i -> i.getType().equals(FormTableTypeEnum.MAIN.getType())).findFirst()
                .orElseThrow(() -> new RuntimeException("主表不存在"));
        for (ViewForm viewForm : viewForms) {
            String type = viewForm.getType();
            if (ViewFormTypeEnum.VIEW.getType().equals(type)) {
                // 查看视图
                viewFormConfigService.createOrUpdateViewPageConfig(viewForm);
            } else if (ViewFormTypeEnum.LIST.getType().equals(type)) {
                // 列表视图
                viewFormConfigService.createOrUpdateListPageConfig(mainTable.getId(),viewForm);
            }
        }
    }

    @Override
    public List<SelectConditionDto> transformFieldConditions(Map<String, Object> conditions) {
        List<SelectConditionDto> result = new ArrayList<>();
        for (Map.Entry<String, Object> entry : conditions.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            try {
                // 值是对象，如：{$condition: 111}
                Map<String, Object> obj = (Map<String, Object>) value;
                for (Map.Entry<String, Object> valueEntry : obj.entrySet()) {
                    ViewFormListSelectTypeEnum selectTypeEnum = ViewFormListSelectTypeEnum.getByType(valueEntry.getKey());
                    if (Objects.isNull(selectTypeEnum)) {
                        throw new ServiceException("无效的查询条件类型： " + valueEntry.getKey());
                    }
                    SelectConditionDto selectConditionDto = new SelectConditionDto();
                    selectConditionDto.setField(key);
                    selectConditionDto.setSelectType(selectTypeEnum);
                    selectConditionDto.setValue(valueEntry.getValue());
                    result.add(selectConditionDto);
                }
            } catch (Exception e) {
                SelectConditionDto selectConditionDto = new SelectConditionDto();
                selectConditionDto.setField(key);
                selectConditionDto.setSelectType(ViewFormListSelectTypeEnum.EQ);
                selectConditionDto.setValue(value);
                result.add(selectConditionDto);
            }
        }
        return result;
    }


    private SelectConditionGroupDto transformConditionGroup(Map<String, Object> conditions) {
        SelectConditionGroupDto group = new SelectConditionGroupDto();
        List<SelectConditionDto> conditionDtos = group.getConditions();
        List<SelectConditionGroupDto> conditionGroupDtos = group.getConditionGroups();
        if (Objects.isNull(conditions)) {
            return group;
        }
        List<String> nestedTypes = Arrays.asList("$and", "$or");
        for (Map.Entry<String, Object> entry : conditions.entrySet()) {
            String key = entry.getKey();
            if (!nestedTypes.contains(key)) {
                throw new RuntimeException("条件格式错误, key: " + key);
            }
            group.setNestedType(key);

            List<Map<String, Object>> items = new ArrayList<>();
            if (!(entry.getValue() instanceof List)) {
                items.add((Map<String, Object>) entry.getValue());
            } else {
                items = (List<Map<String, Object>>) entry.getValue();
            }
            // 遍历数组
            for (Object item : items) {
                Map<String, Object> condition = (Map<String, Object>) item;
                if (condition.containsKey("$and") || condition.containsKey("$or")) {
                    conditionGroupDtos.add(this.transformConditionGroup(condition));
                } else {
                    conditionDtos.addAll(this.transformFieldConditions(condition));
                }
            }

        }
        return group;
    }

    private SelectConditionGroupDto transformSetting2Group(ViewConfigOptionsBaseSetting.FilterConditionGroup settingConditionGroup, String filterType, Map<String, Object> params) {
        SelectConditionGroupDto result = new SelectConditionGroupDto();
        if (Objects.isNull(settingConditionGroup)) {
            return result;
        }
        List<SelectConditionDto> conditionDtos = result.getConditions();
        List<SelectConditionGroupDto> conditionGroupDtos = result.getConditionGroups();
        if (CollUtil.isNotEmpty(settingConditionGroup.getConditions())) {
            for (ViewConfigOptionsBaseSetting.ICondition settingCondition : settingConditionGroup.getConditions()) {
                if (settingCondition instanceof ViewConfigOptionsBaseSetting.FilterConditionGroup) {
                    conditionGroupDtos.add(this.transformSetting2Group((ViewConfigOptionsBaseSetting.FilterConditionGroup) settingCondition, filterType, params));
                } else {
                    ViewConfigOptionsBaseSetting.FilterCondition filterCondition = (ViewConfigOptionsBaseSetting.FilterCondition) settingCondition;
                    SelectConditionDto conditionDto = new SelectConditionDto();
                    conditionDto.setField(filterCondition.getField());
                    conditionDto.setSelectType(ViewFormListSelectTypeEnum.getByType(filterCondition.getSelectType()));
                    Object value = null;
                    if ("fixed".equals(filterCondition.getValueType())) {
                        value = filterCondition.getValue();
                    } else if ("parameter".equals(filterCondition.getValueType())) {
                        value = BeanUtil.getProperty(params, filterCondition.getValue());
                    }

                    if (Objects.isNull(value)) {
                        // 参数为空时忽略查询条件
                        if ("nullIgnore".equals(filterType)) {
                            continue;
                        }
                        // 参数为空时不继续查询，抛出DataFilterException；然后返回空结果
                        if ("nullNotQuery".equals(filterType)) {
                            throw new DataFilterException(ResultTypeEnum.PAGE);
                        }
                    }
                    conditionDto.setValue(value);
                    conditionDtos.add(conditionDto);
                }
            }
        }
        return result;
    }


    @Override
    public SelectConditionGroupDto buildSelectConditionGroup(ViewForm viewForm, Map<String, Object> params, Map<String, Object> conditions, boolean baseDataFilter) {
        SelectConditionGroupDto rootConditionGroupDto = this.transformConditionGroup(conditions);

        ViewFormConfig baseSettingConfig = viewFormConfigService.getOne(new LambdaQueryWrapper<ViewFormConfig>()
                .eq(ViewFormConfig::getViewFormId, viewForm.getId())
                .eq(ViewFormConfig::getViewFormType, ViewFormTypeEnum.LIST.getType())
                .eq(ViewFormConfig::getType, ViewFormConfigTypeEnum.SETTING.getType())
        );
        if (Objects.isNull(baseSettingConfig)) {
            return rootConditionGroupDto;
        }
        if (StrUtil.isBlank(baseSettingConfig.getOptions())) {
            return rootConditionGroupDto;
        }
        if (baseDataFilter) {
            ViewConfigOptionsBaseSetting baseSetting = new ViewConfigOptionsBaseSetting();
            baseSetting.parseFromStr(baseSettingConfig.getOptions());
            ViewConfigOptionsBaseSetting.DataFilterConfig dataFilter = baseSetting.getDataFilter();
            if (Objects.isNull(dataFilter)) {
                return rootConditionGroupDto;
            }
            String flowCondition = dataFilter.getFlowCondition();
            List<SelectConditionDto> conditionDtos = rootConditionGroupDto.getConditions();
            List<SelectConditionGroupDto> conditionGroups = rootConditionGroupDto.getConditionGroups();

            // 流程数据
            if (StrUtil.isNotBlank(flowCondition)) {
                if (FlowConditionEnum.myCreated.getCode().equals(flowCondition)) {
                    SelectConditionDto selectConditionDto = new SelectConditionDto();
                    selectConditionDto.setField("creator");
                    selectConditionDto.setSelectType(ViewFormListSelectTypeEnum.EQ);
                    selectConditionDto.setValue(SecurityUtils.getUserId());
                    conditionDtos.add(selectConditionDto);
                }
                if (FlowConditionEnum.myWaiting.getCode().equals(flowCondition)) {

                }
                if (FlowConditionEnum.myApproved.getCode().equals(flowCondition)) {

                }
            }
            // 查询设置
            if (Objects.nonNull(dataFilter.getConditionGroup())) {
                SelectConditionGroupDto conditionGroupDto = this.transformSetting2Group(dataFilter.getConditionGroup(), dataFilter.getFilterType(), params);
                conditionDtos.addAll(conditionGroupDto.getConditions());
                conditionGroups.addAll(conditionGroupDto.getConditionGroups());
            }
        }
        return rootConditionGroupDto;
    }

    @Override
    public List<SelectSortDto> buildSelectSort(ViewForm viewForm, Map<String, String> sorts) {
        List<SelectSortDto> result = new ArrayList<>();
        if (Objects.isNull(sorts)) {
            return result;
        }

        for (Map.Entry<String, String> entry : sorts.entrySet()) {
            SelectSortDto selectSortDto = new SelectSortDto();
            ViewFormListSortTypeEnum viewFormListSortTypeEnum = ViewFormListSortTypeEnum.getByType(entry.getValue());
            if (Objects.isNull(viewFormListSortTypeEnum)) {
                throw new RuntimeException("无效的排序类型: " + entry.getValue());
            }
            selectSortDto.setField(entry.getKey());
            selectSortDto.setSortType(viewFormListSortTypeEnum);
            result.add(selectSortDto);
        }
        return result;
    }

    /**
     * 查询当前表单正在使用的对应类型的视图
     *
     * @param formId 表单id
     * @param type   表单类型
     * @return 视图
     */
    @Override
    public ViewForm findUsedViewForm(String formId, String type) {
        return viewFormMapper.selectOne(new LambdaQueryWrapper<ViewForm>()
                .eq(ViewForm::getFormId, formId)
                .eq(ViewForm::getType, type)
                .eq(ViewForm::getStatus, Constants.Y)
        );
    }
}
