package com.rcszh.lowcode.service.form;

import com.baomidou.mybatisplus.extension.service.IService;
import com.rcszh.lowcode.entity.dto.FormInfo;
import com.rcszh.lowcode.entity.form.FormHistory;
import com.rcszh.lowcode.schema.FormInfoSchema;

public interface IFormHistoryService extends IService<FormHistory> {
    /**
     * 根据formId获取历史form保存信息
     */
    FormHistory getOneByFormId(String formId);

    /**
     * 根据formInfo进行保存
     * 一般用于表单字段保存提交时的保存
     */
    void saveHistoryByFormInfo(FormInfo formInfo);

    void saveHistoryByFormSchema(FormInfoSchema formInfoSchema);

    /**
     * 保存
     */
    void saveOrUpdateHistory(FormHistory formHistory);

    /**
     * 根据formId保存
     */
//    void saveHistoryByFormId(String formId);
}
