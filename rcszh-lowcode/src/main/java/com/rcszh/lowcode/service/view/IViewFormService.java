package com.rcszh.lowcode.service.view;

import com.baomidou.mybatisplus.extension.service.IService;
import com.rcszh.lowcode.dto.data.SelectConditionDto;
import com.rcszh.lowcode.dto.data.SelectConditionGroupDto;
import com.rcszh.lowcode.dto.data.SelectSortDto;
import com.rcszh.lowcode.dto.view.ViewFormDto;
import com.rcszh.lowcode.entity.form.Form;
import com.rcszh.lowcode.entity.view.ViewForm;
import com.rcszh.lowcode.schema.FormInfoSchema;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

public interface IViewFormService extends IService<ViewForm> {

    List<ViewForm> findByFormId(String formId, ViewForm viewForm);

    /**
     * 创建视图配置
     */
    @Transactional(rollbackFor = Exception.class)
    void createViewFormConfig(String mainTableId,ViewForm viewForm);

    ViewFormDto findConfigByType(String formId, String type);

    ViewFormDto getViewFormConfigById(String viewFormId);

    /**
     * 新增视图配置
     */
    @Transactional(rollbackFor = Exception.class)
    void addViewForm(String mainTableId,ViewForm viewForm);

    int deleteVIewFormById(String viewFormId);

    /**
     * 首次发布时创建默认视图逻辑
     */
    void createDefaultViewForm(FormInfoSchema form);

    List<SelectConditionDto> transformFieldConditions(Map<String, Object> conditions);

    SelectConditionGroupDto buildSelectConditionGroup(ViewForm viewForm, Map<String, Object> params, Map<String, Object> selectParams, boolean baseDataFilter);

    List<SelectSortDto> buildSelectSort(ViewForm viewForm, Map<String, String> sorts);

    /**
     * 查询当前表单正在使用的对应类型的视图
     *
     * @param formId 表单id
     * @param type   表单类型
     * @return 视图
     */
    ViewForm findUsedViewForm(String formId, String type);
}
