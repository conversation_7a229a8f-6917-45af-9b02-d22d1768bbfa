package com.rcszh.lowcode.service.form.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rcszh.common.constant.Constants;
import com.rcszh.lowcode.dto.FormTableFieldDto;
import com.rcszh.lowcode.dto.form.FormTableDto;
import com.rcszh.lowcode.entity.dto.FormInfo;
import com.rcszh.lowcode.entity.form.Form;
import com.rcszh.lowcode.entity.form.FormHistory;
import com.rcszh.lowcode.entity.form.FormTable;
import com.rcszh.lowcode.mapper.form.FormHistoryMapper;
import com.rcszh.lowcode.schema.FormInfoSchema;
import com.rcszh.lowcode.service.form.IFormHistoryService;
import com.rcszh.lowcode.utils.FormUtil;
import com.rcszh.lowcode.utils.UUIDUtils;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class FormHistoryService  extends ServiceImpl<FormHistoryMapper, FormHistory>  implements IFormHistoryService {
    @Resource
    private FormHistoryMapper formHistoryMapper;
    @Resource
    private FormUtil formUtil;
//    @Resource
//    private IFormService formService;
//    @Resource
//    private IFormTableService formTableService;
//    @Resource
//    private IFormTableFieldService formTableFieldService;

    /**
     * 根据formId查询
     */
    @Override
    public FormHistory getOneByFormId(String formId) {
        return formHistoryMapper.selectOne(new LambdaQueryWrapper<FormHistory>().eq(FormHistory::getFormId, formId));
    }
    /**
     * 根据formInfo进行保存
     * 一般用于表单字段保存提交时的保存
     */
    @Override
    public void saveHistoryByFormInfo(FormInfo formInfo) {
        Form form = formInfo.getForm();
        String formJson = JSONUtil.toJsonStr(form);
        List<FormTableDto> formTables = formInfo.getFormTables();
        String formTableJson = JSONUtil.toJsonStr(formTables.stream().map(item -> {
            FormTable formTable = new FormTable();
            BeanUtil.copyProperties(item, formTable);
            return formTable;
        }).collect(Collectors.toList()));
        List<FormTableFieldDto> collect = formTables.stream().map(item -> item.getChildren() == null ? new ArrayList<FormTableFieldDto>() : item.getChildren()).flatMap(Collection::stream).collect(Collectors.toList());
        for (int i = 0; i < collect.size(); i++) {
            FormTableFieldDto item = collect.get(i);
            item.setId(item.getId() == null ? UUIDUtils.generateUUID() : item.getId());
            item.setFtfDisplay(item.getFtfDisplay() == null ?  Constants.Y : item.getFtfDisplay());
            item.setComponentOrder(item.getComponentOrder() == null ? i : item.getComponentOrder());
        }
        String formTableFieldJson = JSONUtil.toJsonStr(collect);
        FormHistory oldForm = getOneByFormId(form.getId());
        if (oldForm != null) {
            oldForm.setFormInfo(formJson);
            oldForm.setFormTableInfo(formTableJson);
            oldForm.setFormTableFiledInfo(formTableFieldJson);
            oldForm.fillBaseFields();
            formHistoryMapper.updateById(oldForm);
        }else {
            FormHistory formHistory = new FormHistory();
            formHistory.setFormId(form.getId());
            formHistory.setFormInfo(formJson);
            formHistory.setFormTableInfo(formTableJson);
            formHistory.setFormTableFiledInfo(formTableFieldJson);
            formHistory.fillBaseFields();
            formHistoryMapper.insert(formHistory);
        }
    }

    @Override
    public void saveHistoryByFormSchema(FormInfoSchema formInfoSchema) {
        Form form = formInfoSchema.getForm();
        String formId = form.getId();
        FormHistory formHistory = Optional.ofNullable(getOneByFormId(formId)).orElse(new FormHistory());
        formHistory.setFormId(formId);
        formHistory.setFormInfo(JSONUtil.toJsonStr(form));
        formHistory.setFormTableInfo(JSONUtil.toJsonStr(formInfoSchema.getFormTables()));
        formHistory.setFormTableFiledInfo(JSONUtil.toJsonStr(formInfoSchema.getFormTableFields()));
        saveOrUpdateHistory(formHistory);
    }

    @Override
    public void saveOrUpdateHistory(FormHistory formHistory) {
        formHistory.fillBaseFields();
        String formId = formHistory.getFormId();
        FormHistory oldFormHistory = formHistoryMapper.selectOne(new LambdaQueryWrapper<FormHistory>().eq(FormHistory::getFormId, formId));
        if (oldFormHistory != null) {
            BeanUtil.copyProperties(formHistory,oldFormHistory, CopyOptions.create().setIgnoreNullValue(true));
            formHistoryMapper.updateById(oldFormHistory);
        }else {
            formHistoryMapper.insert(formHistory);
        }
    }

//    @Override
//    public void saveHistoryByFormId(String formId) {
//        Form form = formService.getFormById(formId);
//        List<FormTable> formTables = formTableService.getTableByFormId(formId);
//        List<FormTableFieldDto> formFields = formTableFieldService.getFieldByFormIdToList(formId);
//        FormHistory oldForm = getOneByFormId(form.getId());
//        if (oldForm != null) {
//            oldForm.setFormInfo(JSONUtil.toJsonStr(form));
//            oldForm.setFormTableInfo(JSONUtil.toJsonStr(formTables));
//            oldForm.setFormTableFiledInfo(JSONUtil.toJsonStr(formFields));
//            oldForm.fillBaseFields();
//            formHistoryMapper.updateById(oldForm);
//        }else {
//            FormHistory formHistory = new FormHistory();
//            formHistory.setFormId(form.getId());
//            formHistory.setFormInfo(JSONUtil.toJsonStr(form));
//            formHistory.setFormTableInfo(JSONUtil.toJsonStr(formTables));
//            formHistory.setFormTableFiledInfo(JSONUtil.toJsonStr(formFields));
//            formHistory.fillBaseFields();
//            formHistoryMapper.insert(formHistory);
//        }
//    }

}
