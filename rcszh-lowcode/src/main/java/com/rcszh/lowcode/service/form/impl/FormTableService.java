package com.rcszh.lowcode.service.form.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rcszh.base.common.exception.ServiceException;
import com.rcszh.lowcode.core.enums.UiComponentTypeEnum;
import com.rcszh.lowcode.dto.form.FormTableDto;
import com.rcszh.lowcode.entity.form.FormTable;
import com.rcszh.lowcode.entity.form.FormTableField;
import com.rcszh.lowcode.enums.FormTableStatusEnum;
import com.rcszh.lowcode.enums.FormTableTypeEnum;
import com.rcszh.lowcode.mapper.FormTableFieldMapper;
import com.rcszh.lowcode.mapper.FormTableMapper;
import com.rcszh.lowcode.orm.factory.ORMFactory;
import com.rcszh.lowcode.orm.factory.OrmConfig;
import com.rcszh.lowcode.service.form.IFormTableService;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 数据表
 * 业务模型
 */
@Service
public class FormTableService extends ServiceImpl<FormTableMapper, FormTable> implements IFormTableService {
    @Resource
    private FormTableMapper formTableMapper;
    @Resource
    private FormTableFieldService formTableFieldService;

    @Autowired
    private FormTableFieldMapper formTableFieldMapper;

    @Autowired
    private ORMFactory ormFactory;


    public FormTable createChildTable(FormTable formTable) {
        Optional<FormTable> mainTable = getTableByFormId(formTable.getFormId())
                .stream()
                .filter(item -> item.getType().equals(FormTableTypeEnum.MAIN.getType())).findFirst();
        if (mainTable.isPresent()) {
            FormTable table = mainTable.get();
            FormTable newTable = initChildFormTable(formTable);
            formTableFieldService.generateChildInitFields(table, formTable.getFormId(), formTable.getId());
            return newTable;
        } else {
            throw new RuntimeException("主表不存在");
        }
    }

    /**
     * 创建数据表（首次生成表单）
     *
     * @param formTable 表单数据
     */
    @Override
    public void initMainFormTable(FormTable formTable) {
        formTableMapper.insert(formTable);
    }

    /**
     * 创建子数据表
     */
    public FormTable initChildFormTable(FormTable formTable) {
        List<FormTable> formTables = formTableMapper.selectList(new LambdaQueryWrapper<FormTable>().eq(FormTable::getFormId, formTable.getFormId()));
        String name = formTable.getName();
        String tableName = formTable.getTableName();
        if (!formTable.getType().equals(FormTableTypeEnum.CHILD.getType())) {
            throw new ServiceException("流程只能创建子表类型");
        }
        // 同一个表单下校验
        for (FormTable table : formTables) {
            if (tableName.equals(table.getTableName())) {
                throw new ServiceException("当前表单已存在相同表名的库表：" + tableName);
            } else if (name.equals(table.getName())) {
                throw new ServiceException("当前表单已存在相同名称的明细表名：" + name);
            }
        }
        // 全局查找，不能有数据库重名的情况
        FormTable oldTable = formTableMapper.selectOne(new LambdaQueryWrapper<FormTable>().eq(FormTable::getTableName, formTable.getTableName()));
        if (oldTable != null) {
            throw new RuntimeException("已存在相同名称的数据库表："+formTable.getTableName());
        }
        formTable.setStatus(FormTableStatusEnum.CREATED.getStatus());
        formTable.fillBaseFields();
        formTableMapper.insert(formTable);
        return formTable;
    }

    /**
     * 创建数据表（首次生成表单）
     *
     * @param formTable       表单数据
     * @param formTableFields
     */
    @Override
    public void createFormTable(FormTable formTable, List<FormTableField> formTableFields) {
        formTableMapper.insert(formTable);
//        ORM.orm().tableName(formTable.getTableName()).createTemplateTable(null);
    }

    /**
     * 删除业务模型
     */
    @Override
    public void deleteById(String id) {
        formTableMapper.deleteById(id);
    }

    /**
     * 更新业务模型
     */
    @Override
    public void update(FormTable formTable) {
        formTableMapper.updateById(formTable);
    }

    /**
     * 通过Id查询业务模型
     */
    @Override
    public FormTable getTableById(String id) {
        return formTableMapper.selectById(id);
    }


    public Object getAllDataSource(Long sourceId) {
//        return coreDataSourceTableMapper.selectList(new LambdaQueryWrapper<FormTable>().eq(FormTable::getCodeDataSourceId, sourceId));
        return null;
    }


    @Override
    public List<FormTable> getTableByFormId(String formId) {
        return formTableMapper.selectList(new LambdaQueryWrapper<FormTable>().eq(FormTable::getFormId, formId));
    }


    /**
     * 批量添加
     *
     * @param formTables
     */
    public void batchSaveFormTable(List<FormTable> formTables) {
        formTables.forEach(formTable -> {
            formTableMapper.insert(formTable);
        });
    }

    /**
     * 添加或者更新表
     */
    public void batchUpdateFormTable(List<FormTableDto> formTables) {
        formTables.forEach(formTableDto -> {
            FormTable formTable = formTableDto;
            String formTableId = formTable.getId();
            FormTable oldFormTable = formTableMapper.selectById(formTableId);
            if (oldFormTable == null) {
                throw new RuntimeException("业务对象表不存在");
            }
            if (!oldFormTable.getTableName().equals(formTable.getTableName())) {
                throw new RuntimeException("不能修改已创建的业务对象表信息");
            }
            if (!oldFormTable.getType().equals(formTable.getType())) {
                throw new RuntimeException("表类型无法变更");
            }
            formTableMapper.updateById(formTable);
        });
    }

    /**
     * 通过数据库表查询对应的表单库表信息
     */
    @Override
    public FormTable getFormTableByTableName(String formTableName) {
        return formTableMapper.selectOne(new LambdaQueryWrapper<FormTable>().eq(FormTable::getTableName, formTableName));
    }

    /**
     * 获取所有的表单表
     */
    @Override
    public List<FormTable> getAllFormTable() {
        return formTableMapper.selectList(null);
    }

    @Override
    public void updateDataModeling(List<FormTable> formTables, List<FormTableField> formTableFields) {
        Map<String, List<FormTableField>> formTableFieldGroup = formTableFields.stream().collect(Collectors.groupingBy(FormTableField::getFormTableId));
        String databaseName = ormFactory.getDatabaseName();
        for (FormTable formTable : formTables) {
            // 拿到需要创建的字段
            List<FormTableField> tableFields = formTableFieldGroup
                    .get(formTable.getId())
                    .stream()
                    .filter(i -> i.getStatus().equals(FormTableStatusEnum.CREATED.getStatus()) && UiComponentTypeEnum.getByType(i.getComponent()).isJdbcField())
                    .collect(Collectors.toList());
            if (tableFields.isEmpty()) {
                continue;
            }
            if (formTable.getStatus().equals(FormTableStatusEnum.CREATED.getStatus())) {
                ormFactory.createORM(OrmConfig.config().databaseName(databaseName).tableName(formTable.getTableName())).createTable(formTable, tableFields);
            } else if (formTable.getStatus().equals(FormTableStatusEnum.PUBLISHED.getStatus())) {
                ormFactory.createORM(OrmConfig.config().databaseName(databaseName).tableName(formTable.getTableName())).updateTable(formTable, tableFields);
            }
        }
    }

    @Override
    public FormTable getMainTale(String formId) {
        return getOne(new LambdaQueryWrapper<FormTable>().eq(FormTable::getFormId, formId)
                .eq(FormTable::getType, FormTableTypeEnum.MAIN.getType()));
    }

    /**
     * 根据表id查询
     * @param tableIdList 表id列表
     * @return 表列表
     */
    @Override
    public List<FormTable> getTableByTableIdList(List<String> tableIdList) {
        return list(new LambdaQueryWrapper<FormTable>().in(FormTable::getId, tableIdList));
    }
}
