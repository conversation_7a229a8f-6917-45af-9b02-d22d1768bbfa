package com.rcszh.lowcode.service.form_init;

import com.baomidou.mybatisplus.extension.service.IService;
import com.rcszh.lowcode.entity.form_init.FormInitRule;

import java.util.Map;

public interface IFormInitRuleService extends IService<FormInitRule> {
    int saveFormInitRule(FormInitRule formInitRule);

    int updateFormInitRule(FormInitRule formInitRule);

    Map<String, Object> transformData(Map<String, Object> data);

    FormInitRule getByCode(String ruleCode);

    int removeInitRuleByFormId(String formId);
}
