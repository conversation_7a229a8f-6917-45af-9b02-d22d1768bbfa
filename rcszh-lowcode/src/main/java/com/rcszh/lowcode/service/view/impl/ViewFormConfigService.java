package com.rcszh.lowcode.service.view.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rcszh.common.constant.Constants;
import com.rcszh.lowcode.entity.form.FormTableField;
import com.rcszh.lowcode.entity.view.ViewForm;
import com.rcszh.lowcode.entity.view.ViewFormConfig;
import com.rcszh.lowcode.enums.view_form.ViewConfigOptionFieldTypeEnum;
import com.rcszh.lowcode.enums.view_form.ViewFormConfigTypeEnum;
import com.rcszh.lowcode.mapper.FormTableFieldMapper;
import com.rcszh.lowcode.mapper.form.ViewFormConfigMapper;
import com.rcszh.lowcode.schema.ViewConfigOptionsBaseSetting;
import com.rcszh.lowcode.schema.ViewConfigOptionsSchema;
import com.rcszh.lowcode.service.view.IViewFormConfigService;
import com.rcszh.lowcode.service.view.config.ViewListConfigService;
import com.rcszh.lowcode.service.view.config.ViewShowConfigService;
import com.rcszh.lowcode.utils.ViewConfigUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class ViewFormConfigService extends ServiceImpl<ViewFormConfigMapper, ViewFormConfig> implements IViewFormConfigService {
    @Resource
    private ViewFormConfigMapper viewFormConfigMapper;
    @Resource
    private FormTableFieldMapper formTableFieldMapper;

    private List<ViewConfigOptionsSchema> jsonStr2OptionsItems(String options) {
        List<ViewConfigOptionsSchema> result = new ArrayList<>();
        for (Object o : JSONUtil.parseArray(options)) {
            ViewConfigOptionsSchema optionsItem = JSONUtil.toBean((JSONObject) o, ViewConfigOptionsSchema.class);
            result.add(optionsItem);
        }
        return result;
    }


    private void createOrUpdateViewFormConfigByOptions(ViewForm viewForm, ViewFormConfigTypeEnum configType, String defaultOptions) {
        ViewFormConfig viewFormConfig = viewFormConfigMapper.selectOne(new LambdaQueryWrapper<ViewFormConfig>()
                .eq(ViewFormConfig::getViewFormId, viewForm.getId())
                .eq(ViewFormConfig::getType, configType.getType())
        );
        if (viewFormConfig == null) {
            viewFormConfig = new ViewFormConfig();
            viewFormConfig.setViewFormId(viewForm.getId());
            viewFormConfig.setViewFormType(viewForm.getType());
            viewFormConfig.setType(configType.getType());
            viewFormConfig.setName(configType.getName());
            viewFormConfig.setSystemType(viewForm.getSystemType());
            viewFormConfig.setOptions(defaultOptions);
            viewFormConfig.fillBaseFields();
            viewFormConfigMapper.insert(viewFormConfig);
        }
    }

    /**
     * 创建或更新
     */
    private void createOrUpdateViewFormConfig(ViewForm viewForm, ViewFormConfigTypeEnum configType, List<ViewConfigOptionsSchema> defaultConfig) {
        ViewFormConfig viewFormConfig = viewFormConfigMapper.selectOne(new LambdaQueryWrapper<ViewFormConfig>()
                .eq(ViewFormConfig::getViewFormId, viewForm.getId())
                .eq(ViewFormConfig::getType, configType.getType())
        );
        if (viewFormConfig == null) {
            viewFormConfig = new ViewFormConfig();
            viewFormConfig.setViewFormId(viewForm.getId());
            viewFormConfig.setViewFormType(viewForm.getType());
            viewFormConfig.setType(configType.getType());
            viewFormConfig.setName(configType.getName());
            viewFormConfig.setSystemType(viewForm.getSystemType());
        } else {
            List<ViewConfigOptionsSchema> oldOptions = this.jsonStr2OptionsItems(viewFormConfig.getOptions());
            // TODO 后面换fieldId 做可以
            Map<String, ViewConfigOptionsSchema> optionsItemMap = oldOptions.stream().collect(Collectors.toMap(ViewConfigOptionsSchema::getFieldCode, item -> item));
            for (ViewConfigOptionsSchema defaultItem : defaultConfig) {
                ViewConfigOptionsSchema oldItem = optionsItemMap.get(defaultItem.getFieldCode());
                if (oldItem != null) {
                    defaultItem.setSort(oldItem.getSort());
                    defaultItem.setIsShow(oldItem.getIsShow());
                    if (StrUtil.isNotBlank(oldItem.getSortType())) {
                        defaultItem.setSortType(oldItem.getSortType());
                    }
                    if (Objects.nonNull(oldItem.getConditionGroup())) {
                        defaultItem.setConditionGroup(oldItem.getConditionGroup());
                    }
                }
            }
        }
        viewFormConfig.setOptions(JSONUtil.toJsonStr(defaultConfig));
        viewFormConfig.fillBaseFields();

        if (viewFormConfig.getId() == null) {
            viewFormConfigMapper.insert(viewFormConfig);
        } else {
            viewFormConfigMapper.updateById(viewFormConfig);
        }
    }
    private void createOrUpdateViewFormConfig4Button(ViewForm viewForm, ViewFormConfigTypeEnum configType, List<ViewConfigOptionsSchema> defaultConfig) {
        ViewFormConfig viewFormConfig = viewFormConfigMapper.selectOne(new LambdaQueryWrapper<ViewFormConfig>()
                .eq(ViewFormConfig::getViewFormId, viewForm.getId())
                .eq(ViewFormConfig::getType, configType.getType())
        );
        if (viewFormConfig == null) {
            viewFormConfig = new ViewFormConfig();
            viewFormConfig.setViewFormId(viewForm.getId());
            viewFormConfig.setViewFormType(viewForm.getType());
            viewFormConfig.setType(configType.getType());
            viewFormConfig.setName(configType.getName());
            viewFormConfig.setSystemType(viewForm.getSystemType());
        } else {
            List<ViewConfigOptionsSchema> oldOptions = this.jsonStr2OptionsItems(viewFormConfig.getOptions());
            // TODO 后面换fieldId 做可以
            Map<String, ViewConfigOptionsSchema> optionsItemMap = oldOptions.stream().collect(Collectors.toMap(ViewConfigOptionsSchema::getButtonType, item -> item));
            for (ViewConfigOptionsSchema defaultItem : defaultConfig) {
                ViewConfigOptionsSchema oldItem = optionsItemMap.get(defaultItem.getButtonType());
                if (oldItem != null) {
                    defaultItem.setSort(oldItem.getSort());
                    defaultItem.setIsShow(oldItem.getIsShow());
                    if (StrUtil.isNotBlank(oldItem.getSortType())) {
                        defaultItem.setSortType(oldItem.getSortType());
                    }
                    if (Objects.nonNull(oldItem.getConditionGroup())) {
                        defaultItem.setConditionGroup(oldItem.getConditionGroup());
                    }
                }
            }
        }
        viewFormConfig.setOptions(JSONUtil.toJsonStr(defaultConfig));
        viewFormConfig.fillBaseFields();

        if (viewFormConfig.getId() == null) {
            viewFormConfigMapper.insert(viewFormConfig);
        } else {
            viewFormConfigMapper.updateById(viewFormConfig);
        }
    }

    private void createViewFormConfigIfNotExists(ViewForm viewForm, ViewFormConfigTypeEnum configType) {
        ViewFormConfig viewFormConfig = viewFormConfigMapper.selectOne(new LambdaQueryWrapper<ViewFormConfig>()
                .eq(ViewFormConfig::getViewFormId, viewForm.getId())
                .eq(ViewFormConfig::getType, configType.getType())
        );
        if (viewFormConfig == null) {
            viewFormConfig = new ViewFormConfig();
            viewFormConfig.setViewFormId(viewForm.getId());
            viewFormConfig.setViewFormType(viewForm.getType());
            viewFormConfig.setType(configType.getType());
            viewFormConfig.setName(configType.getName());
            viewFormConfig.setSystemType(viewForm.getSystemType());
            viewFormConfig.setOptions(JSONUtil.toJsonStr(new ArrayList<>()));
            viewFormConfig.fillBaseFields();
            viewFormConfigMapper.insert(viewFormConfig);
        }
    }

    /**
     * 首次创建查看视图配置
     */
    @Override
    public void createOrUpdateViewPageConfig(ViewForm viewForm) {
        // 新增按钮配置
        List<ViewConfigOptionsSchema> defaultConfig = ViewConfigUtil.getViewFormViewButtonDefaultConfig();
        this.createOrUpdateViewFormConfig4Button(viewForm, ViewFormConfigTypeEnum.BUTTON, defaultConfig);
    }

    /**
     * 首次创建列表视图配置
     * 首次创建会生成的配置有：列表渲染配置、列表按钮配置
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createOrUpdateListPageConfig(String mainTableId,ViewForm viewForm) {
        // 只有在数据库中存在的字段才会显示在列表中
        // 并且只要主表字段
        List<FormTableField> formTableFields = formTableFieldMapper.selectList(new LambdaQueryWrapper<FormTableField>()
                .eq(FormTableField::getFormId, viewForm.getFormId())
                .eq(FormTableField::getFormTableId, mainTableId)
                .isNotNull(FormTableField::getJdbcType)
        );
        // 查询视图的配置
        Map<String, ViewFormConfig> viewConfigMap = viewFormConfigMapper.selectList(new LambdaQueryWrapper<ViewFormConfig>()
                .eq(ViewFormConfig::getViewFormId, viewForm.getId())
        ).stream().collect(Collectors.toMap(ViewFormConfig::getType, i -> i));
        List<ViewFormConfig> configs = new ArrayList<>();
        // 列表显示配置
        configs.add(ViewShowConfigService.createOrUpdateShowViewConfig(viewForm,
                viewConfigMap.get(ViewFormConfigTypeEnum.SHOW.getType()), ViewFormConfigTypeEnum.SHOW));
        // 列表按钮配置
        List<ViewConfigOptionsSchema> buttonList = ViewConfigUtil.getViewFormListButtonDefaultConfig();
        this.createOrUpdateViewFormConfig4Button(viewForm, ViewFormConfigTypeEnum.BUTTON, buttonList);
        // 排序配置
        List<ViewConfigOptionsSchema> sortDefaultConfig = ViewConfigUtil.getViewFormListSortDefaultConfig(formTableFields);
        this.createOrUpdateViewFormConfig(viewForm, ViewFormConfigTypeEnum.SORT, sortDefaultConfig);
        // 查询配置
        this.createViewFormConfigIfNotExists(viewForm, ViewFormConfigTypeEnum.SELECT);
        // 基础配置
        ViewConfigOptionsBaseSetting baseSetting = new ViewConfigOptionsBaseSetting();
        this.createOrUpdateViewFormConfigByOptions(viewForm, ViewFormConfigTypeEnum.SETTING, JSONUtil.toJsonStr(baseSetting));
        saveOrUpdateBatch(configs);
    }

    /**
     * 通过条件查询对应视图的所有配置
     */
    @Override
    public List<ViewFormConfig> findAllConfigById(String viewFormId) {
        return findConfigByViewFormTypeAndId(null, viewFormId);
    }

    /**
     * @param viewFormType 视图配置类型
     * @param viewFormId 视图id
     * @return
     */
    @Override
    public List<ViewFormConfig> findConfigByViewFormTypeAndId(String viewFormType, String viewFormId) {
        // 原本存在于数据库中的配置
        List<ViewFormConfig> exitConfig = viewFormConfigMapper.selectList(new LambdaQueryWrapper<ViewFormConfig>()
                .eq(ViewFormConfig::getViewFormId, viewFormId)
                .eq(viewFormType != null, ViewFormConfig::getViewFormType, viewFormType)
        );
        // 由于后续会添加一系列的配置，这里需要将原本不存在的配置也添加到列表中
        // TODO 原来的配置方式是不合理的，以后新加的配置，岂不是都要用这种方式弥补
        return exitConfig.stream().peek(config -> {
            String type = config.getType();
            if (!type.equals(ViewFormConfigTypeEnum.BUTTON.getType())) {
                return;
            }
            String options = config.getOptions();
            List<ViewConfigOptionsSchema> list = JSONUtil.parseArray(options).toList(ViewConfigOptionsSchema.class);
            if (list.stream().noneMatch(item -> item.getButtonType().equals("export"))) {
                ViewConfigOptionsSchema exportButton = new ViewConfigOptionsSchema();
                exportButton.setButtonType("export");
                exportButton.setButtonName("导出");
                exportButton.setIsShow(Constants.N);
                exportButton.setSort(list.size() + 1);
                list.add(exportButton);
            }
            config.setOptions(JSONUtil.toJsonStr(list));
        }).collect(Collectors.toList());
    }

    /**
     * 更新对应的配置
     */
    @Override
    public int updateViewFormConfig(ViewFormConfig viewFormConfig) {
        return viewFormConfigMapper.updateById(viewFormConfig);
    }
}
