package com.rcszh.lowcode.service.excel.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rcszh.lowcode.entity.excel.ExportConfig;
import com.rcszh.lowcode.mapper.excel.ExportConfigMapper;
import com.rcszh.lowcode.service.excel.IExportConfigService;
import org.springframework.stereotype.Service;

@Service
public class ExportConfigService extends ServiceImpl<ExportConfigMapper, ExportConfig> implements IExportConfigService {
    // 可扩展自定义业务逻辑
} 