package com.rcszh.lowcode.service.view.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rcszh.lowcode.entity.view.ViewFormConnect;
import com.rcszh.lowcode.mapper.ViewFormConnectMapper;
import com.rcszh.lowcode.service.view.IViewFormConnectService;
import org.springframework.stereotype.Service;

@Service
public class ViewFormConnectService extends ServiceImpl<ViewFormConnectMapper, ViewFormConnect> implements IViewFormConnectService {
}
