package com.rcszh.lowcode.handler.data_source;


import com.rcszh.base.common.core.domain.AjaxResult;
import com.rcszh.lowcode.dto.data_source.DataSourceConfigDto;
import com.rcszh.lowcode.entity.data_source.CoreDataSource;
import com.rcszh.lowcode.service.data_source.CoreDataSourceConfigService;
import com.rcszh.lowcode.service.data_source.CoreDataSourceService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/data_source")
public class CoreDataSourceController {
    @Resource
    private CoreDataSourceService coreDataSourceService;
    @Resource
    private CoreDataSourceConfigService coreDataSourceConfigService;


    @ResponseBody
    @GetMapping("/list")
    public AjaxResult datasourceList(){
        return AjaxResult.success(coreDataSourceService.getAllDataSource());
    }

    @ResponseBody
    @PostMapping("/save")
    public AjaxResult saveDatasource(@RequestBody CoreDataSource coreDataSource){
        coreDataSourceService.createOneDataSource(coreDataSource);
        return AjaxResult.success();
    }

    @ResponseBody
    @GetMapping("/config/list")
    public AjaxResult datasourceConfigList(){
        return AjaxResult.success(coreDataSourceConfigService.getAllConfig());
    }

    @ResponseBody
    @PostMapping("/config/save")
    public AjaxResult saveDatasourceConfig(@RequestBody DataSourceConfigDto dataSourceConfigDto){
        coreDataSourceConfigService.createConfig(dataSourceConfigDto.getConfig(),dataSourceConfigDto.getDataSourceIdList());
        return AjaxResult.success();
    }

}
