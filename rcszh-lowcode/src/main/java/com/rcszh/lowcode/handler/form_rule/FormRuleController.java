package com.rcszh.lowcode.handler.form_rule;


import com.rcszh.base.common.core.domain.AjaxResult;
import com.rcszh.lowcode.entity.form_rule.FormRule;
import com.rcszh.lowcode.service.form_rule.FormRuleService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/form/rule")
public class FormRuleController {
    @Resource
    private FormRuleService formRuleService;

    /**
     * 表单列表
     */
    @ResponseBody
    @GetMapping("/all/{type}/{formId}")
    public AjaxResult all(@PathVariable String type, @PathVariable String formId) {
        return AjaxResult.success(formRuleService.getAllRuleByType(formId, type));
    }

    /**
     * 表单列表
     */
    @ResponseBody
    @PostMapping("/add/{type}")
    public AjaxResult add(@PathVariable String type,@RequestBody FormRule formRule) {
        formRuleService.addRule(type,formRule);
        return AjaxResult.success();
    }
}
