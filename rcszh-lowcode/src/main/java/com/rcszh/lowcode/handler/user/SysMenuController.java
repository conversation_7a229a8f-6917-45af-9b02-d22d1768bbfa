package com.rcszh.lowcode.handler.user;//package com.rcszh.lowcode.admin.controller;
//
//import com.ruoyi.common.annotation.Log;
//import com.ruoyi.common.constant.UserConstants;
//import com.ruoyi.common.core.controller.BaseController;
//import com.ruoyi.common.core.domain.AjaxResult;
//import com.ruoyi.common.core.domain.entity.SysMenu;
//import com.ruoyi.common.enums.BusinessType;
//import com.ruoyi.common.utils.StringUtils;
//import com.ruoyi.basic.service.ISysMenuService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.security.access.prepost.PreAuthorize;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.List;
//
///**
// * 菜单信息
// *
// * <AUTHOR>
// */
//@RestController
//@RequestMapping("/basic/menu")
//public class SysMenuController extends BaseController
//{
//    @Autowired
//    private ISysMenuService menuService;
//
//    /**
//     * 获取菜单列表
//     */
//    @PreAuthorize("@ss.hasPermi('basic:menu:list')")
//    @GetMapping("/list")
//    public AjaxResult list(SysMenu menu)
//    {
//        List<SysMenu> menus = menuService.selectMenuList(menu, getUserId());
//        return success(menus);
//    }
//
//    /**
//     * 根据菜单编号获取详细信息
//     */
//    @PreAuthorize("@ss.hasPermi('basic:menu:query')")
//    @GetMapping(value = "/{menuId}")
//    public AjaxResult getInfo(@PathVariable Long menuId)
//    {
//        return success(menuService.selectMenuById(menuId));
//    }
//
//    /**
//     * 获取菜单下拉树列表
//     */
//    @GetMapping("/treeselect")
//    public AjaxResult treeselect(SysMenu menu)
//    {
//        List<SysMenu> menus = menuService.selectMenuList(menu, getUserId());
//        return success(menuService.buildMenuTreeSelect(menus));
//    }
//
//    /**
//     * 加载对应角色菜单列表树
//     */
//    @GetMapping(value = "/roleMenuTreeselect/{roleId}")
//    public AjaxResult roleMenuTreeselect(@PathVariable("roleId") Long roleId)
//    {
//        List<SysMenu> menus = menuService.selectMenuList(getUserId());
//        AjaxResult ajax = AjaxResult.success();
//        ajax.put("checkedKeys", menuService.selectMenuListByRoleId(roleId));
//        ajax.put("menus", menuService.buildMenuTreeSelect(menus));
//        return ajax;
//    }
//
//    /**
//     * 新增菜单
//     */
//    @PreAuthorize("@ss.hasPermi('basic:menu:add')")
//    @Log(title = "菜单管理", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult add(@Validated @RequestBody SysMenu menu)
//    {
//        if (!menuService.checkMenuNameUnique(menu))
//        {
//            return error("新增菜单'" + menu.getMenuName() + "'失败，菜单名称已存在");
//        }
//        else if (UserConstants.YES_FRAME.equals(menu.getIsFrame()) && !StringUtils.ishttp(menu.getPath()))
//        {
//            return error("新增菜单'" + menu.getMenuName() + "'失败，地址必须以http(s)://开头");
//        }
//        menu.setCreateBy(getUsername());
//        return toAjax(menuService.insertMenu(menu));
//    }
//
//    /**
//     * 修改菜单
//     */
//    @PreAuthorize("@ss.hasPermi('basic:menu:edit')")
//    @Log(title = "菜单管理", businessType = BusinessType.UPDATE)
//    @PutMapping
//    public AjaxResult edit(@Validated @RequestBody SysMenu menu)
//    {
//        if (!menuService.checkMenuNameUnique(menu))
//        {
//            return error("修改菜单'" + menu.getMenuName() + "'失败，菜单名称已存在");
//        }
//        else if (UserConstants.YES_FRAME.equals(menu.getIsFrame()) && !StringUtils.ishttp(menu.getPath()))
//        {
//            return error("修改菜单'" + menu.getMenuName() + "'失败，地址必须以http(s)://开头");
//        }
//        else if (menu.getMenuId().equals(menu.getParentId()))
//        {
//            return error("修改菜单'" + menu.getMenuName() + "'失败，上级菜单不能选择自己");
//        }
//        menu.setUpdateBy(getUsername());
//        return toAjax(menuService.updateMenu(menu));
//    }
//
//    /**
//     * 删除菜单
//     */
//    @PreAuthorize("@ss.hasPermi('basic:menu:remove')")
//    @Log(title = "菜单管理", businessType = BusinessType.DELETE)
//    @DeleteMapping("/{menuId}")
//    public AjaxResult remove(@PathVariable("menuId") Long menuId)
//    {
//        if (menuService.hasChildByMenuId(menuId))
//        {
//            return warn("存在子菜单,不允许删除");
//        }
//        if (menuService.checkMenuExistRole(menuId))
//        {
//            return warn("菜单已分配,不允许删除");
//        }
//        return toAjax(menuService.deleteMenuById(menuId));
//    }
//}