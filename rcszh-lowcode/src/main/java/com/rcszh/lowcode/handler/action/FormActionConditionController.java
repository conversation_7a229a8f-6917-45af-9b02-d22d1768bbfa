package com.rcszh.lowcode.handler.action;


import com.rcszh.base.common.core.domain.AjaxResult;
import com.rcszh.lowcode.entity.dto.FormActionInfo;
import com.rcszh.lowcode.entity.form.Form;
import com.rcszh.lowcode.enums.form.FormStatusEnum;
import com.rcszh.lowcode.service.AdminActionService;
import com.rcszh.lowcode.service.form.impl.FormService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/form/action/condition")
public class FormActionConditionController {
    @Resource
    private AdminActionService actionService;
    @Resource
    private FormService formService;
    /**
     * 添加
     */
    @PostMapping("/add")
    public AjaxResult add(@RequestBody FormActionInfo formActionInfo) {
        actionService.addFormConditionByFirst(formActionInfo);
        return AjaxResult.success();
    }
    /**
     * 添加
     */
    @GetMapping("/all/{formId}")
    public AjaxResult all(@PathVariable String formId) {
        Form form =  formService.getFormById(formId);
        if (!FormStatusEnum.PUBLISH.getStatus().equals(form.getFormStatus())){
            throw new RuntimeException("当前表单未发布，无法进行动作配置");
        }
        return AjaxResult.success(actionService.getConditionListByFormId(formId));
    }
}
