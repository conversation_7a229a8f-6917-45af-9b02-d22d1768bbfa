package com.rcszh.lowcode.handler.show;


import com.rcszh.base.common.annotation.Log;
import com.rcszh.base.common.core.domain.AjaxResult;
import com.rcszh.base.common.enums.BusinessType;
import com.rcszh.base.common.utils.SecurityUtils;
import com.rcszh.common.annotation.Lock;
import com.rcszh.lowcode.dto.data.*;
import com.rcszh.lowcode.dto.excel.ExportExcelParam;
import com.rcszh.lowcode.orm.entity.PageResult;
import com.rcszh.lowcode.service.FormDataBusinessService;
import com.rcszh.lowcode.service.view.impl.ViewFormService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 展示层控制器
 */
@RestController
@RequestMapping("/modeling/data")
public class ShowController {
    @Resource
    private ViewFormService viewFormService;
    @Resource
    private FormDataBusinessService formDataBusinessService;


    /**
     * 查看当前表单的对应类型的渲染配置
     */
    @GetMapping("/config/{formId}/{type}")
    public AjaxResult show(@PathVariable String formId, @PathVariable String type) {
        return AjaxResult.success(viewFormService.findConfigByType(formId, type));
    }

    @GetMapping("/init/{formId}")
    public AjaxResult init(@PathVariable String formId, @RequestParam Map<String, Object> params) {
        return AjaxResult.success(formDataBusinessService.initFormData(formId, params));
    }

    /**
     * 查询数据
     *
     * @param modelingDataListReqDto 条件
     */
    @PostMapping("/list")
    public PageResult<Map<String, Object>> list(@RequestBody ModelingDataListReqDto modelingDataListReqDto) {
        modelingDataListReqDto.setBaseDataFilter(true);
        return formDataBusinessService.pageFormData(modelingDataListReqDto);
    }

    /**
     * 查询子表数据
     */
    @PostMapping("/child/list")
    public AjaxResult childList(@RequestBody ModelingDataListReqDto modelingDataListReqDto) {
        return AjaxResult.success(formDataBusinessService.pageFormChildData(modelingDataListReqDto));
    }

    @PostMapping("/componentList")
    public PageResult<Map<String, Object>> componentList(@RequestBody ModelingDataListReqDto modelingDataListReqDto) {
        modelingDataListReqDto.setBaseDataFilter(false);
        return formDataBusinessService.pageFormData(modelingDataListReqDto);
    }


    @Log(title = "表单数据", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @Lock(userFlag = true)
    public AjaxResult add(@RequestBody ModelingDataReqDto modelingDataReqDto) {
        modelingDataReqDto.setOperatorId(SecurityUtils.getUserId());
        formDataBusinessService.saveFormData(modelingDataReqDto);
        return AjaxResult.success();
    }

    @Log(title = "表单数据", businessType = BusinessType.DELETE)
    @PostMapping("/del")
    @Lock(userFlag = true)
    public AjaxResult del(@RequestBody ModelingDataDeleteReqDto modelingDataDeleteReqDto) {
        formDataBusinessService.deleteFormDataByIds(modelingDataDeleteReqDto.getFormId(), modelingDataDeleteReqDto.getRowIds());
        return AjaxResult.success();
    }


    @PostMapping("/view")
    public AjaxResult view(@RequestBody ModelingDataViewReqDto modelingDataViewReqDto) {
        formDataBusinessService.checkOrderKeyLegality(modelingDataViewReqDto.getRowId(), modelingDataViewReqDto.getKey());
        return AjaxResult.success(formDataBusinessService.getFormDataViewInfo(modelingDataViewReqDto));
    }


    @Log(title = "表单数据", businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    @Lock(userFlag = true)
    public AjaxResult update(@RequestBody ModelingDataReqDto modelingDataReqDto) {
        modelingDataReqDto.setOperatorId(SecurityUtils.getUserId());
        formDataBusinessService.saveFormData(modelingDataReqDto);
        return AjaxResult.success();
    }

    /**
     * 提交
     */
    @Log(title = "表单数据", businessType = BusinessType.OTHER)
    @PostMapping("/submit")
    @Lock(userFlag = true)
    public AjaxResult submit(@RequestBody ModelingDataReqDto modelingDataReqDto) {
        modelingDataReqDto.setOperatorId(SecurityUtils.getUserId());
        formDataBusinessService.saveFormData(modelingDataReqDto);
        return AjaxResult.success();
    }

    /**
     * 删除垃圾审批数据 慎重使用此接口
     */
//    @DeleteMapping("/deleteRubbishData")
//    public AjaxResult deleteRubbishData() {
//        formDataBusinessService.deleteRubbishData();
//        return AjaxResult.success();
//    }

    /**
     * 动态导出
     */
    @Log(title = "表单数据", businessType = BusinessType.EXPORT)
    @PostMapping("/exportListData")
    public void exportData(HttpServletResponse response, @RequestBody ExportExcelParam dataListReqDto) {
        formDataBusinessService.exportListData(response, dataListReqDto);
    }

//    @PostMapping("/exportListData")
//    public void exportData(HttpServletResponse response, Object param) {
//        ModelingDataListReqDto dataListReqDto = JSONUtil.parse(param).toBean(ModelingDataListReqDto.class);
//        formDataBusinessService.exportListData(response,dataListReqDto);
//    }
}
