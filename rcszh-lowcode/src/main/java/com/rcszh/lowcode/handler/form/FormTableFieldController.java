package com.rcszh.lowcode.handler.form;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.rcszh.base.common.core.controller.BaseController;
import com.rcszh.base.common.core.domain.AjaxResult;
import com.rcszh.lowcode.core.enums.UiComponentTypeEnum;
import com.rcszh.lowcode.entity.form.FormTable;
import com.rcszh.lowcode.entity.form.FormTableField;
import com.rcszh.lowcode.enums.FormTableTypeEnum;
import com.rcszh.lowcode.enums.view_form.ViewConfigOptionFieldTypeEnum;
import com.rcszh.lowcode.schema.ViewConfigOptionsSchema;
import com.rcszh.lowcode.service.form.IFormTableFieldService;
import com.rcszh.lowcode.service.form.IFormTableService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/form/tableField")
public class FormTableFieldController extends BaseController {
    @Resource
    private IFormTableFieldService formTableFieldService;
    @Resource
    private IFormTableService formTableService;

    @GetMapping("/list")
    public AjaxResult list(FormTableField formTableField) {
        LambdaQueryWrapper<FormTableField> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(formTableField.getFormId()), FormTableField::getFormId, formTableField.getFormId());
        queryWrapper.eq(StrUtil.isNotBlank(formTableField.getFormTableId()), FormTableField::getFormTableId, formTableField.getFormTableId());
        queryWrapper.like(StrUtil.isNotBlank(formTableField.getName()), FormTableField::getName, formTableField.getName());
        return AjaxResult.success(formTableFieldService.list(queryWrapper));
    }

    /**
     * 真实删除字段
     * @param tableFieldId 字段ID
     * @return 结果
     */
    @DeleteMapping("/realDel/{tableFieldId}")
    public AjaxResult realDeleteField(@PathVariable String tableFieldId) {
        formTableFieldService.realDeleteField(tableFieldId);
        return AjaxResult.success();
    }

    /**
     * 获取系统内置字段
     */
    @GetMapping("/system/list")
    public AjaxResult systemList(FormTableField formTableField) {
        LambdaQueryWrapper<FormTableField> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(formTableField.getFormId()), FormTableField::getFormId, formTableField.getFormId());
        queryWrapper.eq(StrUtil.isNotBlank(formTableField.getFormTableId()), FormTableField::getFormTableId, formTableField.getFormTableId());
        queryWrapper.like(StrUtil.isNotBlank(formTableField.getName()), FormTableField::getName, formTableField.getName());
        queryWrapper.eq(FormTableField::getFtfIsSystem, Constants.YES);
        return AjaxResult.success(formTableFieldService.list(queryWrapper));
    }

    /**
     * 获取字段配置列表
     * @param formTableField 表单字段
     * @return 结果
     */
    @GetMapping("/showConfig/list")
    public AjaxResult jdbcFieldList(FormTableField formTableField) {
        String formId = formTableField.getFormId();
        if (StrUtil.isBlank(formId)) {
            return AjaxResult.error("表单ID不能为空");
        }
        Map<String, List<FormTableField>> tableMap = formTableFieldService.list(
                new LambdaQueryWrapper<FormTableField>()
                        .eq(FormTableField::getFormId, formId)
                        .isNotNull(FormTableField::getJdbcType)
        ).stream().collect(Collectors.groupingBy(FormTableField::getFormTableId));
        List<FormTable> formTables = formTableService.getTableByFormId(formId);
        List<ViewConfigOptionsSchema> resultList = new ArrayList<>();
        for (String tableId : tableMap.keySet()) {
            FormTable formTable = formTables.stream()
                    .filter(i -> i.getId().equals(tableId))
                    .findFirst()
                    .orElse(null);
            
            if (formTable == null) {
                continue; // 跳过找不到的表
            }
            List<FormTableField> list = tableMap.get(tableId);
            if (formTable.getType().equals(FormTableTypeEnum.MAIN.getType())){
                for (FormTableField field : list) {
                    ViewConfigOptionsSchema schema = new ViewConfigOptionsSchema();
                    schema.setFieldId(field.getId());
                    schema.setFieldName(field.getLabel());
                    schema.setFieldCode(field.getName());
                    schema.setFieldType(ViewConfigOptionFieldTypeEnum.FIELD.getType());
                    resultList.add(schema);
                }
            } else {
                ViewConfigOptionsSchema schema = new ViewConfigOptionsSchema();
                schema.setFieldType(ViewConfigOptionFieldTypeEnum.TABLE.getType());
                schema.setFieldId(tableId);
                schema.setFieldName(formTable.getName());
                schema.setFieldCode(formTable.getTableName());
                for (FormTableField field : list) {
                    ViewConfigOptionsSchema child = new ViewConfigOptionsSchema();
                    child.setFieldId(field.getId());
                    child.setFieldName(field.getLabel());
                    child.setFieldCode(field.getName());
                    child.setFieldType(ViewConfigOptionFieldTypeEnum.FIELD.getType());
                    schema.getChildren().add(child);
                }
                resultList.add(schema);
            }
        } 
        return AjaxResult.success(resultList);
    }
}
