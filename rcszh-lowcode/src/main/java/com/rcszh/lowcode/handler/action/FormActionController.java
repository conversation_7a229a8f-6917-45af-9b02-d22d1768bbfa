package com.rcszh.lowcode.handler.action;


import com.rcszh.base.common.core.domain.AjaxResult;
import com.rcszh.lowcode.dto.action.FormActionCreateDto;
import com.rcszh.lowcode.service.AdminActionService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/form/action")
public class FormActionController {
    @Resource
    private AdminActionService actionService;
    /**
     * 添加动作
     */
    @PostMapping("/add")
    public AjaxResult add(@RequestBody FormActionCreateDto formAction) {
        actionService.addFormAction(formAction);
        return AjaxResult.success();
    }
}
