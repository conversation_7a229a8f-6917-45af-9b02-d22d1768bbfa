package com.rcszh.lowcode.handler.form;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rcszh.base.common.core.domain.AjaxResult;
import com.rcszh.lowcode.dto.FormTableFieldDto;
import com.rcszh.lowcode.dto.form.TableInfo;
import com.rcszh.lowcode.entity.form.FormTable;
import com.rcszh.lowcode.service.form.impl.FormHistoryService;
import com.rcszh.lowcode.service.form.impl.FormService;
import com.rcszh.lowcode.service.form.impl.FormTableFieldService;
import com.rcszh.lowcode.service.form.impl.FormTableService;
import jakarta.annotation.Resource;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping("/form/table")
public class FormTableController {
    @Resource
    private FormService formService;
    @Resource
    private FormHistoryService formHistoryService;
    @Resource
    private FormTableService formTableService;
    @Resource
    private FormTableFieldService formTableFieldService;
    /**
     * 表单列表
     */
    @ResponseBody
    @GetMapping("/all")
    public AjaxResult all() {
        return AjaxResult.success(formTableService.getAllFormTable());
    }

    @GetMapping("/list")
    public AjaxResult list(FormTable formTable) {
        LambdaQueryWrapper<FormTable> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(formTable.getFormId()), FormTable::getFormId, formTable.getFormId());
        queryWrapper.eq(StrUtil.isNotBlank(formTable.getType()), FormTable::getType, formTable.getType());
        queryWrapper.eq(StrUtil.isNotBlank(formTable.getTableName()), FormTable::getTableName, formTable.getTableName());
        queryWrapper.like(StrUtil.isNotBlank(formTable.getName()), FormTable::getName, formTable.getName());
        return AjaxResult.success(formTableService.list(queryWrapper));
    }

    /**
     * 表单列表
     */
    @ResponseBody
    @GetMapping("/get/{tableId}")
    public AjaxResult getTableInfo(@PathVariable String tableId) {
        TableInfo tableInfo = new TableInfo();
        FormTable table = formTableService.getTableById(tableId);
        tableInfo.setTable(table);
        List<FormTableFieldDto> fields = formTableFieldService.getFieldByTable(tableId);
        tableInfo.setFields(fields);
        return AjaxResult.success(tableInfo);
    }

    /**
     * 创建表
     * 这种表的创建一般是明细表
     */
    @ResponseBody
    @PostMapping("/createChildTable")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult create(@RequestBody FormTable formTable){
        formTableService.createChildTable(formTable);
        return AjaxResult.success();
    }

}
