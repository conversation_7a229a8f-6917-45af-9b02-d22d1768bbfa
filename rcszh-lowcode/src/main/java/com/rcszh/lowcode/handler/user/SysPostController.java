package com.rcszh.lowcode.handler.user;//package com.rcszh.lowcode.admin.controller;
//
//import com.ruoyi.common.annotation.Log;
//import com.ruoyi.common.core.controller.BaseController;
//import com.ruoyi.common.core.domain.AjaxResult;
//import com.ruoyi.common.core.page.TableDataInfo;
//import com.ruoyi.common.enums.BusinessType;
//import com.ruoyi.common.utils.poi.ExcelUtil;
//import com.ruoyi.basic.domain.SysPost;
//import com.ruoyi.basic.service.ISysPostService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.security.access.prepost.PreAuthorize;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.*;
//
//import javax.servlet.http.HttpServletResponse;
//import java.util.List;
//
///**
// * 岗位信息操作处理
// *
// * <AUTHOR>
// */
//@RestController
//@RequestMapping("/basic/post")
//public class SysPostController extends BaseController
//{
//    @Autowired
//    private ISysPostService postService;
//
//    /**
//     * 获取岗位列表
//     */
//    @PreAuthorize("@ss.hasPermi('basic:post:list')")
//    @GetMapping("/list")
//    public TableDataInfo list(SysPost post)
//    {
//        notSupAdminSetTenant(post);
//        startPage();
//        List<SysPost> list = postService.selectPostList(post);
//        return getDataTable(list);
//    }
//
//    @Log(title = "岗位管理", businessType = BusinessType.EXPORT)
//    @PreAuthorize("@ss.hasPermi('basic:post:export')")
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, SysPost post)
//    {
//        List<SysPost> list = postService.selectPostList(post);
//        ExcelUtil<SysPost> util = new ExcelUtil<SysPost>(SysPost.class);
//        util.exportExcel(response, list, "岗位数据");
//    }
//
//    /**
//     * 根据岗位编号获取详细信息
//     */
//    @PreAuthorize("@ss.hasPermi('basic:post:query')")
//    @GetMapping(value = "/{postId}")
//    public AjaxResult getInfo(@PathVariable Long postId)
//    {
//        return success(postService.selectPostById(postId));
//    }
//
//    /**
//     * 新增岗位
//     */
//    @PreAuthorize("@ss.hasPermi('basic:post:add')")
//    @Log(title = "岗位管理", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult add(@Validated @RequestBody SysPost post)
//    {
//        if (!postService.checkPostNameUnique(post))
//        {
//            return error("新增岗位'" + post.getPostName() + "'失败，岗位名称已存在");
//        }
//        else if (!postService.checkPostCodeUnique(post))
//        {
//            return error("新增岗位'" + post.getPostName() + "'失败，岗位编码已存在");
//        }
//        post.setCreateBy(getUsername());
//        post.setTenantId(getTenantId());
//        return toAjax(postService.insertPost(post));
//    }
//
//    /**
//     * 修改岗位
//     */
//    @PreAuthorize("@ss.hasPermi('basic:post:edit')")
//    @Log(title = "岗位管理", businessType = BusinessType.UPDATE)
//    @PutMapping
//    public AjaxResult edit(@Validated @RequestBody SysPost post)
//    {
//        if (!postService.checkPostNameUnique(post))
//        {
//            return error("修改岗位'" + post.getPostName() + "'失败，岗位名称已存在");
//        }
//        else if (!postService.checkPostCodeUnique(post))
//        {
//            return error("修改岗位'" + post.getPostName() + "'失败，岗位编码已存在");
//        }
//        post.setUpdateBy(getUsername());
//        return toAjax(postService.updatePost(post));
//    }
//
//    /**
//     * 删除岗位
//     */
//    @PreAuthorize("@ss.hasPermi('basic:post:remove')")
//    @Log(title = "岗位管理", businessType = BusinessType.DELETE)
//    @DeleteMapping("/{postIds}")
//    public AjaxResult remove(@PathVariable Long[] postIds)
//    {
//        return toAjax(postService.deletePostByIds(postIds));
//    }
//
//    /**
//     * 获取岗位选择框列表
//     */
//    @GetMapping("/optionselect")
//    public AjaxResult optionselect()
//    {
//        List<SysPost> posts = postService.selectPostAll();
//        return success(posts);
//    }
//}
