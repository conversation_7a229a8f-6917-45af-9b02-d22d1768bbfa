package com.rcszh.lowcode.handler.form_init;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rcszh.base.common.core.controller.BaseController;
import com.rcszh.base.common.core.domain.AjaxResult;
import com.rcszh.base.common.core.page.TableDataInfo;
import com.rcszh.lowcode.entity.form_init.FormInitRule;
import com.rcszh.lowcode.entity.form_init.FormInitRuleField;
import com.rcszh.lowcode.service.form_init.IFormInitRuleFieldService;
import com.rcszh.lowcode.service.form_init.IFormInitRuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping("/formInit")
public class FormInitRuleController extends BaseController {
    @Autowired
    private IFormInitRuleService formInitRuleService;

    @Autowired
    private IFormInitRuleFieldService formInitRuleFieldService;

    @GetMapping("/list")
    public TableDataInfo list() {
        startPage();
        List<FormInitRule> list = formInitRuleService.list();
//        if (CollUtil.isNotEmpty(list)) {
//            List<FormInitRuleField> fields = formInitRuleFieldService.list(new LambdaQueryWrapper<FormInitRuleField>().in(FormInitRuleField::getInitRuleId, list.stream().map(FormInitRule::getId).toList()));
//            Map<String, List<FormInitRuleField>> fieldGroup = fields.stream().collect(Collectors.groupingBy(FormInitRuleField::getInitRuleId));
//            for (FormInitRule formInitRule : list) {
//                List<FormInitRuleField> initRuleFields = fieldGroup.get(formInitRule.getId());
//                formInitRule.setFields(initRuleFields);
//            }
//        }
        return getDataTable(list);
    }

    @GetMapping("/all")
    public AjaxResult all() {
        List<FormInitRule> list = formInitRuleService.list();
        return AjaxResult.success(list);
    }


    /**
     * 获取单初始化规则详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        FormInitRule formInitRule = formInitRuleService.getById(id);
        if (Objects.nonNull(formInitRule)) {
            List<FormInitRuleField> fields = formInitRuleFieldService.list(new LambdaQueryWrapper<FormInitRuleField>().eq(FormInitRuleField::getInitRuleId, id));
            formInitRule.setFields(fields);
        }
        return success(formInitRule);
    }

    /**
     * 新增单初始化规则
     */
    @PostMapping
    public AjaxResult add(@RequestBody FormInitRule formInitRule) {
        return toAjax(formInitRuleService.saveFormInitRule(formInitRule));
    }

    /**
     * 修改单初始化规则
     */
    @PutMapping
    public AjaxResult edit(@RequestBody FormInitRule formInitRule) {
        return toAjax(formInitRuleService.updateFormInitRule(formInitRule));
    }

    /**
     * 删除单初始化规则
     */
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(formInitRuleService.removeBatchByIds(Arrays.asList(ids)));
    }

}
