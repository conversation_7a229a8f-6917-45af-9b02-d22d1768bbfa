package com.rcszh.lowcode.handler.form;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageInfo;
import com.rcszh.base.common.annotation.Log;
import com.rcszh.base.common.constant.HttpStatus;
import com.rcszh.base.common.core.controller.BaseController;
import com.rcszh.base.common.core.domain.AjaxResult;
import com.rcszh.base.common.core.page.PageDomain;
import com.rcszh.base.common.core.page.TableDataInfo;
import com.rcszh.base.common.core.page.TableSupport;
import com.rcszh.base.common.enums.BusinessType;
import com.rcszh.lowcode.dto.FormDto;
import com.rcszh.lowcode.dto.form.ImportFormData;
import com.rcszh.lowcode.entity.dto.FormInfo;
import com.rcszh.lowcode.entity.dto.design_info.FormDesignInfo;
import com.rcszh.lowcode.entity.form.Form;
import com.rcszh.lowcode.entity.form.FormHistory;
import com.rcszh.lowcode.enums.form.SaveFormTypeEnum;
import com.rcszh.lowcode.mapper.form.FormHistoryMapper;
import com.rcszh.lowcode.schema.FormInfoSchema;
import com.rcszh.lowcode.service.form.IFormService;
import com.rcszh.lowcode.utils.FormUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@RestController
@RequestMapping("/form")
public class FormController extends BaseController {
    @Resource
    private IFormService formService;
    @Resource
    private FormHistoryMapper formHistoryMapper;
    @Resource
    private FormUtil formUtil;

    /**
     * 导出表单
     *
     * @param formId 表单id
     * @return 表单json
     */
    @ResponseBody
    @PostMapping("/export/{formId}")
    public void exportForm(HttpServletResponse response, @PathVariable String formId) {
        String result = formService.exportForm(formId);
        // 设置响应头以下载文件
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename*=utf-8'zh_cn'" + URLEncoder.encode("form.json", StandardCharsets.UTF_8));
        // 获取输出流
        try (OutputStream out = response.getOutputStream()) {
            // 确保内容写入输出流
            out.write(result.getBytes());
            out.flush();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
    /**
     * 导入表单
     *
     * @param formJson 表单json
     * @return 表单json
     */
    @ResponseBody
    @PostMapping("/import")
    public AjaxResult importForm(@RequestBody ImportFormData formJson) {
        return AjaxResult.success(formService.importForm(formJson));
    }
    /**
     * 创建表单
     */
    @Log(title = "表单管理", businessType = BusinessType.INSERT)
    @ResponseBody
    @PostMapping("/create")
    public AjaxResult create(@RequestBody FormInfo formInfo) {
        formService.createForm(formInfo, false);
        return AjaxResult.success();
    }

    @Log(title = "表单管理", businessType = BusinessType.INSERT)
    @ResponseBody
    @PostMapping("/update")
    public AjaxResult update(@RequestBody FormInfo form) {
        formService.updateFormInfo(form);
        return AjaxResult.success();
    }
    /**
     * 获取表单分组
     */
    @ResponseBody
    @GetMapping("/group")
    public TableDataInfo getGroup(Form form) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        List<String> formGroup = formService.getFormGroup(form);
        List<Map<String, String>> list = formGroup.stream()
                .skip((long) (pageNum - 1) * pageSize).limit(pageSize)
                .map(item -> Map.of("groupName", item))
                .toList();
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(list);
        rspData.setTotal(formGroup.size());
        return rspData;
    }
    /**
     * 表单列表
     */
    @ResponseBody
    @GetMapping("/all")
    public AjaxResult all(Form form) {
        return AjaxResult.success(formService.getAllForm(form));
    }
    /**
     * 表单列表
     */
    @ResponseBody
    @GetMapping("/page")
    public TableDataInfo page(Form form) {
        List<Form> allForm = formService.getPageForm(form);
        List<List<FormDto>> res = new ArrayList<>();
        // 查询表单历史记录
        if (!CollUtil.isEmpty(allForm)){
            Map<String, FormHistory> historyMap = formHistoryMapper.selectList(new LambdaQueryWrapper<FormHistory>()
                            .in(FormHistory::getFormId, allForm.stream().map(Form::getId).toList()))
                    .stream()
                    .collect(Collectors.toMap(FormHistory::getFormId, FormHistory -> FormHistory));
            Map<String, List<FormDto>> collect = allForm.stream()
                    .map(item -> {
                        FormDto formDto = new FormDto();
                        BeanUtil.copyProperties(item, formDto);
                        formDto.setFormHistory(historyMap.get(item.getId()));
                        return formDto;
                    })
                    .collect(Collectors.groupingBy(Form::getGroupName));
            collect.forEach((key, value) -> {
                res.add(value);
            });
        }
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(res);
        rspData.setTotal(new PageInfo(allForm).getTotal());
        return rspData;
    }
    /**
     * 保存表单数据建模
     * 废弃（不要再调用）
     */
    @Log(title = "表单管理", businessType = BusinessType.INSERT)
    @ResponseBody
    @PostMapping("/save")
    @Deprecated
    public AjaxResult saveForm(@RequestBody FormInfo formInfo) {
        FormInfoSchema formInfoSchema = formUtil.covertFormRequestToInfo(SaveFormTypeEnum.SAVE_FORM_TABLE, formInfo, null);
        formService.saveForm(SaveFormTypeEnum.SAVE_FORM_TABLE,formInfoSchema, false);
        return AjaxResult.success();
    }
    /**
     * 保存表单设计
     */
    @Log(title = "表单管理", businessType = BusinessType.UPDATE)
    @PostMapping("/saveDesign")
    public AjaxResult saveFormDesign(@RequestBody FormDesignInfo formDesignInfo) {
        FormInfoSchema formInfoSchema = formUtil.covertFormRequestToInfo(SaveFormTypeEnum.SAVE_FORM_DESIGN, null, formDesignInfo);
        formService.saveForm(SaveFormTypeEnum.SAVE_FORM_DESIGN, formInfoSchema, false);
        return AjaxResult.success();
    }
    /**
     * 获取表单设计
     */
    @GetMapping("/getDesignInfo/{formId}")
    public AjaxResult getFormDesignInfo(@PathVariable String formId) {
        HashMap<String, Object> result = formService.getFormDesignInfo(formId);
        return AjaxResult.success(result);
    }

    /**
     * 获取表单设计历史
     */
    @GetMapping("/getHistoryDesignInfo/{formId}")
    public AjaxResult getHistoryDesignInfo(@PathVariable String formId) {
        HashMap<String, Object> result = formService.getHistoryDesignInfo(formId);
        return AjaxResult.success(result);
    }

    /**
     * 获取上一次保存的表单详情
     */
    @ResponseBody
    @GetMapping("/getVersionFormInfo/{formId}")
    public AjaxResult getVersionFormInfo(@PathVariable String formId) {
        return AjaxResult.success(formService.getVersionFormInfo(formId));
    }
    /**
     * 获取表单详情
     */
    @ResponseBody
    @GetMapping("/getFormInfo/{formId}")
    public AjaxResult getFormInfo(@PathVariable String formId) {
        return AjaxResult.success(formService.getFormInfoById(formId));
    }
    /**
     * 发布
     */
    @Log(title = "表单管理", businessType = BusinessType.UPDATE)
    @PostMapping("/release")
    public AjaxResult release(@RequestBody FormDesignInfo formDesignInfo) {
        formService.releaseForm(formDesignInfo);
        return AjaxResult.success();
    }

    /**
     * 删除表单
     */
    @Log(title = "表单管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/delete/{formId}")
    public AjaxResult delete(@PathVariable String formId) {
        formService.deleteForm(formId);
        return AjaxResult.success();
    }


    @GetMapping("/getFormFields/{formId}")
    public AjaxResult getFormFields(@PathVariable String formId) {
        return AjaxResult.success(formService.getFormFieldsById(formId));
    }

    @GetMapping("/getFormMenu")
    public AjaxResult getFormMenu() {
        return AjaxResult.success(formService.getFormMenu());
    }
}
