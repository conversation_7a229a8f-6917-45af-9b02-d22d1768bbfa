package com.rcszh.lowcode.handler.form;

import com.rcszh.base.common.core.domain.AjaxResult;
import com.rcszh.lowcode.feign_api.ProjectTemplateInterface;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/form/config")
public class FormConfigHandler {
    @Resource
    private ProjectTemplateInterface projectTemplateInterface;

    // 获取项目相关的配置信息
    @GetMapping("/getProjectConfig")
    public AjaxResult getProjectConfig() {
        return AjaxResult.success(projectTemplateInterface.getProjectConfig());
    }
}
