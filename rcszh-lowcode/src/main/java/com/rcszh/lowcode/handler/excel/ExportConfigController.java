package com.rcszh.lowcode.handler.excel;

import com.rcszh.lowcode.entity.excel.ExportConfig;
import com.rcszh.lowcode.service.excel.IExportConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;

@RestController
@RequestMapping("/api/exportConfig")
public class ExportConfigController {
    @Autowired
    private IExportConfigService exportConfigService;

    @GetMapping("/list")
    public List<ExportConfig> list() {
        return exportConfigService.list();
    }

    @GetMapping("/{id}")
    public ExportConfig get(@PathVariable String id) {
        return exportConfigService.getById(id);
    }

    @PostMapping
    public boolean add(@RequestBody ExportConfig config) {
        return exportConfigService.save(config);
    }

    @PutMapping
    public boolean update(@RequestBody ExportConfig config) {
        return exportConfigService.updateById(config);
    }

    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return exportConfigService.removeById(id);
    }
} 