package com.rcszh.lowcode.handler.form;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.rcszh.base.common.core.controller.BaseController;
import com.rcszh.base.common.core.domain.AjaxResult;
import com.rcszh.base.common.core.page.TableDataInfo;
import com.rcszh.base.common.exception.ServiceException;
import com.rcszh.lowcode.entity.form.Form;
import com.rcszh.lowcode.entity.form_init.FormInitRule;
import com.rcszh.lowcode.entity.view.ViewForm;
import com.rcszh.lowcode.entity.view.ViewFormConnect;
import com.rcszh.lowcode.service.form.IFormService;
import com.rcszh.lowcode.service.form_init.impl.FormInitRuleService;
import com.rcszh.lowcode.service.view.IViewFormConnectService;
import com.rcszh.lowcode.service.view.IViewFormService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/form/viewConnect")
public class ViewFormConnectController extends BaseController {

    @Resource
    private IViewFormConnectService viewFormConnectService;
    @Resource
    private IFormService formService;
    @Resource
    private IViewFormService viewFormService;
    @Resource
    private FormInitRuleService formInitRuleService;

    /**
     * 创建视图关联
     * @param viewFormConnect 视图关联实体
     * @return 是否创建成功
     */
    @PostMapping
    public boolean create(@RequestBody ViewFormConnect viewFormConnect) {
        viewFormConnect.fillBaseFields();
        return viewFormConnectService.save(viewFormConnect);
    }

    /**
     * 根据code查询视图关联
     */
    @GetMapping("/getByCode/{code}")
    public AjaxResult getByCode(@PathVariable String code) {
        ViewFormConnect one = viewFormConnectService.getOne(new QueryWrapper<ViewFormConnect>().lambda().eq(ViewFormConnect::getCode, code));
        if (Objects.isNull(one)) {
            throw new ServiceException("视图关联不存在");
        }
        covertConnect(List.of(one));
        return AjaxResult.success(one);
    }

    /**
     * 根据ID查询视图关联
     * @param id 视图关联ID
     * @return 视图关联实体
     */
    @GetMapping("/{id}")
    public AjaxResult getById(@PathVariable String id) {
        return AjaxResult.success(viewFormConnectService.getById(id));
    }

    /**
     * 查询所有视图关联
     * @return 视图关联列表
     */
    @GetMapping("/all")
    public AjaxResult getAll() {
        return  AjaxResult.success(viewFormConnectService.list());
    }

    /**
     * 更新视图关联
     * @param viewFormConnect 视图关联实体
     * @return 是否更新成功
     */
    @PutMapping
    public void update(@RequestBody ViewFormConnect viewFormConnect) {
        viewFormConnectService.updateById(viewFormConnect);
    }

    /**
     * 根据ID删除视图关联
     * @param id 视图关联ID
     * @return 是否删除成功
     */
    @DeleteMapping("/{id}")
    public void delete(@PathVariable String id) {
        viewFormConnectService.removeById(id);
    }

    /**
     * 分页查询视图关联
     * @return 分页结果
     */
    @GetMapping("/page")
    public TableDataInfo getPage() {
        startPage();
        List<ViewFormConnect> list = viewFormConnectService.list(new QueryWrapper<>());
        if (CollUtil.isEmpty(list)) {
            return getDataTable(new ArrayList<>());
        }
        covertConnect(list);
        return getDataTable(list);
    }

    private void covertConnect(List<ViewFormConnect> list) {
        List<String> viewFormIdList = list.stream().map(ViewFormConnect::getViewFormId).filter(Objects::nonNull).toList();
        List<String> formIdList = list.stream().map(ViewFormConnect::getFormId).filter(Objects::nonNull).toList();
        List<String> initRuleIdList = list.stream().map(ViewFormConnect::getInitRuleId).filter(Objects::nonNull).toList();
        Map<String, ViewForm> collect = CollUtil.isNotEmpty(viewFormIdList) ? viewFormService.list(new LambdaQueryWrapper<ViewForm>().in(ViewForm::getId, viewFormIdList)
        ).stream().collect(Collectors.toMap(ViewForm::getId, item -> item))
                : new HashMap<>();

        Map<String, Form> formCollect = CollUtil.isNotEmpty(formIdList) ? formService.list(new LambdaQueryWrapper<Form>().in(Form::getId, formIdList)
        ).stream().collect(Collectors.toMap(Form::getId, item -> item))
                : new HashMap<>();

        Map<String, FormInitRule> formInitRuleCollect = CollUtil.isNotEmpty(initRuleIdList) ? formInitRuleService.list(new LambdaQueryWrapper<FormInitRule>()
                .in(FormInitRule::getId, initRuleIdList)
        ).stream().collect(Collectors.toMap(FormInitRule::getId, item -> item))
                : new HashMap<>();

        for (ViewFormConnect viewFormConnect : list) {
            viewFormConnect.setViewFormName(collect.getOrDefault(viewFormConnect.getViewFormId(), new ViewForm()).getName());
            viewFormConnect.setFormName(formCollect.getOrDefault(viewFormConnect.getFormId(), new Form()).getName());
            viewFormConnect.setInitRuleName(formInitRuleCollect.getOrDefault(viewFormConnect.getInitRuleId(), new FormInitRule()).getName());
            viewFormConnect.setInitRuleCode(formInitRuleCollect.getOrDefault(viewFormConnect.getInitRuleId(), new FormInitRule()).getCode());
        }
    }
}
