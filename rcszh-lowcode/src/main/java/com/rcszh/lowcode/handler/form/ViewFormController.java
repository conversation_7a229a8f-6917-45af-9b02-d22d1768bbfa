package com.rcszh.lowcode.handler.form;


import com.rcszh.base.common.core.controller.BaseController;
import com.rcszh.base.common.core.domain.AjaxResult;
import com.rcszh.lowcode.entity.form.Form;
import com.rcszh.lowcode.entity.form.FormTable;
import com.rcszh.lowcode.entity.view.ViewForm;
import com.rcszh.lowcode.entity.view.ViewFormConfig;
import com.rcszh.lowcode.enums.form.FormStatusEnum;
import com.rcszh.lowcode.service.form.IFormTableService;
import com.rcszh.lowcode.service.form.impl.FormService;
import com.rcszh.lowcode.service.view.IViewFormService;
import com.rcszh.lowcode.service.view.impl.ViewFormConfigService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

/**
 * 视图
 */
@RestController
@RequestMapping("/form/view")
public class ViewFormController extends BaseController {
    @Resource
    private FormService formService;
    @Resource
    private IViewFormService viewFormService;
    @Resource
    private ViewFormConfigService viewFormConfigService;
    @Resource
    private IFormTableService formTableService;

    /**
     * 获取表单视图
     */
    @GetMapping("/list/{formId}")
    public AjaxResult getViewFormInfo(@PathVariable String formId, ViewForm viewForm) {
        Form form = formService.getFormById(formId);
        if (!FormStatusEnum.PUBLISH.getStatus().equals(form.getFormStatus())) {
            throw new RuntimeException("当前表单未发布，无法查看视图");
        }
        return AjaxResult.success(viewFormService.findByFormId(formId, viewForm));
    }

    @GetMapping("/{viewFormId}")
    public AjaxResult getViewForm(@PathVariable String viewFormId) {
        return AjaxResult.success(viewFormService.getViewFormConfigById(viewFormId));
    }

    /**
     * 改变视图状态
     */
    @GetMapping("/status/{viewFormId}/{status}")
    public AjaxResult changeViewStatus(@PathVariable String viewFormId, @PathVariable String status) {
        return AjaxResult.success();
    }

    /**
     * 获取对应视图的所有表单配置
     */
    @GetMapping("/{viewFormId}/config")
    public AjaxResult getViewFormConfig(@PathVariable String viewFormId) {
        return AjaxResult.success(viewFormConfigService.findAllConfigById(viewFormId));
    }

    @GetMapping("/config/{viewFormId}/{viewFormType}")
    public AjaxResult getViewFormConfig(@PathVariable String viewFormId, @PathVariable String viewFormType) {
        return AjaxResult.success(viewFormConfigService.findConfigByViewFormTypeAndId(viewFormType,viewFormId));
    }

    /**
     * 更新表单视图配置
     */
    @PutMapping("/config")
    public AjaxResult updateViewConfig(@RequestBody ViewFormConfig viewFormConfig) {
        return toAjax(viewFormConfigService.updateViewFormConfig(viewFormConfig));
    }

    /**
     * 新增表单视图
     */
    @PostMapping("/addViewForm")
    public AjaxResult addViewForm(@RequestBody ViewForm viewForm) {
        String formId = viewForm.getFormId();
        Form form = formService.getFormById(formId);
        if (form == null) {
            throw new RuntimeException("对应表单不存在");
        }
        // 查询主表
        FormTable formTable =  formTableService.getMainTale(formId);
        viewFormService.addViewForm(formTable.getId(),viewForm);
        return AjaxResult.success();
    }

    @DeleteMapping("/{viewFormId}")
    public AjaxResult deleteViewForm(@PathVariable String viewFormId) {
        return toAjax(viewFormService.deleteVIewFormById(viewFormId));
    }
}
