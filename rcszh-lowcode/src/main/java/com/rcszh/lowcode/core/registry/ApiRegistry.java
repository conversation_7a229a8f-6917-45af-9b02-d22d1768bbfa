package com.rcszh.lowcode.core.registry;

import com.rcszh.lowcode.core.enums.PollingTypeEnum;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import java.util.HashMap;
import java.util.Map;

/**
 * 接口注册中心
 */
public class ApiRegistry implements ApplicationContextAware {
    // 容器
    private static final Map<Class<?>, Map<String, Object>> apiMap = new HashMap<>();
    // 注册
    public static <T> void register(Class<?> clazz, String apiKey, T apiInfo) {
        Map<String, Object> map = apiMap.computeIfAbsent(clazz, k -> new HashMap<>());
        Object instance = map.get(apiKey);
        if (instance != null) {
            throw new RuntimeException("接口已存在");
        }
        map.put(apiKey, apiInfo);
    }

    // 获取
    public static <T> T getApi(Class<T> clazz) {

        return getApi(clazz, null);
    }

    @SuppressWarnings("unchecked")
    public static <T> T getApi(Class<T> clazz, PollingTypeEnum typeEnum) {


        Map<String, Object> map = apiMap.get(clazz);
        if (map == null) {
            throw new IllegalStateException("没有找到 " + clazz.getName() + " 的实现");
        }
        typeEnum = typeEnum == null ? PollingTypeEnum.FIRST : typeEnum;
        return switch (typeEnum) {
            case FIRST -> (T) map.values().stream().findFirst().orElse(null);
            case POLLING -> (T) map.values().stream().findAny().orElse(null);
            case RANDOM -> (T) map.values().stream().skip((int) (Math.random() * map.size())).findFirst().orElse(null);
            default -> throw new RuntimeException("不兼容的轮询类型");
        };
    }


}
