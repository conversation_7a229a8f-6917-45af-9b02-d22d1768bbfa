package com.rcszh.lowcode.entity.base;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.rcszh.lowcode.core.registry.ApiRegistry;
import com.rcszh.lowcode.feign_api.DeptTemplateInterface;
import com.rcszh.lowcode.feign_api.LoginInfoTemplateInterface;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;


@Data
@NoArgsConstructor
public class FormBaseEntity implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @JsonIgnore
    @TableField(exist = false)
    private String searchValue;
    private String createBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    private String updateBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    @JsonInclude(Include.NON_EMPTY)
    @TableField(exist = false)
    private Map<String, Object> params;
    public Map<String, Object> getParams() {
        if (this.params == null) {
            this.params = new HashMap();
        }
        return this.params;
    }
    public void fillBaseFields() {
        LoginInfoTemplateInterface bean = ApiRegistry.getApi(LoginInfoTemplateInterface.class);
        String userId = bean.getLoginUserId();
        Date now = new Date();
        if (StrUtil.isBlank(this.createBy)) {
            this.createBy = userId;
        }
        if (Objects.isNull(this.createTime)) {
            this.createTime = now;
        }
        this.updateTime = now;
        this.updateBy = userId;
    }
}
