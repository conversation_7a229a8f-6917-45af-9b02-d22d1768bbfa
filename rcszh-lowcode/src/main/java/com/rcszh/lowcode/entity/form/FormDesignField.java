package com.rcszh.lowcode.entity.form;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rcszh.lowcode.entity.base.FormBaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 表单设计字段
 */
@Getter
@Setter
@TableName("form_design_field")
public class FormDesignField extends FormBaseEntity {
    /**
     * 字段组件id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 表单Id
     */
    private String formId;
    /**
     * 表Id
     */
    private String formTableId;

    private String formTableFieldId;

    private String parentId;
    /**
     * 字段编码（对应真实数据库字段）
     * 原（code）
     */
    private String name;
    /**
     * 字段组件类型
     * type: input、text...
     */
    private String component;

    /**
     * 字段长度
     */
    private Integer length;

    /**
     * 小数点
     */
    private Integer digit;

    /**
     * 字段组件名称
     * 原 name
     */
    private String label;
    /**
     * 状态
     */
    private String status;
    /**
     * 前端UI prop
     */
    private String props;
    /**
     * 字段配置（UI）
     */
    private String options;
    /**
     * 字段索引
     */
    private String fieldIndex;
    /**
     * 组件描述
     */
    private String description;
}
