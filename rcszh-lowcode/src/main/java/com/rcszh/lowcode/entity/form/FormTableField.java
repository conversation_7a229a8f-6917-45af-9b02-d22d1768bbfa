package com.rcszh.lowcode.entity.form;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 组件
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("form_table_field")
public class FormTableField extends FormBaseFormTableField {

    private String props;

    public JSONObject getPropsJson() {
        if (props == null) {
            return new JSONObject();
        }
        return JSONUtil.parseObj(props);
    }


}
