package com.rcszh.lowcode.entity.dto.design_info;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.rcszh.lowcode.entity.form.Form;
import lombok.Getter;
import lombok.Setter;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class FormDesignData extends Form {
    private List<FormDesignDataItem> items;

    // 分组名称
    @JsonIgnore
    private Map<String, Object> otherFields = new LinkedHashMap<>();

    @JsonAnyGetter
    public Map<String, Object> getUnKnowField() {
        return otherFields;
    }

    @JsonAnySetter
    public void setUnKnowField(String key, Object value) {
        otherFields.put(key, value);
    }
}
