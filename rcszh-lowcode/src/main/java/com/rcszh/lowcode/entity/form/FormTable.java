package com.rcszh.lowcode.entity.form;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rcszh.lowcode.entity.base.FormBaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 与表单关联的数据库信息
 */
@Getter
@Setter
@TableName("form_table")
public class FormTable extends FormBaseEntity {
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 关联的表单Id
     */
    private String formId;
    /**
     * 数据库表名
     */
    private String name;
    /**
     * 真实的数据库表名（表单编码）
     */
    private String tableName;
    /**
     * 状态
     */
    private String status;
    /**
     * 数据库类型: 主表、明细表
     */
    private String type;

    public String getForeignKey() {
        return this.tableName + "_id";
    }

}
