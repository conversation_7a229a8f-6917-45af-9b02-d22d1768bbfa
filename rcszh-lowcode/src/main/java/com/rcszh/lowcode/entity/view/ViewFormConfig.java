package com.rcszh.lowcode.entity.view;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rcszh.lowcode.entity.base.FormBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("view_form_config")
public class ViewFormConfig extends FormBaseEntity {
    @TableId(type= IdType.ASSIGN_ID)
    private String id;
    /**
     * 视图id
     */
    private String viewFormId;
    /**
     * viewForm类型（冗余）：列表视图、查看视图
     */
    private String viewFormType;
    /**
     * 配置类型：显示配置、搜搜配置、排序配置、 筛选配置、按钮配置、基础配置
     */
    private String type;
    /**
     * 配置名称
     */
    private String name;
    /**
     * 系统类型：内置|自定义
     */
    private String systemType;
    /**
     * 配置信息
     * 显示配置：[{fieldId:字段id,fieldCode:字段编码,fieldName:字段名称,fieldType:字段组件类型,isShow:是否显示,sort:排序}]
     * 搜索设置：[{fieldCode:字段编码,fieldName:字段名称,selectType:查询类型}]
     * 排序设置：[{fieldCode:字段编码,fieldName:字段名称,fieldType:字段组件类型,order: 排序}]
     * 筛选设置：待定
     * 按钮设置：[{buttonType:按钮类型,buttonName:按钮名称}]
     * 基础设置：待定
     * 页面配置
     */
    private String options;

}
