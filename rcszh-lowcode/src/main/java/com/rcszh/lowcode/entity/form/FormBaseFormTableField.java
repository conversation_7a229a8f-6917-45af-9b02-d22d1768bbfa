package com.rcszh.lowcode.entity.form;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.rcszh.lowcode.entity.base.FormBaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

@Getter
@Setter
public class FormBaseFormTableField extends FormBaseEntity {
    /**
     * 字段组件id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 表单Id
     */
    private String formId;

    /**
     * 表Id
     */
    private String formTableId;
    /**
     * 真实表名
     */
    @TableField(exist = false)
    private String formTableName;
    /**
     * 是否开启索引
     */
    private String isOpenIndex;
    /**
     * 父id
     */
    private String parentId;
    /**
     * 字段编码（对应真实数据库字段）
     * 原（code）
     */
    private String name;
    /**
     * 字段组件类型
     * type: input、text...
     */
    private String component;
    /**
     * 组件顺序
     */
    private Integer componentOrder;
    /**
     * 对应数据库类型
     * 原 type
     */
    private String jdbcType;
    /**
     * 字段顺序
     * TODO 这个和组件顺序有无区别
     */
    private Integer fieldOrder;
    /**
     * 字段长度
     */
    private Integer length;
    /**
     * 小数点
     */
    private Integer digit;
    /**
     * 字段组件名称
     * 原 name
     */
    private String label;
    /**
     * 状态
     */
    private String status;
    /**
     * 是否是系统字段
     */
    private String ftfIsSystem;
    /**
     * 是否显示（在表单设计中）
     */
    private String ftfDisplay;
    /**
     * 字段配置（UI）
     */
    private String options;
    /**
     * 组件描述
     */
    private String description;

    public JSONObject getOptionsJson() {
        if (options == null) {
            return new JSONObject();
        }
        return JSONUtil.parseObj(options);
    }


    public String getDbFieldLengthStr() {
        if (Objects.isNull(length)) {
            return "";
        }
        if (Objects.isNull(digit)) {
            return "(" + length + ")";
        }
        return "(" + length + "," + digit + ")";
    }
}
