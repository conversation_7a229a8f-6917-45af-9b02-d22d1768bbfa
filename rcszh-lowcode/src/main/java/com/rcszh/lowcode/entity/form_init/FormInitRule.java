package com.rcszh.lowcode.entity.form_init;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rcszh.lowcode.entity.base.FormBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Objects;


@EqualsAndHashCode(callSuper = true)
@Data
@TableName("form_init_rule")
public class FormInitRule extends FormBaseEntity {
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 编码
     */
    private String code;

    private String name;

    private String formId;

    /**
     * 类型
     * 1、数据映射，使用时指定ruleCode, {$ruleCode: "workTimeEntry", id: "", code: "", name: ""}
     * 2、根据配置的数据来源，查询数据；并按映射转换
     */
    private String type;

    private String options;

    private String outputField;

    @TableField(exist = false)
    private JSONObject jsonOptions;

//    public void setJsonOptions(JSONObject jsonOptions) {
//        this.jsonOptions = jsonOptions;
//        if (Objects.nonNull(jsonOptions)) {
//            this.options = JSONUtil.toJsonStr(jsonOptions);
//        }
//    }
//
    public JSONObject getJsonOptions() {
        if (Objects.nonNull(this.options)) {
            this.jsonOptions = JSONUtil.parseObj(this.options);
        }
        return this.jsonOptions;
    }

    @TableField(exist = false)
    private List<FormInitRuleField> fields;
}
