package com.rcszh.lowcode.entity.excel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rcszh.lowcode.entity.base.FormBaseEntity;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@TableName("export_config")
public class ExportConfig extends FormBaseEntity {
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    // 模板名称
    private String name;
}
