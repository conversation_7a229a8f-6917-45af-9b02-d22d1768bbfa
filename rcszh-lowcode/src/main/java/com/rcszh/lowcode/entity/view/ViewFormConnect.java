package com.rcszh.lowcode.entity.view;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.rcszh.lowcode.entity.base.FormBaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 视图关联
 */
@Getter
@Setter
public class ViewFormConnect extends FormBaseEntity {
    // 主键
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    // 名称
    private String name;
    // 编码
    private String code;
    // 表单id
    private String formId;
    // 表单名称
    @TableField(exist = false)
    private String formName;
    // 视图id
    private String viewFormId;
    // 视图名称
    @TableField(exist = false)
    private String viewFormName;
    // 初始化规则id
    private String initRuleId;
    // 初始化规则名称
    @TableField(exist = false)
    private String initRuleName;
    // 初始化规则名称
    @TableField(exist = false)
    private String initRuleCode;
}
