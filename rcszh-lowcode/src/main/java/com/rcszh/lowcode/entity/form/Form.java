package com.rcszh.lowcode.entity.form;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rcszh.lowcode.entity.base.FormBaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 表单
 */
@Getter
@Setter
@TableName("form")
public class Form extends FormBaseEntity {
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 表单名称
     */
    private String name;
    /**
     * 表单编码
     */
    private String code;
    /**
     * 数据库类型: 普通表
     */
    private String type;
    /**
     * 表单分组
     */
    private String groupName;
    /**
     * 表单状态
     */
    private String formStatus;
    /**
     * 表单设计配置
     */
    private String formDesignConfig;
    /**
     * 是否开启移动端
     */
    private String isOpenMobile;
}
