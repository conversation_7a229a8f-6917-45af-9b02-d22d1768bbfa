package com.rcszh.lowcode.entity.form_init;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rcszh.lowcode.entity.base.FormBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


@EqualsAndHashCode(callSuper = true)
@Data
@TableName("form_init_rule_field")
public class FormInitRuleField extends FormBaseEntity {
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 源编码
     */
    private String initRuleId;

    /** 冗余 */
    private String formId;

    /**
     * 源编码
     */
    private String sourceCode;

    /**
     * 目标编码
     */
    private String targetCode;

    private String type;
}
