package com.rcszh.lowcode.entity.dto.design_info;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.rcszh.lowcode.dto.FormTableFieldDto;
import lombok.Getter;
import lombok.Setter;

import java.util.*;

@Getter
@Setter
public class FormDesignDataItem extends FormTableFieldDto {
    private List<FormDesignDataItem> children;
    // 分组名称
    @JsonIgnore
    private Map<String, Object> otherFields = new LinkedHashMap<>();

    @JsonAnyGetter
    public Map<String, Object> getUnKnowField() {
        return otherFields;
    }

    @JsonAnySetter
    public void setUnKnowField(String key, Object value) {
        otherFields.put(key, value);
    }

    public HashMap<String, Object> toMap() {
        HashMap<String, Object> map = new HashMap<>();
        map.putAll(JSONUtil.parseObj(this));
        map.putAll(this.getOtherFields());
        map.putAll(this.getOptionsJson());
        for (String key : map.keySet()) {
            if (map.get(key) instanceof Date date) {
                map.put(key, DateUtil.format(date, "yyyy-MM-dd HH:mm:ss"));
            }
        }
        return map;
    }
}
