package com.rcszh.lowcode.entity.form;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rcszh.lowcode.entity.base.FormBaseEntity;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@TableName("form_history")
public class FormHistory extends FormBaseEntity {
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    // 表单id
    private String formId;
    // 表单内容
    private String formInfo;
    // 表单数据表内容
    private String formTableInfo;
    // 表单字段内容
    private String formTableFiledInfo;
    // 表单设计内容
    private String formDesignInfo;
    // 逻辑删除
    @TableLogic(value = "N", delval = "Y")
    private String isDeleted;
}
