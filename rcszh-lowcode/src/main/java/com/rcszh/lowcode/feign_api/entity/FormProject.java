package com.rcszh.lowcode.feign_api.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class FormProject {
    /**
     * 主键
     */
    private Long id;

    /**
     * 项目编码
     */
    private String code;

    /**
     * 项目名称
     */
    private String name;

    /**
     * 关联项目
     */
    private Long associatedProjectId;

    /**
     * 关联项目
     */
    private String associatedProjectName;

    /**
     * 项目简称
     */
    private String projectAbbr;

    /**
     * 项目类别
     */
    private String categoryCode;

    /**
     * 项目类别
     */
    private String categoryName;

    /**
     * 项目状态
     */
    private String projectStatus;

    /**
     * 项目状态
     */
    private String projectStatusName;

    /**
     * 项目状态
     */
    private String projectMode;

    /**
     * 项目状态
     */
    private String projectModeName;

    /**
     * 项目经理
     */
    private Long projectManagerId;

    /**
     * 项目经理
     */
    private String projectManagerName;

    /**
     * 项目主管
     */
    private Long projectSupervisorId;

    /**
     * 项目主管
     */
    private String projectSupervisorName;

    /**
     * 项目助理
     */
    private Long projectAssistantId;

    /**
     * 项目助理
     */
    private String projectAssistantName;

    /**
     * 计划开始于
     */
    private Date plannedStartAt;

    /**
     * 计划完成于
     */
    private Date plannedFinishBy;

    /**
     * 实际开始时间
     */
    private Date actualStartDate;

    /**
     * 实际完成时间
     */
    private Date actualEndDate;

    /**
     * 项目助理2
     */
    private Long projectAssistantId2;

    /**
     * 项目助理2
     */
    private String projectAssistantId2Name;

    /**
     * 项目助理3
     */
    private Long projectAssistantId3;

    /**
     * 项目助理3
     */
    private String projectAssistantId3Name;

    /**
     * 项目助理4
     */
    private Long projectAssistantId4;

    /**
     * 项目助理4
     */
    private String projectAssistantId4Name;

    /**
     * 项目助理5
     */
    private Long projectAssistantId5;

    /**
     * 项目助理5
     */
    private String projectAssistantId5Name;

    /**
     * 销售
     */
    private Long salespersonId;

    /**
     * 销售
     */
    private String salespersonName;

    /**
     * 省份
     */
    private String province;

    /**
     * 省份
     */
    private String provinceName;

    /**
     * 城市
     */
    private String city;

    /**
     * 城市
     */
    private String cityName;

    /**
     * 区县
     */
    private String area;

    /**
     * 区县
     */
    private String areaName;

    /**
     * 地址
     */
    private String address;

    /**
     * 币种
     */
    private String currencyCode;

    /**
     * 汇率
     */
    private BigDecimal rate;

    /**
     * 合同金额
     */
    private BigDecimal contractAmount;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 不含税金额
     */
    private BigDecimal amountExcludingTax;

    /**
     * 描述
     */
    private String description;

    /**
     * 客户
     */
    private String customerId;

    /**
     * 客户
     */
    private String customerName;

    /**
     * 公司id
     */
    private Long companyId;

    /**
     * 公司id
     */
    private String companyName;

    /**
     * 状态标识0.所有项目  1.未完成项目  2.已完成项目
     */
    private Integer statusFlag;

    /**
     * 维度1.项目类别  2.完成时间
     */
    private Integer dimensionFlag;

    /**
     * 工时比例
     */
    private BigDecimal workHoursRate;
    // 计划日期和当前日期的日期差异
    private Long daysSince;
    // 计划日期和当前日期的日期差异标识
    private String daysSinceFlag;
    // 计划完成日期和当前日期的日期差异提示语
    private String daysSinceMessage;

    /**
     * 进度
     */
    private BigDecimal progress;
    // 总工时: 分钟
    private Integer workDuration;
    // 人员状态
    private String userStatus;
    // 可填报日期
    private List<Date> entryDates;
    // 开始时间
    private Date startTime;
    // 结束时间
    private Date endTime;
    // 用户ID
    private Long userId;
    // 创建人
    private String createByName;
    // 项目ids
    private List<Long> projectIds;

    // 任务id
    private Long taskId;
}
