package com.rcszh.lowcode.feign_api.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class FormProjectTask {
    private Long id;
    // 项目名称
    @ApiModelProperty(value = "项目名称")
    private String name;
    // 项目编号
    @ApiModelProperty(value = "项目编号")
    private String code;
    @ApiModelProperty(value = "项目类型")
    private String category;

    @ApiModelProperty(value = "是否填报工时")
    private String isEntry;
    /**
     * SysProjectType
     */
    @ApiModelProperty(value = "任务类型")
    private String type;

    @ApiModelProperty(value = "活动类型")
    private String activityType;

    @TableField(exist = false)
    private String typeName;

    /**
     * 项目负责人
     */
    @ApiModelProperty(value = "项目负责人")
    private Long projectLeaderId;

    @TableField(exist = false)
    private String projectLeaderName;

    @ApiModelProperty(value = "父id")
    private Long parentId;

    @ApiModelProperty(value = "祖级列表")
    private String ancestors;
}
