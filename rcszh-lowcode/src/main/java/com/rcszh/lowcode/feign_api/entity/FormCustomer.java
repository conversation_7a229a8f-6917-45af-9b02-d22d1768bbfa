package com.rcszh.lowcode.feign_api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.rcszh.base.common.utils.excel.ExcelModelHelper;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
@Data
public class FormCustomer {
    /** 主键 */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /** 编码 */
    @ApiModelProperty(name = "编码")
    private String code;

    /** 名称 */
    @ApiModelProperty(name = "名称")
    private String name;

    /** 简称 */
    @ApiModelProperty(name = "简称")
    private String abbreviation;

    /** 公司id */
    @ApiModelProperty(name = "公司id")
    private Long signingCompany;

    /** 公司id */
    @ApiModelProperty(name = "公司id")
    @TableField(exist = false)
    private String signingCompanyName;

    /** 集团公司 */
    @ApiModelProperty(name = "集团公司")
    private String groupCompany;

    /** 客户类型 */
    @ApiModelProperty(name = "客户类型")
    private String customerType;

    /** 客户类型 */
    @ApiModelProperty(name = "客户类型")
    private String customerTypeName;

    /** 客户等级 */
    @ApiModelProperty(name = "客户等级")
    private String customerLevel;

    /** 客户等级 */
    @ApiModelProperty(name = "客户等级")
    private String customerLevelName;

    /** 客户来源 */
    @ApiModelProperty(name = "客户来源")
    private String customerSource;

    /** 客户来源 */
    @ApiModelProperty(name = "客户来源")
    private String customerSourceName;

    /** 来源备注 */
    @ApiModelProperty(name = "来源备注")
    private String sourceRemark;

    /** 电话 */
    @ApiModelProperty(name = "电话")
    private String phone;

    /** 邮件 */
    @ApiModelProperty(name = "邮件")
    private String email;

    /** 网址 */
    @ApiModelProperty(name = "网址")
    private String website;

    /** 邮编 */
    @ApiModelProperty(name = "邮编")
    private String postalCode;

    /** 传真 */
    @ApiModelProperty(name = "传真")
    private String fax;

    /** 省份 */
    @ApiModelProperty(name = "省份")
    private String province;

    /** 省份 */
    @ApiModelProperty(name = "省份")
    private String provinceName;

    /** 城市 */
    @ApiModelProperty(name = "城市")
    private String city;

    /** 城市 */
    @ApiModelProperty(name = "城市")
    private String cityName;

    /** 区县 */
    @ApiModelProperty(name = "区县")
    private String district;

    /** 区县 */
    @ApiModelProperty(name = "区县")
    private String districtName;

    /** 地址 */
    @ApiModelProperty(name = "地址")
    @ExcelModelHelper(name = "地址", order = 8)
    private String address;

    /** 客户Logo */
    @ApiModelProperty(name = "客户Logo")
    private String customerLogo;

    /** 账期 */
    @ApiModelProperty(name = "账期")
    private String paymentTerm;

    /** 公司名称 */
    @ApiModelProperty(name = "公司名称")
    private String companyName;

    /** 统一社会信用代码 */
    @ApiModelProperty(name = "统一社会信用代码")
    private String socialCreditCode;

    /** 注册地址 */
    @ApiModelProperty(name = "注册地址")
    private String registeredAddress;

    /** 注册电话 */
    @ApiModelProperty(name = "注册电话")
    private String registeredPhone;

    /** 开户行 */
    @ApiModelProperty(name = "开户行")
    private String bankName;

    /** 银行帐号 */
    @ApiModelProperty(name = "银行帐号")
    private String accountNumber;

    /** 开票代码 */
    @ApiModelProperty(name = "开票代码")
    private String invoiceCode;

    /** 开票说明 */
    @ApiModelProperty(name = "开票说明")
    private String invoiceDescription;

    /** 备注 */
    @ApiModelProperty(name = "备注")
    private String remark;

    @ApiModelProperty(value = "联系人姓名")
    private String contactName;

    @ApiModelProperty(value = "联系人性别")
    private String contactSex;

    @ApiModelProperty(value = "联系人部门")
    private String contactDept;

    @ApiModelProperty(value = "联系人岗位")
    private String contactPosition;

    @ApiModelProperty(value = "联系人固定电话")
    private String contactFixedPhone;

    @ApiModelProperty(value = "联系人移动电话")
    private String contactMobilePhone;

    @ApiModelProperty(value = "联系人邮箱")
    private String contactEmail;

    @ApiModelProperty(value = "联系人即时通讯")
    private String contactInstantMessaging;

    @ApiModelProperty(value = "联系人地址")
    private String contactAddress;

    private String createName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


}
