package com.rcszh.lowcode.feign_api;

import com.rcszh.lowcode.feign_api.entity.FormFile;

import java.util.List;

/**
 * 上传文件
 */
public interface UploadTemplateInterface {
    /**
     * 获取上传文件列表
     *
     * @param fileIds 文件id列表
     * @return 文件列表
     */
    public List<FormFile> getUploadFileList(List<String> fileIds);

    /**
     * 获取上传文件
     *
     * @param fileId 文件id
     * @return 文件
     */
    FormFile getUploadFile(String fileId);

    /**
     * 保存上传文件列表
     *
     * @param files 文件列表
     */
    List<String> saveUploadFileList(List<FormFile> files);

    /**
     * 保存上传文件
     *
     * @param file 文件
     * @return 文件id
     */
    String saveOrUpdateFile(FormFile file);
}
