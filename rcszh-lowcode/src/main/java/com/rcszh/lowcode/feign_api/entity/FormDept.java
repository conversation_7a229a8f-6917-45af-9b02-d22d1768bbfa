package com.rcszh.lowcode.feign_api.entity;

import lombok.Data;

@Data
public class FormDept {
    /**
     * 部门ID
     */
    private Long deptId;
    /**
     * 父部门ID
     */
    private Long parentId;
    /**
     * 祖级列表
     */
    private String ancestors;
    /**
     * 部门名称
     */
    private String deptName;
    /**
     * 显示顺序
     */
    private Integer orderNum;
    /**
     * 负责人
     */
    private String leader;
    /**
     * 负责人
     */
    private String leaderName;
    /**
     * 联系电话
     */
    private String phone;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 部门状态:0正常,1停用
     */
    private String status;
    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;
    /**
     * 父部门名称
     */
    private String parentName;
    // 部门编码
    private String deptCode;
    // 公司id
    private Long companyId;
    // 0.公司 1.部门
    private String type;
}
