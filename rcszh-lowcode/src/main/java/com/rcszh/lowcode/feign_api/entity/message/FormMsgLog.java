package com.rcszh.lowcode.feign_api.entity.message;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
@Data
public class FormMsgLog implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 标题 */
    private String title;

    /** 消息内容 */
    private String message;

    /** 发送时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date sendDate;

    /** 发送状态 */
    private String sendStatus;

    /** 异常消息 */
    private String errorMsg;

    /** 扩展参数 */
    private String param;

    /** 平台 */
    private String platform;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 类型：待办、驳回，对应MsgConfig的key
     */
    private String type;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createDate;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 三方用户id
     */
    private String externalUserId;
}
