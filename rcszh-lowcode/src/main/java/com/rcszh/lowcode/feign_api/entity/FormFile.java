package com.rcszh.lowcode.feign_api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.rcszh.annotation.BaseDataTranslate;
import com.rcszh.enums.CacheDataType;
import com.rcszh.enums.CacheMethod;
import com.rcszh.lowcode.entity.base.FormBaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 表单文件
 */
@Data
public class FormFile extends FormBaseEntity {
    /**
     * 主键 ==1
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 业务ID ==0
     */
    private String busId;
    /**
     * 附件URL ==0
     */
    private String url;
    /**
     * 附件大小，单位字节 ==0
     */
    private Long size;
    /**
     * 附件名称 ==0
     */
    private String name;
    /**
     * 备注
     */
    private String remark;
    /**
     * 上传人
     */
    private Long uploadUserId;
    /**
     * 上传人
     */
    @BaseDataTranslate(dataType = CacheDataType.USER, method = CacheMethod.findById, params = "uploadUserId", showCol = "uploadUserName=name")
    private String uploadUserName;
    /**
     * 上传时间
     */
    private Date uploadDate;
    /**
     * 创建人名称
     */
    @BaseDataTranslate(dataType = CacheDataType.USER, method = CacheMethod.findById, params = "createBy", showCol = "createName=name")
    private String createName;
}
