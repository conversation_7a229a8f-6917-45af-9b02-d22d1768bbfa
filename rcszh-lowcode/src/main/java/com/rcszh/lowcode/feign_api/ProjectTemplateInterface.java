package com.rcszh.lowcode.feign_api;

import com.rcszh.lowcode.feign_api.entity.FormProject;
import com.rcszh.lowcode.feign_api.entity.FormProjectTask;

import java.util.HashMap;
import java.util.List;

public interface ProjectTemplateInterface {
    // 根据id获取项目
    FormProject getProjectById(Long id);

    // 根据id获取项目列表
    List<FormProject> getProjectListByIds(List<Long> ids);

    // 根据id获取项目任务
    FormProjectTask getProjectTaskById(Long id);

    // 根据id获取项目任务列表
    List<FormProjectTask> getProjectTaskListByIds(List<Long> ids);

    // 获取项目配置
    List<HashMap<String, Object>> getProjectConfig();
}
