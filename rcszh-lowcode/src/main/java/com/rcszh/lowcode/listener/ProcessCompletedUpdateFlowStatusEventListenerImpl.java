package com.rcszh.lowcode.listener;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rcszh.lowcode.entity.form.FormTable;
import com.rcszh.lowcode.enums.FlowStatusEnum;
import com.rcszh.lowcode.enums.FormTableTypeEnum;
import com.rcszh.lowcode.enums.action.FormActionTypeEnum;
import com.rcszh.lowcode.service.data.DynamicTableService;
import com.rcszh.lowcode.service.form.IFormTableService;
import lombok.RequiredArgsConstructor;
import org.activiti.api.process.model.ProcessInstance;
import org.activiti.api.process.runtime.events.ProcessCompletedEvent;
import org.activiti.api.process.runtime.events.listener.ProcessRuntimeEventListener;
import org.activiti.engine.RuntimeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 流程完成，更新主表状态-flowStatus
 */
@Component
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Order(1)
public class ProcessCompletedUpdateFlowStatusEventListenerImpl implements ProcessRuntimeEventListener<ProcessCompletedEvent> {

    private final RuntimeService runtimeService;

    private final IFormTableService formTableService;

    private final DynamicTableService dynamicTableService;

    @Override
    public void onEvent(ProcessCompletedEvent event) {
        ProcessInstance entity = event.getEntity();
        String businessKey = entity.getBusinessKey();

        Map<String, Object> variables = runtimeService.getVariables(entity.getId());
        String formId = (String) variables.get("formId");
        FormTable mainTable = formTableService.getOne(new LambdaQueryWrapper<FormTable>()
                .eq(FormTable::getFormId, formId)
                .eq(FormTable::getType, FormTableTypeEnum.MAIN.getType())
        );
        Map<String, Object> data = dynamicTableService.getDynamicTableDataById(mainTable.getTableName(), businessKey);
        if (FlowStatusEnum.running.name().equals(data.get("flow_status")) || FlowStatusEnum.draft.name().equals(data.get("flow_status"))) {
            Map<String, Object> jdbcData = new HashMap<>();
            jdbcData.put("id", businessKey);
            jdbcData.put("flow_status", FlowStatusEnum.passed.name());
            dynamicTableService.doActionToDynamicTable(mainTable.getTableName(), FormActionTypeEnum.UPDATE.getType(), jdbcData);
            System.out.println("ProcessCompletedEventListenerImpl onEvent");
        }

    }
}
