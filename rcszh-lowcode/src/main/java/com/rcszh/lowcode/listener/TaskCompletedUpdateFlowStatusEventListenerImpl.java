package com.rcszh.lowcode.listener;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rcszh.activiti.constant.VariableConstants;
import com.rcszh.lowcode.entity.form.FormTable;
import com.rcszh.lowcode.enums.FlowStatusEnum;
import com.rcszh.lowcode.enums.FormTableTypeEnum;
import com.rcszh.lowcode.enums.action.FormActionTypeEnum;
import com.rcszh.lowcode.enums.activity.ApproveActionEnum;
import com.rcszh.lowcode.service.data.DynamicTableService;
import com.rcszh.lowcode.service.form.IFormTableService;
import lombok.RequiredArgsConstructor;
import org.activiti.api.task.model.Task;
import org.activiti.api.task.runtime.events.TaskCompletedEvent;
import org.activiti.api.task.runtime.events.listener.TaskEventListener;
import org.activiti.engine.TaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 任务完成，更新主表状态-flowStatus
 */
@Component
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class TaskCompletedUpdateFlowStatusEventListenerImpl implements TaskEventListener<TaskCompletedEvent> {

    private final TaskService taskService;

    private final IFormTableService formTableService;

    private final DynamicTableService dynamicTableService;


    @Override
    public void onEvent(TaskCompletedEvent event) {
        Task entity = event.getEntity();
        String businessKey = entity.getBusinessKey();
        Map<String, Object> variables = taskService.getVariables(entity.getId());
        String action = (String) variables.get(VariableConstants.ACTION);
        String flowStatus = "";
        if (ApproveActionEnum.refuse.name().equals(action)) {
            flowStatus = FlowStatusEnum.refused.name();
        } else if (ApproveActionEnum.cancel.name().equals(action)) {
            flowStatus = FlowStatusEnum.canceled.name();
        } else {
            return;
        }

        String formId = (String) variables.get("formId");
        FormTable mainTable = formTableService.getOne(new LambdaQueryWrapper<FormTable>()
                .eq(FormTable::getFormId, formId)
                .eq(FormTable::getType, FormTableTypeEnum.MAIN.getType())
        );
        Map<String, Object> jdbcData = new HashMap<>();
        jdbcData.put("id", businessKey);
        jdbcData.put("flow_status", flowStatus);
        dynamicTableService.doActionToDynamicTable(mainTable.getTableName(), FormActionTypeEnum.UPDATE.getType(), jdbcData);

        System.out.println("TaskCompletedEventListenerImpl onEvent");
    }
}
