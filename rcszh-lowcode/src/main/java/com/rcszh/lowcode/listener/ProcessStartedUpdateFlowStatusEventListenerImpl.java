package com.rcszh.lowcode.listener;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rcszh.lowcode.entity.form.FormTable;
import com.rcszh.lowcode.enums.FlowStatusEnum;
import com.rcszh.lowcode.enums.FormTableTypeEnum;
import com.rcszh.lowcode.enums.action.FormActionTypeEnum;
import com.rcszh.lowcode.service.data.DynamicTableService;
import com.rcszh.lowcode.service.form.IFormTableService;
import lombok.RequiredArgsConstructor;
import org.activiti.api.process.model.ProcessInstance;
import org.activiti.api.process.runtime.events.ProcessStartedEvent;
import org.activiti.api.process.runtime.events.listener.ProcessRuntimeEventListener;
import org.activiti.engine.RuntimeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 流程完成，更新主表状态-flowStatus
 *
 *  注意：当流程没有用户节点时：整个流程只有一个事务（started事务），事务顺序会是：ProcessCompletedEvent --> ProcessStartedEvent(最后)
 */
@Component
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class ProcessStartedUpdateFlowStatusEventListenerImpl implements ProcessRuntimeEventListener<ProcessStartedEvent> {

    private final RuntimeService runtimeService;

    private final IFormTableService formTableService;

    private final DynamicTableService dynamicTableService;

    @Override
    public void onEvent(ProcessStartedEvent event) {
        ProcessInstance entity = event.getEntity();
        String businessKey = entity.getBusinessKey();

        Map<String, Object> variables = runtimeService.getVariables(entity.getId());
        if (Objects.isNull(variables)) {
            return;
        }
        String formId = (String) variables.get("formId");
        FormTable mainTable = formTableService.getOne(new LambdaQueryWrapper<FormTable>()
                .eq(FormTable::getFormId, formId)
                .eq(FormTable::getType, FormTableTypeEnum.MAIN.getType())
        );

        Map<String, Object> data = dynamicTableService.getDynamicTableDataById(mainTable.getTableName(), businessKey);
        if (!FlowStatusEnum.running.name().equals(data.get("flow_status")) && !FlowStatusEnum.passed.name().equals(data.get("flow_status"))) {
            Map<String, Object> jdbcData = new HashMap<>();
            jdbcData.put("id", businessKey);
            jdbcData.put("flow_status", FlowStatusEnum.running.name());
            dynamicTableService.doActionToDynamicTable(mainTable.getTableName(), FormActionTypeEnum.UPDATE.getType(), jdbcData);
        }


    }
}
