package com.rcszh.lowcode.utils;

import cn.hutool.core.date.DateUtil;
import com.rcszh.lowcode.core.UiComponentFactory;
import com.rcszh.lowcode.dto.FormTableFieldDto;
import com.rcszh.lowcode.enums.form_table_field.FormTableFieldJdbcTypeConstant;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Data;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 列表数据导出工具类
 */
public class DataExportUtil {


    public static void download(HttpServletResponse response, List<FormTableFieldDto> fields, List<Map<String, Object>> data) {
        // 这里注意 有同学反应使用swagger 会导致各种问题，请直接用浏览器或者用postman
//        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
//        response.setCharacterEncoding("utf-8");
//        // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
//        String fileName = URLEncoder.encode("测试", StandardCharsets.UTF_8).replaceAll("\\+", "%20");
//        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
//        ExportRes exportRes = getExportRes(fields, data);
//        try {
//            EasyExcel.write(response.getOutputStream()).head(exportRes.getHead()).sheet("导出数据").doWrite(exportRes.getData());
//        } catch (IOException e) {
//            throw new RuntimeException("导出数据失败：" + e);
//        }
        ExportRes exportRes = getExportRes(fields, data);
        // 创建 Excel 工作簿
        Workbook workbook = new XSSFWorkbook();
        // 创建工作表
        Sheet sheet = workbook.createSheet("示例");

        // 构造表头
        Row headerRow = sheet.createRow(0);
        List<String> headers = exportRes.getHead().stream().map(List::getFirst).toList();
        for (int i = 0; i < headers.size(); i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers.get(i));
        }

        // 填充数据
        List<List<Object>> excelData = exportRes.getData();
        for (int i = 0; i < data.size(); i++) {
            Row dataRow = sheet.createRow(i + 1);  // 从第二行开始填充数据
            List<Object> rowData = excelData.get(i);
            for (int j = 0; j < rowData.size(); j++) {
                Cell cell = dataRow.createCell(j);
                cell.setCellValue(rowData.get(j) == null ? "" : rowData.get(j).toString());
            }
        }

        // 设置响应头，触发下载
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=example.xlsx");

        // 将工作簿输出到响应流中
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            workbook.write(outputStream);
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            try {
                workbook.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }

    /**
     * excel头
     */
    private static ExportRes getExportRes(List<FormTableFieldDto> fields, List<Map<String, Object>> data) {
        ExportRes exportRes = new ExportRes();
        List<List<Object>> dataInfo = new ArrayList<>();
        // excel头行缓存
        Map<String, Integer> headMap = new HashMap<>();
        List<List<String>> heads = new ArrayList<>();
        Map<String, FormTableFieldDto> fieldDtoMap = new HashMap<>();
        for (FormTableFieldDto field : fields) {
            // 遍历字段
            List<String> head = new ArrayList<>();
            String headName = field.getLabel();
            Integer num = headMap.get(headName);
            if (num != null) {
                num++;
                headMap.put(headName, num);
                headName = headName + num;
            } else {
                headMap.put(headName, 1);
            }
            head.add(headName);
            heads.add(head);
            fieldDtoMap.put(field.getName(), field);
        }
        exportRes.setHead(heads);
        if (data != null) {
            UiComponentFactory.transformDataList2ComponentByDto(fields, data);
            for (Map<String, Object> item : data) {
                List<Object> dataList = new ArrayList<>();
                for (FormTableFieldDto field : fields) {
                    String key = field.getName();
                    FormTableFieldDto formTableFieldDto = fieldDtoMap.get(key);
                    String jdbcType = formTableFieldDto.getJdbcType();
                    Object value = item.get(key);
                    if (FormTableFieldJdbcTypeConstant.DATA_TIME.equals(jdbcType)) {
                        value = value == null ? null : DateUtil.parse(value.toString()).toString("yyyy-MM-dd");
                    } else if (value instanceof Map<?, ?> map) {
                        value = map.get("name");
                    }
                    dataList.add(value);
                }
                dataInfo.add(dataList);
            }
        }
        exportRes.setData(dataInfo);
        return exportRes;
    }

    @Data
    private static class ExportRes {
        private List<List<String>> head;
        private List<List<Object>> data;
    }

}
