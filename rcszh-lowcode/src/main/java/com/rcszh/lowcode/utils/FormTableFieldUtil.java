package com.rcszh.lowcode.utils;

import com.rcszh.lowcode.constant.InitFieldConstant;
import com.rcszh.lowcode.entity.base.FormBaseEntity;
import com.rcszh.lowcode.entity.form.FormTableField;
import com.rcszh.lowcode.enums.form_table_field.InitFieldTypeEnum;

import java.util.ArrayList;
import java.util.List;

public class FormTableFieldUtil {
    /**
     * 获取初始化字段
     *
     * @param formId      表单ID
     * @param formTableId 表单表ID
     * @return 初始化字段列表
     */
    public static List<FormTableField> getInitFields(String formId, String formTableId) {
        List<FormTableField> result = new ArrayList<>();
        for (InitFieldTypeEnum type : InitFieldTypeEnum.values()) {
            switch (type) {
                case ID:
                    result.add(InitFieldConstant.ID(formId, formTableId));
                    break;
                case CREATOR:
                    result.add(InitFieldConstant.CREATOR(formId, formTableId));
                    break;
                case CREATOR_DEPT:
                    result.add(InitFieldConstant.CREATOR_DEPT(formId, formTableId));
                    break;
                case CREATE_TIME:
                    result.add(InitFieldConstant.CREATE_TIME(formId, formTableId));
                    break;
                case OWNER:
                    result.add(InitFieldConstant.OWNER(formId, formTableId));
                    break;
                case OWNER_DEPT:
                    result.add(InitFieldConstant.OWNER_DEPT(formId, formTableId));
                    break;
                case UPDATER:
                    result.add(InitFieldConstant.UPDATER(formId, formTableId));
                    break;
                case UPDATE_TIME:
                    result.add(InitFieldConstant.UPDATE_TIME(formId, formTableId));
                    break;
                case FLOW_STATUS:
                    result.add(InitFieldConstant.FLOW_STATUS(formId, formTableId));
                    break;
                case FORM_ID:
                    result.add(InitFieldConstant.FORM_ID(formId, formTableId));
                    break;
            }
        }
        result.forEach(FormBaseEntity::fillBaseFields);
        return result;
    }
}
