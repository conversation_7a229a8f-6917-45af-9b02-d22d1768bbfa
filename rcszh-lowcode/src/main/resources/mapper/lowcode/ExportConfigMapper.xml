<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rcszh.lowcode.mapper.excel.ExportConfigMapper">
    <resultMap id="ExportConfigResultMap" type="com.rcszh.lowcode.entity.excel.ExportConfig">
        <id property="id" column="id"/>
        <result property="exportType" column="export_type"/>
        <result property="exportFields" column="export_fields" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="name" column="name"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    <!-- 可根据需要添加自定义SQL -->
</mapper> 