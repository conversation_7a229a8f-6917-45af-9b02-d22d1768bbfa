<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rcszh.lowcode.mapper.form.FormMapper">

    <!-- 结果映射 -->
    <resultMap id="FormMenu" type="com.rcszh.lowcode.dto.form.FormMenuDto">
        <result property="formName" column="form_name"/>
        <result property="formId" column="form_id"/>
        <collection property="viewFormList" ofType="com.rcszh.lowcode.entity.view.ViewForm">
            <result property="id" column="id"/>
            <result property="formId" column="form_id"/>
            <result property="name" column="name"/>

        </collection>
    </resultMap>


    <select id="getAllGroup" resultType="java.lang.String">
        select distinct group_name
        from form
    </select>
    <select id="getFormMenu" resultMap="FormMenu">
        select f.name as form_name,f.id as form_id ,v.*
        from form f
                 left join view_form v on f.id = v.form_id and v.type = 'list'
    </select>
</mapper>