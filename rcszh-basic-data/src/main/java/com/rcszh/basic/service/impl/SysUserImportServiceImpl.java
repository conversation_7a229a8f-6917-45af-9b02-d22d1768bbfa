package com.rcszh.basic.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rcszh.base.common.core.domain.ImportResult;
import com.rcszh.base.common.core.domain.entity.SysUser;
import com.rcszh.base.common.exception.ServiceException;
import com.rcszh.base.common.utils.SecurityUtils;
import com.rcszh.base.common.utils.StringUtils;
import com.rcszh.base.common.utils.excel.ExcelUtils;
import com.rcszh.base.common.utils.file.FileUploadUtils;
import com.rcszh.basic.domain.SysApplication;
import com.rcszh.basic.domain.SysExternalUser;
import com.rcszh.basic.enums.PlatformType;
import com.rcszh.basic.mapper.SysUserExtMapper;
import com.rcszh.basic.service.ISysApplicationService;
import com.rcszh.basic.service.ISysCommonService;
import com.rcszh.basic.service.ISysExternalService;
import com.rcszh.basic.service.ISysUserImportService;
import com.rcszh.common.util.InputStreamToMultipartFile;
import com.rcszh.system.service.ISysConfigService;
import com.rcszh.system.service.ISysPasswordRuleSettingService;
import com.rcszh.system.service.ISysUserService;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SysUserImportServiceImpl implements ISysUserImportService {

    @Autowired
    private SysUserExtMapper sysUserExtMapper;

    @Autowired
    private ISysExternalService sysExternalService;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private ISysApplicationService applicationService;

    @Autowired
    private ISysCommonService sysCommonService;

    @Autowired
    private ISysPasswordRuleSettingService iSysPasswordRuleSettingService;

    private List<Map<String, String>> parseExeclUserData(MultipartFile file, int headerNum) {
        List<Map<String, String>> result = new ArrayList<>();
        try {
            Workbook workbook = WorkbookFactory.create(file.getInputStream());
            Sheet sheet = workbook.getSheetAt(0);
            int rows = sheet.getLastRowNum();
            if (rows > 0) {
                Map<Integer, String> titleMap = new HashMap<>();
                Row heard = sheet.getRow(headerNum);
                for (int i = 0; i < heard.getPhysicalNumberOfCells(); i++) {
                    Cell cell = heard.getCell(i);
                    if (StringUtils.isNotNull(cell)) {
                        String stringCellValue = cell.getStringCellValue();
                        titleMap.put(i, stringCellValue);
                    } else {
                        titleMap.put(i, null);
                    }
                }
                for (int i = headerNum + 1; i <= rows; i++) {
                    Row row = sheet.getRow(i);
                    HashMap<String, String> obj = new HashMap<>();
                    for (Map.Entry<Integer, String> entry : titleMap.entrySet()) {
                        Integer index = entry.getKey();
                        Cell cell = row.getCell(index);
                        if (cell == null) {
                            continue;
                        }
                        String value = "";
                        if (cell.getCellType() == CellType.NUMERIC) {
                            value = (new DecimalFormat("0")).format(cell.getNumericCellValue());
                        } else {
                            value = cell.getStringCellValue();
                        }
                        obj.put(entry.getValue(), value);
                    }
                    result.add(obj);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
        return result;
    }

    @Override
    public ImportResult importWechatUser(MultipartFile file, Long companyId) throws IOException {
        SysApplication application = applicationService.getByPlatformAndCompanyId(PlatformType.wechat.getCode(), companyId);
        if (Objects.isNull(application)) {
            throw new ServiceException("未找到企业微信应用");
        }
        int titleRowNum = 10;
        List<Map<String, String>> data = parseExeclUserData(file, titleRowNum);
        Map<Integer, String> errMsgMap = new HashMap<>();
        ImportResult result = new ImportResult();
        result.setCount(data.size());

        List<SysUser> sysUsers = sysUserExtMapper.selectAll();
        Map<String, SysUser> sysUserMap = sysUsers.stream().collect(Collectors.toMap(SysUser::getUserName, t -> t, (k1, k2) -> k1));
        List<SysExternalUser> sysExternalUsers = sysExternalService.list(new LambdaQueryWrapper<SysExternalUser>().eq(SysExternalUser::getAppId, application.getId()));
        Map<String, SysExternalUser> externalUserMap = sysExternalUsers.stream().collect(Collectors.toMap(item -> StrUtil.format("{}_{}", item.getAppId(), item.getExternalUserId()), t -> t, (k1, k2) -> k1));

        int index = 1;
        for (Map<String, String> row : data) {
            String nickName = row.get("姓名");
            String userid = row.get("账号");
            String email = row.get("企业邮箱");
            String phonenumber = row.get("手机");
            String sex = "男".equals(row.get("性别")) ? "0" : "女".equals(row.get("性别")) ? "1" : "2";
            String workNumber = row.get("工号"); // 看情况调整

            StringBuilder errorMsg = new StringBuilder();
            if (StrUtil.isBlank(nickName)) {
                errorMsg.append("姓名不能为空;");
            }
            if (StrUtil.isBlank(userid)) {
                errorMsg.append("账号不能为空;");
            }
            if (StrUtil.isNotBlank(errorMsg)) {
                errMsgMap.put(index, errorMsg.toString());
                continue;
            }
            String userName = StrUtil.isBlank(workNumber) ? userid : workNumber;

            SysUser sysUser = sysUserMap.get(userName);
            if (Objects.isNull(sysUser)) {
                sysUser = new SysUser();
                sysUser.setUserName(userName);
                sysUser.setNickName(nickName);
                sysUser.setEmail(email);
                sysUser.setPhonenumber(phonenumber);
                sysUser.setSex(sex);
                sysUser.setPassword(SecurityUtils.encryptPassword(iSysPasswordRuleSettingService.getDefaultPwd()));
                sysUser.setCreateBy(SecurityUtils.getUsername());
                sysUser.setCreateTime(new Date());
                sysUser.setRoleIds(sysCommonService.getDefaultRoleIds());
                sysUserService.insertUser(sysUser);
            }
            String key = StrUtil.format("{}_{}", application.getId(), userid);
            SysExternalUser externalUser = externalUserMap.get(key);
            if (Objects.isNull(externalUser)) {
                externalUser = new SysExternalUser();
                externalUser.setPlatform(application.getPlatform());
                externalUser.setAppId(application.getId());
                externalUser.setExternalUserId(userid);
                externalUser.setUserId(sysUser.getUserId());
                externalUser.fillBaseFields();
                sysExternalService.save(externalUser);
                externalUserMap.put(key, externalUser);
            }
            index++;
        }

        result.setSuccessCount(result.getCount() - errMsgMap.size());
        result.setErrorCount(errMsgMap.size());
        if (!errMsgMap.isEmpty()) {
            InputStream stream = ExcelUtils.writeErrMessageToExcel(file, errMsgMap, titleRowNum);
            MultipartFile multipartFile = InputStreamToMultipartFile.convert(stream, "用户导入错误信息.xlsx", file.getContentType());
            String errFileUrl = FileUploadUtils.upload(multipartFile);
            result.setErrFileUrl(errFileUrl);
        }

        return result;
    }

    @Override
    public ImportResult importDingUser(MultipartFile file, Long companyId) throws IOException {
        SysApplication application = applicationService.getByPlatformAndCompanyId(PlatformType.dingTalk.getCode(), companyId);
        if (Objects.isNull(application)) {
            throw new ServiceException("未找到企业微信应用");
        }
        int titleRowNum = 2;
        List<Map<String, String>> data = parseExeclUserData(file, titleRowNum);
        Map<Integer, String> errMsgMap = new HashMap<>();
        ImportResult result = new ImportResult();
        result.setCount(data.size());

        List<SysUser> sysUsers = sysUserExtMapper.selectAll();
        Map<String, SysUser> sysUserMap = sysUsers.stream().collect(Collectors.toMap(SysUser::getPhonenumber, t -> t, (k1, k2) -> k1));
        List<SysExternalUser> sysExternalUsers = sysExternalService.list(new LambdaQueryWrapper<SysExternalUser>().eq(SysExternalUser::getAppId, application.getId()));
        Map<String, SysExternalUser> sysDingUserMap = sysExternalUsers.stream().collect(Collectors.toMap(item -> StrUtil.format("{}_{}", item.getAppId(), item.getExternalUserId()), t -> t, (k1, k2) -> k1));
        int index = 1;
        for (Map<String, String> row : data) {
            String nickName = row.get("姓名");
            String userid = row.get("员工ID");
            String email = row.get("邮箱");
            String phonenumber = row.get("手机号码");
            if (StrUtil.isNotBlank(phonenumber) && phonenumber.startsWith("+86-")) {
                phonenumber = phonenumber.substring(4);
            }
            String deptName = row.get("部门");
            String position = row.get("职位");

            StringBuilder errorMsg = new StringBuilder();
            if (StrUtil.isBlank(nickName)) {
                errorMsg.append("姓名不能为空;");
            }
            if (StrUtil.isBlank(userid)) {
                errorMsg.append("员工ID不能为空;");
            }
            if (StrUtil.isBlank(phonenumber)) {
                errorMsg.append("手机号码不能为空;");
            }
            if (StrUtil.isNotBlank(errorMsg)) {
                errMsgMap.put(index, errorMsg.toString());
                continue;
            }

            SysUser sysUser = sysUserMap.get(phonenumber);
            if (Objects.isNull(sysUser)) {
                sysUser = new SysUser();
                sysUser.setUserName(phonenumber);
                sysUser.setNickName(nickName);
                sysUser.setEmail(email);
                sysUser.setPhonenumber(phonenumber);
                sysUser.setPassword(SecurityUtils.encryptPassword(iSysPasswordRuleSettingService.getDefaultPwd()));
                if (StrUtil.isNotBlank(deptName)) {
                    sysUser.setDeptName(deptName);
                }
                if (StrUtil.isNotBlank(position)) {
                    sysUser.setPosition(position);
                }
                sysUser.setCreateBy(SecurityUtils.getUsername());
                sysUser.setCreateTime(new Date());
                sysUser.setRoleIds(sysCommonService.getDefaultRoleIds());
                sysUserService.insertUser(sysUser);
            }

            String key = StrUtil.format("{}_{}", application.getId(), userid);
            SysExternalUser externalUser = sysDingUserMap.get(key);
            if (Objects.isNull(externalUser)) {
                externalUser = new SysExternalUser();
                externalUser.setPlatform(application.getPlatform());
                externalUser.setAppId(application.getId());
                externalUser.setExternalUserId(userid);
                externalUser.setUserId(sysUser.getUserId());
                externalUser.fillBaseFields();
                sysExternalService.save(externalUser);
                sysDingUserMap.put(key, externalUser);
            }
            index++;
        }

        result.setSuccessCount(result.getCount() - errMsgMap.size());
        result.setErrorCount(errMsgMap.size());
        if (!errMsgMap.isEmpty()) {
            InputStream stream = ExcelUtils.writeErrMessageToExcel(file, errMsgMap, titleRowNum);
            MultipartFile multipartFile = InputStreamToMultipartFile.convert(stream, "用户导入错误信息.xlsx", file.getContentType());
            String errFileUrl = FileUploadUtils.upload(multipartFile);
            result.setErrFileUrl(errFileUrl);
        }

        return result;
    }
}
