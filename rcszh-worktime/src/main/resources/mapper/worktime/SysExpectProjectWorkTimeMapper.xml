<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rcszh.worktime.mapper.SysExpectProjectWorkTimeMapper">

    <select id="findAll" resultType="com.rcszh.worktime.domain.expect_work.SysExpectProjectWorkTime">
        SELECT w.*,p.name as project_name,p.code as project_code from sys_expect_project_work_time w
        left join pms_project p on p.id = w.project_id
        <where>
            <if test="projectId != null">
                and p.id = #{projectId}
            </if>
            <if test="expectDate != null">
                and w.expect_date = #{expectDate}
            </if>
        </where>
    </select>

    <select id="findAllWithUserWorkTime"
            resultType="com.rcszh.worktime.domain.expect_work.SysExpectProjectWorkTime">
        SELECT w.id,
        w.project_id,
        w.expect_work_time,
        w.expect_date,
        w.create_time,
        w.create_by,
        w.update_time,
        w.update_by,
        p.name as project_name,
        p.code as project_code,
        sum(uw.expect_work_time) as expect_work_time
        from sys_expect_project_work_time w
        left join pms_project p on p.id = w.project_id
        left join sys_project_user_work_time uw on uw.expect_project_id = w.id
        <where>
            <if test="projectId != null">
                and p.id = #{projectId}
            </if>
            <if test="expectDate != null">
                and w.expectDate = #{expectDate}
            </if>
        </where>
        group by uw.expect_work_time
    </select>

    <select id="findEveryMonthWorkTime"
            resultType="com.rcszh.worktime.domain.dto.expect_work_time.EveryMonthWorkTime">
        SELECT month(expect_date) as month,
        sum(expect_work_time) as work_time
        from sys_expect_project_work_time
        <where>
            and expect_date between #{start} and #{end}
            <if test="param.projectId != null">
                and project_id = #{param.projectId}
            </if>
        </where>
        group by month
    </select>

    <select id="findProjectDiffRange"
            resultType="com.rcszh.worktime.domain.dto.expect_work_time.ProjectDiffRangeDto">
        SELECT id as project_id,name as project_name, (sum(asum)-sum(bsum))/sum(bsum) as diff_range FROM
        (
        SELECT p.id,p.name,sum(a.work_duration) as asum,0 as bsum FROM pms_project p LEFT JOIN sys_work_record a ON
        a.project_id = p.id
        Where a.work_time between #{start} and #{end}
        <if test="param.projectId != null">
            and p.id = #{param.projectId}
        </if>
        GROUP BY p.id
        UNION
        SELECT p.id,p.name,0 as asum,SUM(b.expect_work_time) as bsum FROM pms_project p LEFT JOIN
        sys_expect_project_work_time b ON b.project_id = p.id
        Where b.expect_date between #{start} and #{end}
        <if test="param.projectId != null">
            and p.id = #{param.projectId}
        </if>
        GROUP BY p.id
        ) as union_t
        GROUP BY id

    </select>

    <sql id="base_column_list">
        <where>
            <if test="projectId != null">
                and p.id = #{projectId}
            </if>
        </where>
    </sql>

</mapper>