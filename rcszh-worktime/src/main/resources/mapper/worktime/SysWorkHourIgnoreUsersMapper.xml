<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rcszh.worktime.mapper.SysWorkHourIgnoreUsersMapper">
    <select id="queryPage" resultType="com.rcszh.worktime.domain.SysWorkHourIgnoreUsers">
        select u.id,su.user_id,su.user_name,su.nick_name,su.phonenumber,u.create_by,u.create_time from sys_work_hour_ignore_users u
        left join sys_user su on su.user_id = u.user_id
        <where>
            <if test="nickName != null">
                nick_name like concat('%', #{nickName}, '%')
            </if>
        </where>
    </select>
</mapper>