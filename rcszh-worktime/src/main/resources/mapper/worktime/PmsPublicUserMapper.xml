<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rcszh.worktime.mapper.PmsPublicUserMapper">

    <select id="selectUnallocatedList" parameterType="SysUser" resultType="SysUser">
        select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phonenumber, u.status, u.create_time
        from sys_user u
        left join pms_public_user p on p.user_id = u.user_id and p.deleted = 'N'
        where p.user_id is null
          and u.status='0'
          and u.del_flag='0'
          and u.user_name != 'admin'
        <if test="searchValue != null and searchValue != ''">
            AND (
            u.user_name like concat('%', #{searchValue}, '%')
            OR u.nick_name like concat('%', #{searchValue}, '%')
            )
        </if>
        <if test="phonenumber != null and phonenumber != ''">
            AND u.phonenumber like concat('%', #{phonenumber}, '%')
        </if>
        <if test="userName != null and userName != ''">
            AND u.user_name like concat('%', #{userName}, '%')
        </if>
        <if test="nickName != null and nickName != ''">
            AND u.nick_name like concat('%', #{nickName}, '%')
        </if>
    </select>
</mapper>