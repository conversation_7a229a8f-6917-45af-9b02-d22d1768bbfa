<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rcszh.worktime.mapper.SysExpectUserWorkTimeMapper">


    <select id="findEveryMonthWorkTime"
            resultType="com.rcszh.worktime.domain.dto.expect_work_time.EveryMonthWorkTime">
        SELECT DATE_FORMAT(expect_date, '%Y-%m') as month,
        sum(expect_work_time) as total_work_time
        from sys_expect_user_work_time
        <where>
            <if test="start != null and end != null">
                and expect_work_time between #{start} and #{end}
            </if>
            <if test="param.projectId != null">
                and project_id = #{param.projectId}
            </if>
        </where>
        group by month
    </select>

    <select id="findUserDiffRange"
            resultType="com.rcszh.worktime.domain.vo.ExpectWorkTimeComparePieVo">
        SELECT
        CASE
        <foreach collection="rangeConfig" item="range" separator=" ">
            WHEN diff > #{range.min} AND diff &lt;= #{range.max} THEN #{range.rangeTitle}
        </foreach>
        ELSE '其他'
        END AS name,
        COUNT(*) AS value
        FROM (
        SELECT
        (a.work_duration - sum(c.expect_work_time))/sum(c.expect_work_time) AS diff,
        b.project_id as project_id
        FROM
        sys_work_record a
        INNER JOIN sys_expect_project_work_time b
        ON a.project_id = b.project_id
        LEFT JOIN sys_expect_user_work_time c
        ON b.id = c.expect_project_id
        ) AS subquery
        GROUP BY name
    </select>


</mapper>