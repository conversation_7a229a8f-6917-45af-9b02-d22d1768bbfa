<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rcszh.worktime.mapper.SysWorkRecordMapper">
    <select id="monthStat" resultType="SysWorkRecord">
        select wr.user_id,
               wr.work_time,
               sum(wr.work_duration) as work_duration,
               sum(wr.regular_work_duration) as regular_work_duration
        from sys_work_record wr,sys_project p
        where wr.project_id=p.id
          and wr.work_time >= #{startTime}
          and wr.work_time &lt;= #{endTime}
          and wr.status='audited'
        <if test="userId != null and userId !='' ">
            and wr.user_id = #{userId}
        </if>
        <if test="userIds != null">
            and wr.user_id in
            <foreach collection="userIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        group by wr.user_id,wr.work_time
    </select>

    <select id="findWorkRecordUserIds" resultType="Long">
        select wr.user_id
        from sys_work_record wr,sys_project p
        where wr.project_id=p.id
        and wr.work_time >= #{startTime}
        and wr.work_time &lt;= #{endTime}
        and wr.status='audited'
        <if test="userId != null and userId !='' ">
            and wr.user_id = #{userId}
        </if>
        group by wr.user_id
        order by wr.user_id
    </select>

    <select id="projectWorkTimeTotal" resultType="SysWorkRecord">
        select
            wr.project_id,
            sum(wr.work_duration) as work_duration
        from sys_work_record wr
        where wr.status = 'audited'
          and wr.type='entry'
          and wr.work_time >= #{startTime}
          and wr.work_time &lt;= #{endTime}
        group by wr.project_id
    </select>
    <select id="queryUserSubmissionStatus" resultType="java.util.Map">
        SELECT
        DATE(r.work_time) AS fillDate,
        COUNT(DISTINCT r.user_id) AS submissionCount,
        total_users AS totalCount,
        CONCAT(ROUND((COUNT(DISTINCT r.user_id) / total_users) * 100, 2), '%') AS submissionRate
        FROM sys_work_record r
        JOIN (
        SELECT COUNT(1) AS total_users
        FROM sys_user
        WHERE status = '0'
        ) AS totals
        -- 计算填报人数
        WHERE r.work_time &gt;= #{startDate}
        AND r.work_time &lt;= #{endDate}
        GROUP BY DATE(r.work_time);
    </select>
    <select id="userReportAnalysis" resultType="com.rcszh.basic.domain.dto.UserReportAnalysisDTO">
        select
            swr.create_time,
            swr.work_time,
            su.user_name,
            su.nick_name,
            sp.code,
            sp.name,
            case when swr.`type` = 'entry' then '录入工时' when swr.`type` = 'share' then '分摊工时' end type,
            swr.status,
            swr.work_duration,
            swr.max_work_duration,
            swr.regular_work_duration,
            swr.standard_work_duration,
            wta.id approve_id,
            sp.project_manager_id,
            swr.remark
        from
        sys_work_record swr
        left join (select wta.id,MAX(wtal.work_time) work_time,MAX(wtal.project_id) project_id,MAX(wtal.user_id) user_id from work_time_approve wta
        left join work_time_approve_line wtal on wta.id = wtal.work_time_approve_id where wta.flow_status = 'running'
        group by wtal.work_time,wtal.project_id,wtal.user_id) wta on wta.work_time = swr.work_time and wta.project_id = swr.project_id and wta.user_id = swr.user_id
        left join sys_user su on su.user_id = swr.user_id
        left join pms_project sp on sp.id = swr.project_id
        where 1=1
        <if test="userId != null">
            and su.user_id = #{userId}
        </if>
        <if test="projectName != null and projectName != ''">
            and sp.name like concat('%', #{projectName}, '%')
        </if>
        <if test="projectId != null and projectId != ''">
            and sp.id = #{projectId}
        </if>
        <if test="startDate != null">
            and swr.create_time &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            and swr.create_time &lt;= #{endDate}
        </if>
        <if test="workTimeStartDate != null">
            and swr.work_time &gt;= #{workTimeStartDate}
        </if>
        <if test="workTimeEndDate != null">
            and swr.work_time &lt;= #{workTimeEndDate}
        </if>
        <if test="status != null">
            and swr.status = #{status}
        </if>
        <if test="deptId != null">
            AND (su.dept_id = #{deptId} OR su.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{deptId}, ancestors) ))
        </if>
        order by swr.work_time asc
    </select>

    <select id="findWorkRecordByDepartment" resultType="com.rcszh.worktime.domain.SysWorkRecord">
        select r.* from sys_work_record r
        inner join pms_project p on r.project_id = p.id
        where 1=1
        <if test="projectName != null">
            and p.name like concat('%', #{projectName}, '%')
        </if>
        <if test="workTimeStartDate != null">
            and r.work_time &gt;= #{workTimeStartDate}
        </if>
        <if test="workTimeEndDate != null">
            and r.work_time &lt;= #{workTimeEndDate}
        </if>
        <if test="projectManagerId != null">
            and p.project_manager_id = #{projectManagerId}
        </if>
    </select>
    <select id="getEveryMonthWorkTime"
            resultType="com.rcszh.worktime.domain.dto.expect_work_time.EveryMonthWorkTime">
        select month(wr.work_time) as month,
        sum(wr.work_duration) as work_time
        from sys_work_record wr
        where wr.status = 'audited'
        and wr.type='entry'
        and wr.work_time between #{start} and #{end}
        <if test="param.projectId != null">
            and wr.project_id = #{param.projectId}
        </if>
        group by month
    </select>
    <select id="findProjectTotalWorkTime" resultType="java.lang.Long">
        select sum(wr.work_duration)
        from sys_work_record wr
        where wr.status = 'audited'
        and wr.type='entry'
        and wr.work_time between #{start} and #{end}
        and wr.project_id = #{projectId}
    </select>
    <select id="findProjectUserTotalWorkTime"
            resultType="com.rcszh.worktime.domain.dto.ProjectUserWorkTimeProportionDto">
        select su.nick_name, sum(wr.work_duration) as work_time
        from sys_work_record wr
        left join sys_user su on wr.user_id = su.user_id
        where wr.status = 'audited'
          and wr.type='entry'
          and wr.work_time between #{start} and #{end}
          and wr.project_id = #{projectId}
        group by wr.user_id
    </select>
</mapper>