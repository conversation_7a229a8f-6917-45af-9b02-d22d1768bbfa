package com.rcszh.worktime.mapper;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rcszh.basic.enums.SysExpectConfigEnum;
import com.rcszh.worktime.domain.dto.expect_work_time.EveryMonthWorkTime;
import com.rcszh.worktime.domain.dto.expect_work_time.ExpectWorkTimeCompareDto;
import com.rcszh.worktime.domain.expect_work.SysExpectUserWorkTime;
import com.rcszh.worktime.domain.vo.ExpectWorkTimeComparePieVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface SysExpectUserWorkTimeMapper extends BaseMapper<SysExpectUserWorkTime> {
    // 获取每个月的用户预估工时
    List<EveryMonthWorkTime> findEveryMonthWorkTime(DateTime start, DateTime end, ExpectWorkTimeCompareDto param);

    // 统计图专用：获取指定差异范围内的用户预估工时
    List<ExpectWorkTimeComparePieVo> findUserDiffRange(List<SysExpectConfigEnum.DiffRange> rangeConfig, ExpectWorkTimeCompareDto param);
}
