package com.rcszh.worktime.mapper;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rcszh.worktime.domain.dto.expect_work_time.EveryMonthWorkTime;
import com.rcszh.worktime.domain.dto.expect_work_time.ExpectWorkTimeCompareDto;
import com.rcszh.worktime.domain.dto.expect_work_time.ProjectDiffRangeDto;
import com.rcszh.worktime.domain.expect_work.SysExpectProjectWorkTime;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface SysExpectProjectWorkTimeMapper extends BaseMapper<SysExpectProjectWorkTime> {
    // 条件查询所有的项目预估工时
    List<SysExpectProjectWorkTime> findAll(SysExpectProjectWorkTime params);

    // 关联用户预估工时去条件查询所有的项目预估工时
    List<SysExpectProjectWorkTime> findAllWithUserWorkTime(SysExpectProjectWorkTime params);

    // 查询每个月预估工时
    List<EveryMonthWorkTime> findEveryMonthWorkTime(DateTime start, DateTime end, ExpectWorkTimeCompareDto param);

    // 查询指定差异范围内的项目预估工时
    List<ProjectDiffRangeDto> findProjectDiffRange(DateTime start, DateTime end, ExpectWorkTimeCompareDto param);
}
