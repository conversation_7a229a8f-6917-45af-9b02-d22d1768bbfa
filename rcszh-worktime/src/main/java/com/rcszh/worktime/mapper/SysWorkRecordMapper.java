package com.rcszh.worktime.mapper;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rcszh.basic.domain.dto.UserReportAnalysisDTO;
import com.rcszh.basic.domain.dto.WorkRecordMonthStatDto;
import com.rcszh.worktime.domain.SysWorkRecord;
import com.rcszh.worktime.domain.dto.ProjectUserWorkTimeProportionDto;
import com.rcszh.worktime.domain.dto.expect_work_time.EveryMonthWorkTime;
import com.rcszh.worktime.domain.dto.expect_work_time.ExpectWorkTimeCompareDto;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;


@Mapper
public interface SysWorkRecordMapper extends BaseMapper<SysWorkRecord> {

    List<Long> findWorkRecordUserIds(WorkRecordMonthStatDto workRecordMonthStat);

    List<SysWorkRecord> monthStat(WorkRecordMonthStatDto workRecordMonthStat);

    List<SysWorkRecord> projectWorkTimeTotal(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<Map<String, Object>> queryUserSubmissionStatus(@Param("startDate") String startDate,@Param("endDate") String endDate);

    List<UserReportAnalysisDTO> userReportAnalysis(Map<String, Object> params);

    List<SysWorkRecord> findWorkRecordByDepartment(Map<String, Object> query);

    List<EveryMonthWorkTime> getEveryMonthWorkTime(@Param("start") DateTime start, @Param("end") DateTime end, @Param("param") ExpectWorkTimeCompareDto param);
    // 查询指定之间范围内的指定项目的所有工时
    Long findProjectTotalWorkTime(@Param("start") DateTime start, @Param("end") DateTime end, Long projectId);
    // 查询指定之间范围内的所有项目成员的工时
    List<ProjectUserWorkTimeProportionDto> findProjectUserTotalWorkTime(@Param("start") DateTime start, @Param("end") DateTime end, Long projectId);
}
