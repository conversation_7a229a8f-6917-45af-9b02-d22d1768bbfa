package com.rcszh.worktime.enums;

import lombok.Getter;

// 1-审批中；2-已通过；3-已驳回；4-已撤销；6-通过后撤销；7-已删除；10-已支付
@Getter
public enum PmsAllocationType {
    all("all", "全部项目"),
    part("part", "部分项目"),
    ;

    private final String code;

    private final String name;

    PmsAllocationType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code) {
        for (PmsAllocationType allocationType : PmsAllocationType.values()) {
            if (allocationType.getCode().equals(code)) {
                return allocationType.getName();
            }
        }
        return "";
    }

}
