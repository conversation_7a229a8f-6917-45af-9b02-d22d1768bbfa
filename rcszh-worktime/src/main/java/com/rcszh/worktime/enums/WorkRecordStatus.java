package com.rcszh.worktime.enums;

import lombok.Getter;

@Getter
public enum WorkRecordStatus {
    confirming("confirming", "待提交"),
    pending("pending", "待审核"),
    audited("audited", "已审核"),
    rejected("rejected", "已拒绝"),
    cancel("cancel", "已撤回"),
    ;

    private final String code;

    private final String name;

    WorkRecordStatus(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code) {
        for (WorkRecordStatus workRecordStatus : WorkRecordStatus.values()) {
            if (workRecordStatus.getCode().equals(code)) {
                return workRecordStatus.getName();
            }
        }
        return null;
    }

}
