package com.rcszh.worktime.enums;

import lombok.Getter;

@Getter
public enum WorkRecordType {
    entry("entry", "录入工时"),
    share("share", "分摊工时"),
    ;

    private final String code;

    private final String name;

    WorkRecordType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code) {
        for (WorkRecordType workRecordType : WorkRecordType.values()) {
            if (workRecordType.getCode().equals(code)) {
                return workRecordType.getName();
            }
        }
        return "";
    }

}
