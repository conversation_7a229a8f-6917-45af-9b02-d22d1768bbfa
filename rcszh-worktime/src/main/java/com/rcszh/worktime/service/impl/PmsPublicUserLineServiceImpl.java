package com.rcszh.worktime.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rcszh.base.common.exception.ServiceException;
import com.rcszh.worktime.domain.PmsPublicUser;
import com.rcszh.worktime.domain.PmsPublicUserLine;
import com.rcszh.worktime.domain.PmsPublicUserProject;
import com.rcszh.worktime.domain.dto.PmsPublicUserLineDto;
import com.rcszh.worktime.enums.PmsAllocationType;
import com.rcszh.worktime.mapper.PmsPublicUserLineMapper;
import com.rcszh.worktime.mapper.PmsPublicUserMapper;
import com.rcszh.worktime.service.IPmsPublicUserLineService;
import com.rcszh.worktime.service.IPmsPublicUserProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
public class PmsPublicUserLineServiceImpl extends ServiceImpl<PmsPublicUserLineMapper, PmsPublicUserLine> implements IPmsPublicUserLineService {

    @Autowired
    private PmsPublicUserMapper pmsPublicUserMapper;

    @Autowired
    private PmsPublicUserLineMapper pmsPublicUserLineMapper;

    @Autowired
    private IPmsPublicUserProjectService pmsPublicUserProjectService;

    private void checkTimeRange(PmsPublicUserLineDto pmsPublicUserLineDto) {
        LambdaQueryWrapper<PmsPublicUserLine> queryWrapper = new LambdaQueryWrapper<PmsPublicUserLine>()
                .eq(PmsPublicUserLine::getPublicUserId, pmsPublicUserLineDto.getPublicUserId())
                .ne(Objects.nonNull(pmsPublicUserLineDto.getId()), PmsPublicUserLine::getId, pmsPublicUserLineDto.getId())
                .le(PmsPublicUserLine::getStartTime, pmsPublicUserLineDto.getEndTime())
                .ge(PmsPublicUserLine::getEndTime, pmsPublicUserLineDto.getStartTime());
        Long count = pmsPublicUserLineMapper.selectCount(queryWrapper);
        if (count > 0) {
            throw new ServiceException("时间段重复");
        }
    }

    @Override
    public int addPmsPublicUserLine(PmsPublicUserLineDto pmsPublicUserLineDto) {
        PmsPublicUser pmsPublicUser = pmsPublicUserMapper.selectById(pmsPublicUserLineDto.getPublicUserId());
        if (pmsPublicUser == null) {
            throw new ServiceException("公共人员不存在");
        }

        this.checkTimeRange(pmsPublicUserLineDto);
        PmsPublicUserLine pmsPublicUserLine = new PmsPublicUserLine();
        pmsPublicUserLine.setPublicUserId(pmsPublicUserLineDto.getPublicUserId());
        pmsPublicUserLine.setUserId(pmsPublicUser.getUserId());
        pmsPublicUserLine.setStartTime(pmsPublicUserLineDto.getStartTime());
        pmsPublicUserLine.setEndTime(pmsPublicUserLineDto.getEndTime());
        pmsPublicUserLine.setAllocationType(pmsPublicUserLineDto.getAllocationType());
        pmsPublicUserLine.fillBaseFields();
        int flag = pmsPublicUserLineMapper.insert(pmsPublicUserLine);
        this.batchSavePmsPublicUserProject(pmsPublicUserLineDto, pmsPublicUserLine);
        return flag;
    }

    private void batchSavePmsPublicUserProject(PmsPublicUserLineDto pmsPublicUserLineDto, PmsPublicUserLine pmsPublicUserLine) {
        if (CollUtil.isNotEmpty(pmsPublicUserLineDto.getProjectIds()) && PmsAllocationType.part.getCode().equals(pmsPublicUserLineDto.getAllocationType())) {
            List<PmsPublicUserProject> publicUserProjects = pmsPublicUserLineDto.getProjectIds().stream().map(projectId -> {
                PmsPublicUserProject pmsPublicUserProject = new PmsPublicUserProject();
                pmsPublicUserProject.setPublicUserLineId(pmsPublicUserLine.getId());
                pmsPublicUserProject.setProjectId(projectId);
                pmsPublicUserProject.fillBaseFields();
                return pmsPublicUserProject;
            }).toList();
            pmsPublicUserProjectService.saveBatch(publicUserProjects);
        }
    }

    @Override
    public int updatePmsPublicUserLine(PmsPublicUserLineDto pmsPublicUserLineDto) {
        this.checkTimeRange(pmsPublicUserLineDto);
        PmsPublicUserLine pmsPublicUserLine = pmsPublicUserLineMapper.selectById(pmsPublicUserLineDto.getId());
        if (pmsPublicUserLine == null) {
            throw new ServiceException("数据不存在");
        }
        pmsPublicUserLine.setStartTime(pmsPublicUserLineDto.getStartTime());
        pmsPublicUserLine.setEndTime(pmsPublicUserLineDto.getEndTime());
        pmsPublicUserLine.setAllocationType(pmsPublicUserLineDto.getAllocationType());
        pmsPublicUserLine.fillBaseFields();
        int flag = pmsPublicUserLineMapper.updateById(pmsPublicUserLine);

        pmsPublicUserProjectService.remove(new LambdaQueryWrapper<PmsPublicUserProject>().eq(PmsPublicUserProject::getPublicUserLineId, pmsPublicUserLineDto.getId()));
        this.batchSavePmsPublicUserProject(pmsPublicUserLineDto, pmsPublicUserLine);

        return flag;
    }

    @Override
    public int removePmsPublicUserLine(List<Long> ids) {
        pmsPublicUserProjectService.remove(new LambdaQueryWrapper<PmsPublicUserProject>().in(PmsPublicUserProject::getPublicUserLineId, ids));
        return pmsPublicUserLineMapper.deleteBatchIds(ids);
    }

    @Override
    public List<PmsPublicUserLine> findEffectiveProjects(List<Long> pmsPublicUserIds, Date startTime, Date endTime) {
        return pmsPublicUserLineMapper.selectList(new LambdaQueryWrapper<PmsPublicUserLine>()
                .in(CollUtil.isNotEmpty(pmsPublicUserIds), PmsPublicUserLine::getPublicUserId, pmsPublicUserIds)
                .le(PmsPublicUserLine::getStartTime, DateUtil.beginOfDay(endTime))
                .ge(PmsPublicUserLine::getEndTime, DateUtil.beginOfDay(startTime))
        );
    }
}
