package com.rcszh.worktime.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.rcszh.activiti.domain.vo.WfFlowTaskVo;
import com.rcszh.activiti.domain.vo.WfTaskIdentityVo;
import com.rcszh.activiti.service.IWfRuntimeService;
import com.rcszh.attendance.domain.SysCheckinDay;
import com.rcszh.attendance.service.ISysCheckinDayService;
import com.rcszh.base.common.core.domain.entity.SysDept;
import com.rcszh.base.common.core.page.TableDataInfo;
import com.rcszh.base.common.utils.PageUtils;
import com.rcszh.basic.domain.dto.UserReportAnalysisDTO;
import com.rcszh.cache.BaseDataTranslateUtils;
import com.rcszh.cache.CacheUtil;
import com.rcszh.cache.SimpleObjCacheDto;
import com.rcszh.common.constant.Constants;
import com.rcszh.common.util.PageUtil;
import com.rcszh.common.util.WorkTimeUtil;
import com.rcszh.message.enums.MsgTypeEnum;
import com.rcszh.message.service.ISysMessageService;
import com.rcszh.project.domain.PmsProject;
import com.rcszh.project.domain.PmsProjectUser;
import com.rcszh.project.enums.PmsProjectMode;
import com.rcszh.project.service.IPmsProjectService;
import com.rcszh.project.service.IPmsProjectUserService;
import com.rcszh.util.SimpleObjCacheUtil;
import com.rcszh.worktime.domain.PmsPublicUser;
import com.rcszh.worktime.domain.PmsPublicUserLine;
import com.rcszh.worktime.domain.SysWorkRecord;
import com.rcszh.worktime.service.IPmsPublicUserService;
import com.rcszh.worktime.service.IPmsWorkRecordService;
import com.rcszh.worktime.service.ISysWorkRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class PmsWorkRecordServiceImpl implements IPmsWorkRecordService {

    @Autowired
    private ISysWorkRecordService iSysWorkRecordService;

    @Autowired
    private ISysCheckinDayService sysCheckinDayService;

    @Autowired
    private BaseDataTranslateUtils baseDataTranslateUtils;

    @Autowired
    private CacheUtil cacheUtil;

    @Autowired
    private IPmsProjectUserService iPmsProjectUserService;

    @Autowired
    private IPmsProjectService iPmsProjectService;

    @Autowired
    private IWfRuntimeService iWfRuntimeService;

    @Autowired
    private IPmsPublicUserService iPmsPublicUserService;

    @Autowired
    private ISysMessageService iSysMessageService;

    @Override
    public TableDataInfo queryReportWarning(Map<String, Object> params) {
        Date startDate = Convert.toDate(params.get("startDate"));
        Date endDate = Convert.toDate(params.get("endDate"));
        String deptIds = Convert.toStr(params.get("deptIds"));
        if (StrUtil.isNotBlank(deptIds)) {
            params.put("deptIds", CollUtil.toList(deptIds.split(",")));
        }

        List<DateTime> dateTimes = DateUtil.rangeToList(startDate, endDate, DateField.DAY_OF_MONTH);

        PageUtils.startPage();
        List<Long> checkinDaysToDept = sysCheckinDayService.findCheckinDaysToDept(params);
        if (CollUtil.isEmpty(checkinDaysToDept)) {
            return PageUtil.getDataTable(checkinDaysToDept);
        }
        PageUtils.clearPage();
        params.put("deptIdList", checkinDaysToDept);
        params.put("pageNum", null);
        params.put("pageSize", null);

        // 获取考勤数据和项目数据
        List<SysCheckinDay> checkinDayList = sysCheckinDayService.findCheckinDaysByDate(params);
        baseDataTranslateUtils.baseDataTranslate(checkinDayList);

        // 按部门分组
        Map<Long, List<SysCheckinDay>> deptCheckinMap = checkinDayList.stream()
                .filter(t -> Objects.nonNull(t.getDeptId()))
                .collect(Collectors.groupingBy(SysCheckinDay::getDeptId));

        // 获取工作记录
        List<SysWorkRecord> workRecordList = iSysWorkRecordService.findWorkRecordByDate(params);
        Map<String, List<SysWorkRecord>> workRecordMap = workRecordList.stream()
                .collect(Collectors.groupingBy(t -> DateUtil.formatDate(t.getWorkTime())));

        // 获取用户项目数据
        List<Long> userIds = checkinDayList.stream().map(SysCheckinDay::getUserId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<PmsProjectUser> projectUsers = iPmsProjectUserService.getProjectUserByUserIds(userIds, Constants.Y);
        Map<Long, List<PmsProjectUser>> projectUserMap = projectUsers.stream().collect(Collectors.groupingBy(PmsProjectUser::getUserId));

        // 获取项目数据
        List<Long> projectIds = projectUsers.stream().map(PmsProjectUser::getProjectId).distinct().collect(Collectors.toList());
        List<PmsProject> projectList = iPmsProjectService.findByIds(projectIds);
        Map<Long, PmsProject> projectMap = projectList.stream().collect(Collectors.toMap(PmsProject::getId, t -> t));

        //获取公共人员
        List<PmsPublicUserLine> publicUserLines = iPmsPublicUserService.findEffectiveProjects(startDate, endDate);

        Map<Long, List<PmsPublicUserLine>> publicUserMap = publicUserLines.stream().collect(Collectors.groupingBy(PmsPublicUserLine::getUserId));

        List<PmsProject> allProjectList = iPmsProjectService.list();

        List<Map<String, Object>> mapList = new ArrayList<>(deptCheckinMap.size());
        // 遍历部门数据
        deptCheckinMap.forEach((deptId, checkinDays) -> {
            SimpleObjCacheDto deptCache = cacheUtil.findDeptById(deptId);
            if (!SimpleObjCacheUtil.isExist(deptCache)) {
                return;
            }
            SysDept dept = deptCache.getObj(SysDept.class);
            List<String> ancestors = StrUtil.split(dept.getAncestors(),",");
            List<SimpleObjCacheDto> deptCacheList = cacheUtil.findDeptByIds(ancestors);
            Map<String, Object> deptMap = new HashMap<>();
            deptMap.put("deptId", deptId);
            List<String> deptNameList = deptCacheList.stream().map(SimpleObjCacheDto::getName).collect(Collectors.toList());
            deptNameList.add(deptCache.getName());
            deptMap.put("deptName", CollUtil.join(deptNameList, " / "));

            List<Map<String, Object>> detailList = new ArrayList<>();

            // 按日期分组
            Map<String, List<SysCheckinDay>> checkinDayMap = checkinDays.stream()
                    .collect(Collectors.groupingBy(t -> DateUtil.formatDate(t.getStatDay()),TreeMap::new, // 使用 TreeMap 自动按键排序
                            Collectors.toList()));

            checkinDayMap.forEach((statDay, dayCheckins) -> {
                int attendanceCount = 0;
                int unreportedCount = 0;

                // 处理每个考勤记录
                for (SysCheckinDay checkinDay : dayCheckins) {
                    if (checkinDay.getRegularWorkSec() == 0){
                        continue;
                    }
                    //是否是公共人员
                    List<PmsPublicUserLine> pmsPublicUserLines = publicUserMap.get(checkinDay.getUserId());
                    if (CollUtil.isNotEmpty(pmsPublicUserLines)) {
                        PmsPublicUserLine pmsPublicUserLine = pmsPublicUserLines.stream()
                                .filter(d -> d.getStartTime().getTime() <= checkinDay.getStatDay().getTime() && d.getEndTime().getTime() >= checkinDay.getStatDay().getTime())
                                .findFirst().orElse(null);
                        if (Objects.nonNull(pmsPublicUserLine)) {
                            continue;
                        }
                    }

                    boolean validFlag = false;
                    //验证是否对应的负责人 可以填报的
                    validFlag = allProjectList.stream().anyMatch(t -> (Objects.equals(t.getProjectManagerId(),checkinDay.getUserId())
                            || Objects.equals(t.getProjectSupervisorId(),checkinDay.getUserId()) || Objects.equals(t.getProjectAssistantId(),checkinDay.getUserId())
                            || Objects.equals(t.getProjectAssistantId2(),checkinDay.getUserId()) || Objects.equals(t.getProjectAssistantId3(),checkinDay.getUserId())
                            || Objects.equals(t.getProjectAssistantId4(),checkinDay.getUserId()) || Objects.equals(t.getProjectAssistantId5(),checkinDay.getUserId()))
                            && iPmsProjectService.matchProject(t, Convert.toDate(DateUtil.format(checkinDay.getStatDay(),DatePattern.NORM_DATE_PATTERN) + " 00:00:00"), Convert.toDate(DateUtil.format(checkinDay.getStatDay(),DatePattern.NORM_DATE_PATTERN) + " 23:59:59")));

                    if (!validFlag){
                        //判断公共项目
                        validFlag = allProjectList.stream().anyMatch(t -> StrUtil.equals(t.getProjectMode(), PmsProjectMode.publicProject.getCode()) && iPmsProjectService.matchProject(t, Convert.toDate(DateUtil.format(checkinDay.getStatDay(),DatePattern.NORM_DATE_PATTERN) + " 00:00:00"), Convert.toDate(DateUtil.format(checkinDay.getStatDay(),DatePattern.NORM_DATE_PATTERN) + " 23:59:59")));
                    }

                    if (!validFlag){
                        List<PmsProjectUser> pmsProjectUsers = projectUserMap.get(checkinDay.getUserId());
                        if (CollUtil.isEmpty(pmsProjectUsers)) {
                            continue; // 没有项目用户，跳过
                        }

                        // 过滤符合条件的项目用户
                        validFlag = pmsProjectUsers.stream()
                                .anyMatch(projectUser -> {
                                    PmsProject project = projectMap.get(projectUser.getProjectId());
                                    return project != null && iPmsProjectService.matchProject(project, Convert.toDate(DateUtil.format(checkinDay.getStatDay(),DatePattern.NORM_DATE_PATTERN) + " 00:00:00"), Convert.toDate(DateUtil.format(checkinDay.getStatDay(),DatePattern.NORM_DATE_PATTERN) + " 23:59:59"));
                                });
                    }



                    if (!validFlag) {
                        continue; // 没有匹配的项目用户，跳过
                    }

                    // 获取对应日期的工作记录
                    List<SysWorkRecord> records = workRecordMap.get(statDay);
                    attendanceCount++; // 无论如何，出勤次数增加

                    // 判断是否存在该用户的工作记录
                    boolean isPresent = CollUtil.isNotEmpty(records) &&
                            records.stream().anyMatch(record -> record.getUserId().equals(checkinDay.getUserId()));

                    if (!isPresent) {
                        unreportedCount++; // 如果没有记录，增加未报到次数
                    }
                }

                // 计算出勤率
                BigDecimal attendanceRate = attendanceCount == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(attendanceCount - unreportedCount)
                        .divide(BigDecimal.valueOf(attendanceCount), 4, RoundingMode.HALF_UP)
                        .multiply(BigDecimal.valueOf(100))
                        .setScale(2, RoundingMode.HALF_UP);

                Map<String, Object> detailMap = new HashMap<>();
                detailMap.put("day", statDay);
                detailMap.put("attendanceCount", attendanceCount);
                detailMap.put("unreportedCount", unreportedCount);
                detailMap.put("rate", attendanceRate.setScale(2,RoundingMode.HALF_UP) + "%");
                detailList.add(detailMap);
            });

            // 填充缺失的日期
            for (DateTime dateTime : dateTimes) {
                if (detailList.stream().noneMatch(map -> DateUtil.compare(Convert.toDate(dateTime),Convert.toDate(map.get("day")),DatePattern.NORM_DATE_PATTERN) == 0)) {
                    Map<String, Object> detailMap = new HashMap<>();
                    detailMap.put("day", DateUtil.formatDate(dateTime));
                    detailMap.put("attendanceCount", 0);
                    detailMap.put("unreportedCount", 0);
                    detailMap.put("rate", "0.00%");
                    detailList.add(detailMap);
                }
            }

            detailList.sort(Comparator.comparing(a -> Convert.toDate(a.get("day"))));
            deptMap.put("detailList", detailList);
            mapList.add(deptMap);
        });

        return PageUtil.getDataTable(checkinDaysToDept, mapList);
    }


    @Override
    public Map<String, List<Map<String, Object>>> queryReportWarningExport(Map<String, Object> params) {
        Date startDate = Convert.toDate(params.get("startDate"));
        Date endDate = Convert.toDate(params.get("endDate"));
        params.put("pageNum", null);
        params.put("pageSize", null);
        String deptIds = Convert.toStr(params.get("deptIds"));
        if (StrUtil.isNotBlank(deptIds)) {
            params.put("deptIds", CollUtil.toList(deptIds.split(",")));
        }

        List<DateTime> dateTimes = DateUtil.rangeToList(startDate, endDate, DateField.DAY_OF_MONTH);

        List<Long> checkinDaysToDept = sysCheckinDayService.findCheckinDaysToDept(params);
        if (CollUtil.isEmpty(checkinDaysToDept)) {
            throw new RuntimeException("没有可导出的数据");
        }
        PageUtils.clearPage();
        params.put("deptIdList", checkinDaysToDept);
        // 获取考勤数据和项目数据
        List<SysCheckinDay> checkinDayList = sysCheckinDayService.findCheckinDaysByDate(params);
        baseDataTranslateUtils.baseDataTranslate(checkinDayList);

        // 按部门分组
        Map<Long, List<SysCheckinDay>> deptCheckinMap = checkinDayList.stream()
                .filter(t -> Objects.nonNull(t.getDeptId()))
                .collect(Collectors.groupingBy(SysCheckinDay::getDeptId));

        // 获取工作记录
        List<SysWorkRecord> workRecordList = iSysWorkRecordService.findWorkRecordByDate(params);
        Map<String, List<SysWorkRecord>> workRecordMap = workRecordList.stream()
                .collect(Collectors.groupingBy(t -> DateUtil.formatDate(t.getWorkTime())));

        // 获取用户项目数据
        List<Long> userIds = checkinDayList.stream().map(SysCheckinDay::getUserId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<PmsProjectUser> projectUsers = iPmsProjectUserService.getProjectUserByUserIds(userIds, Constants.Y);
        Map<Long, List<PmsProjectUser>> projectUserMap = projectUsers.stream().collect(Collectors.groupingBy(PmsProjectUser::getUserId));

        // 获取项目数据
        List<Long> projectIds = projectUsers.stream().map(PmsProjectUser::getProjectId).distinct().collect(Collectors.toList());
        List<PmsProject> projectList = iPmsProjectService.findByIds(projectIds);
        Map<Long, PmsProject> projectMap = projectList.stream().collect(Collectors.toMap(PmsProject::getId, t -> t));

        //获取公共人员
        List<PmsPublicUserLine> publicUserLines = iPmsPublicUserService.findEffectiveProjects(startDate, endDate);
        Map<Long, List<PmsPublicUserLine>> publicUserMap = publicUserLines.stream().collect(Collectors.groupingBy(PmsPublicUserLine::getUserId));

        List<PmsProject> allProjectList = iPmsProjectService.allProject();

        List<Map<String, Object>> mapList = new ArrayList<>(deptCheckinMap.size());
        List<Map<String, Object>> detailList = new ArrayList<>();
        // 遍历部门数据
        deptCheckinMap.forEach((deptId, checkinDays) -> {
            SimpleObjCacheDto deptCache = cacheUtil.findDeptById(deptId);
            if (!SimpleObjCacheUtil.isExist(deptCache)) {
                return;
            }
            SysDept dept = deptCache.getObj(SysDept.class);
            List<String> ancestors = StrUtil.split(dept.getAncestors(), ",");
            List<SimpleObjCacheDto> deptCacheList = cacheUtil.findDeptByIds(ancestors);
            Map<String, Object> deptMap = new HashMap<>();
            deptMap.put("deptId", deptId);
            List<String> deptNameList = deptCacheList.stream().map(SimpleObjCacheDto::getName).collect(Collectors.toList());
            deptNameList.add(deptCache.getName());
            String deptName = CollUtil.join(deptNameList, " / ");
            deptMap.put("deptName", deptName);

            List<Map<String, Object>> dataList = new ArrayList<>();

            // 按日期分组
            Map<String, List<SysCheckinDay>> checkinDayMap = checkinDays.stream()
                    .collect(Collectors.groupingBy(t -> DateUtil.formatDate(t.getStatDay()), TreeMap::new, // 使用 TreeMap 自动按键排序
                            Collectors.toList()));

            checkinDayMap.forEach((statDay, dayCheckins) -> {
                int attendanceCount = 0;
                int unreportedCount = 0;

                // 处理每个考勤记录
                for (SysCheckinDay checkinDay : dayCheckins) {
                    if (checkinDay.getRegularWorkSec() == 0) {
                        continue;
                    }
                    //是否是公共人员
                    List<PmsPublicUserLine> pmsPublicUserLines = publicUserMap.get(checkinDay.getUserId());
                    if (CollUtil.isNotEmpty(pmsPublicUserLines)) {
                        PmsPublicUserLine pmsPublicUserLine = pmsPublicUserLines.stream()
                                .filter(d -> d.getStartTime().getTime() <= checkinDay.getStatDay().getTime() && d.getEndTime().getTime() >= checkinDay.getStatDay().getTime())
                                .findFirst().orElse(null);
                        if (Objects.nonNull(pmsPublicUserLine)) {
                            continue;
                        }
                    }

                    boolean validFlag = false;
                    //验证是否对应的负责人 可以填报的
                    validFlag = allProjectList.stream().anyMatch(t -> (Objects.equals(t.getProjectManagerId(), checkinDay.getUserId())
                            || Objects.equals(t.getProjectSupervisorId(), checkinDay.getUserId()) || Objects.equals(t.getProjectAssistantId(), checkinDay.getUserId())
                            || Objects.equals(t.getProjectAssistantId2(), checkinDay.getUserId()) || Objects.equals(t.getProjectAssistantId3(), checkinDay.getUserId())
                            || Objects.equals(t.getProjectAssistantId4(), checkinDay.getUserId()) || Objects.equals(t.getProjectAssistantId5(), checkinDay.getUserId()))
                            && iPmsProjectService.matchProject(t, Convert.toDate(DateUtil.format(checkinDay.getStatDay(), DatePattern.NORM_DATE_PATTERN) + " 00:00:00"), Convert.toDate(DateUtil.format(checkinDay.getStatDay(), DatePattern.NORM_DATE_PATTERN) + " 23:59:59")));

                    if (!validFlag) {
                        //判断公共项目
                        validFlag = allProjectList.stream().anyMatch(t -> StrUtil.equals(t.getProjectMode(), PmsProjectMode.publicProject.getCode()) && iPmsProjectService.matchProject(t, Convert.toDate(DateUtil.format(checkinDay.getStatDay(), DatePattern.NORM_DATE_PATTERN) + " 00:00:00"), Convert.toDate(DateUtil.format(checkinDay.getStatDay(), DatePattern.NORM_DATE_PATTERN) + " 23:59:59")));
                    }

                    if (!validFlag) {
                        List<PmsProjectUser> pmsProjectUsers = projectUserMap.get(checkinDay.getUserId());
                        if (CollUtil.isEmpty(pmsProjectUsers)) {
                            continue; // 没有项目用户，跳过
                        }

                        // 过滤符合条件的项目用户
                        validFlag = pmsProjectUsers.stream()
                                .anyMatch(projectUser -> {
                                    PmsProject project = projectMap.get(projectUser.getProjectId());
                                    return project != null && iPmsProjectService.matchProject(project, Convert.toDate(DateUtil.format(checkinDay.getStatDay(), DatePattern.NORM_DATE_PATTERN) + " 00:00:00"), Convert.toDate(DateUtil.format(checkinDay.getStatDay(), DatePattern.NORM_DATE_PATTERN) + " 23:59:59"));
                                });
                    }


                    if (!validFlag) {
                        continue; // 没有匹配的项目用户，跳过
                    }

                    // 获取对应日期的工作记录
                    List<SysWorkRecord> records = workRecordMap.get(statDay);
                    attendanceCount++; // 无论如何，出勤次数增加

                    // 判断是否存在该用户的工作记录
                    boolean isPresent = CollUtil.isNotEmpty(records) &&
                            records.stream().anyMatch(record -> record.getUserId().equals(checkinDay.getUserId()));

                    SimpleObjCacheDto userCache = cacheUtil.findUserById(checkinDay.getUserId());
                    if (!SimpleObjCacheUtil.isExist(userCache)) {
                        continue;
                    }
                    Map<String, Object> detailMap = new HashMap<>();
                    detailMap.put("deptName", deptName);
                    detailMap.put("day", statDay);
                    detailMap.put("userName", userCache.getCode());
                    detailMap.put("nickName", userCache.getName());
                    detailMap.put("regularWorkSec", WorkTimeUtil.transformMinute(checkinDay.getRegularWorkSec()));
                    if (!isPresent) {
                        unreportedCount++; // 如果没有记录，增加未报到次数
                        detailMap.put("status", "未填报");
                    } else {
                        detailMap.put("status", "已填报");
                    }
                    detailList.add(detailMap);
                }

                // 计算出勤率
                BigDecimal attendanceRate = attendanceCount == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(attendanceCount - unreportedCount)
                        .divide(BigDecimal.valueOf(attendanceCount), 4, RoundingMode.HALF_UP)
                        .multiply(BigDecimal.valueOf(100))
                        .setScale(2, RoundingMode.HALF_UP);

                Map<String, Object> detailMap = new HashMap<>();
                detailMap.put("day", statDay);
                detailMap.put("attendanceCount", attendanceCount);
                detailMap.put("unreportedCount", unreportedCount);
                detailMap.put("rate", attendanceRate.setScale(2, RoundingMode.HALF_UP) + "%");
                dataList.add(detailMap);
            });

            // 填充缺失的日期
            for (DateTime dateTime : dateTimes) {
                if (dataList.stream().noneMatch(map -> DateUtil.compare(Convert.toDate(dateTime), Convert.toDate(map.get("day")), DatePattern.NORM_DATE_PATTERN) == 0)) {
                    Map<String, Object> detailMap = new HashMap<>();
                    detailMap.put("day", DateUtil.formatDate(dateTime));
                    detailMap.put("attendanceCount", 0);
                    detailMap.put("unreportedCount", 0);
                    detailMap.put("rate", "0.00%");
                    dataList.add(detailMap);
                }
            }

            dataList.sort(Comparator.comparing(a -> Convert.toDate(a.get("day"))));
            deptMap.put("detailList", dataList);
            mapList.add(deptMap);
        });

        return Map.of("dataList", mapList, "detailList", detailList);
    }


    @Override
    public List<Map<String, Object>> getAttendanceList(String date,Long deptId) {
        List<SysCheckinDay> checkinDayList = sysCheckinDayService.findCheckinDaysByDate(Map.of("startDate", Convert.toDate(date), "endDate", Convert.toDate(date)));
        Date workDate = Convert.toDate(date);
        baseDataTranslateUtils.baseDataTranslate(checkinDayList);
        checkinDayList = checkinDayList.stream().filter(t -> Objects.equals(t.getDeptId(),deptId)).collect(Collectors.toList());

        // 获取用户项目数据
        List<Long> userIds = checkinDayList.stream().map(SysCheckinDay::getUserId).distinct().collect(Collectors.toList());
        List<PmsProjectUser> projectUsers = iPmsProjectUserService.getProjectUserByUserIds(userIds, Constants.Y);
        Map<Long, List<PmsProjectUser>> projectUserMap = projectUsers.stream().collect(Collectors.groupingBy(PmsProjectUser::getUserId));

        // 获取项目数据
        List<Long> projectIds = projectUsers.stream().map(PmsProjectUser::getProjectId).distinct().collect(Collectors.toList());
        List<PmsProject> projectList = iPmsProjectService.findByIds(projectIds);
        Map<Long, PmsProject> projectMap = projectList.stream().collect(Collectors.toMap(PmsProject::getId, t -> t));

        //获取公共人员
        List<PmsPublicUserLine> publicUserLines = iPmsPublicUserService.findEffectiveProjects(workDate, workDate);
        Set<Long> publicUserSet = publicUserLines.stream().map(PmsPublicUserLine::getUserId).collect(Collectors.toSet());

        List<PmsProject> allProjectList = iPmsProjectService.list();

        List<Map<String,Object>> mapList = new ArrayList<>();
        for (SysCheckinDay checkinDay:checkinDayList) {
            if (checkinDay.getRegularWorkSec() == 0){
                continue;
            }

            //是否是公共人员
            if (publicUserSet.contains(checkinDay.getUserId())){
                continue;
            }

            boolean validFlag = false;
            //验证是否对应的负责人 可以填报的
            validFlag = allProjectList.stream().anyMatch(t -> (Objects.equals(t.getProjectManagerId(),checkinDay.getUserId())
                    || Objects.equals(t.getProjectSupervisorId(),checkinDay.getUserId()) || Objects.equals(t.getProjectAssistantId(),checkinDay.getUserId())
                    || Objects.equals(t.getProjectAssistantId2(),checkinDay.getUserId()) || Objects.equals(t.getProjectAssistantId3(),checkinDay.getUserId())
                    || Objects.equals(t.getProjectAssistantId4(),checkinDay.getUserId()) || Objects.equals(t.getProjectAssistantId5(),checkinDay.getUserId()))
                    && iPmsProjectService.matchProject(t, Convert.toDate(DateUtil.format(checkinDay.getStatDay(),DatePattern.NORM_DATE_PATTERN) + " 00:00:00"), Convert.toDate(DateUtil.format(checkinDay.getStatDay(),DatePattern.NORM_DATE_PATTERN) + " 23:59:59")));

            if (!validFlag){
                //判断公共项目
                validFlag = allProjectList.stream().anyMatch(t -> StrUtil.equals(t.getProjectMode(), PmsProjectMode.publicProject.getCode()) && iPmsProjectService.matchProject(t, Convert.toDate(DateUtil.format(checkinDay.getStatDay(),DatePattern.NORM_DATE_PATTERN) + " 00:00:00"), Convert.toDate(DateUtil.format(checkinDay.getStatDay(),DatePattern.NORM_DATE_PATTERN) + " 23:59:59")));
            }

            if (!validFlag){
                List<PmsProjectUser> pmsProjectUsers = projectUserMap.get(checkinDay.getUserId());
                if (CollUtil.isEmpty(pmsProjectUsers)) {
                    continue; // 没有项目用户，跳过
                }

                // 过滤符合条件的项目用户
                validFlag = pmsProjectUsers.stream()
                        .anyMatch(projectUser -> {
                            PmsProject project = projectMap.get(projectUser.getProjectId());
                            return project != null && iPmsProjectService.matchProject(project, Convert.toDate(DateUtil.format(checkinDay.getStatDay(),DatePattern.NORM_DATE_PATTERN) + " 00:00:00"), Convert.toDate(DateUtil.format(checkinDay.getStatDay(),DatePattern.NORM_DATE_PATTERN) + " 23:59:59"));
                        });
            }

            if (!validFlag) {
                continue; // 没有匹配的项目用户，跳过
            }

            Map<String,Object> map = new HashMap<>();
            map.put("userName",checkinDay.getUserName());
            map.put("nickName",checkinDay.getNickName());
            map.put("deptName",checkinDay.getDeptName());
            map.put("regularWorkSec", WorkTimeUtil.transformMinute(checkinDay.getRegularWorkSec()));
            mapList.add(map);
        }
        return mapList;
    }

    @Override
    public List<Map<String, Object>> getUnreportedPersons(String date,Long deptId) {
        List<SysCheckinDay> checkinDayList = sysCheckinDayService.findCheckinDaysByDate(Map.of("startDate", Convert.toDate(date), "endDate", Convert.toDate(date)));
        baseDataTranslateUtils.baseDataTranslate(checkinDayList);

        checkinDayList = checkinDayList.stream().filter(t -> Objects.equals(t.getDeptId(),deptId)).collect(Collectors.toList());


        // 获取用户项目数据
        List<Long> userIds = checkinDayList.stream().map(SysCheckinDay::getUserId).distinct().collect(Collectors.toList());
        List<PmsProjectUser> projectUsers = iPmsProjectUserService.getProjectUserByUserIds(userIds, Constants.Y);
        Map<Long, List<PmsProjectUser>> projectUserMap = projectUsers.stream().collect(Collectors.groupingBy(PmsProjectUser::getUserId));

        // 获取项目数据
        List<Long> projectIds = projectUsers.stream().map(PmsProjectUser::getProjectId).distinct().collect(Collectors.toList());
        List<PmsProject> projectList = iPmsProjectService.findByIds(projectIds);
        Map<Long, PmsProject> projectMap = projectList.stream().collect(Collectors.toMap(PmsProject::getId, t -> t));

        //获取公共人员
        Date workDate = Convert.toDate(date);
        List<PmsPublicUserLine> publicUserLines = iPmsPublicUserService.findEffectiveProjects(workDate, workDate);
        Set<Long> publicUserSet = publicUserLines.stream().map(PmsPublicUserLine::getUserId).collect(Collectors.toSet());

        List<PmsProject> allProjectList = iPmsProjectService.list();

        List<SysWorkRecord> workRecordList = iSysWorkRecordService.findWorkRecordByDate(Map.of("startDate",date,"endDate",date));
        Map<Long, List<SysWorkRecord>> recordMap = workRecordList.stream().collect(Collectors.groupingBy(SysWorkRecord::getUserId));
        List<Map<String,Object>> mapList = new ArrayList<>();
        for (SysCheckinDay checkinDay:checkinDayList) {
            if (checkinDay.getRegularWorkSec() == 0){
                continue;
            }
            //是否是公共人员
            if (publicUserSet.contains(checkinDay.getUserId())){
                continue;
            }

            boolean validFlag = false;
            //验证是否对应的负责人 可以填报的
            validFlag = allProjectList.stream().anyMatch(t -> (Objects.equals(t.getProjectManagerId(),checkinDay.getUserId())
                    || Objects.equals(t.getProjectSupervisorId(),checkinDay.getUserId()) || Objects.equals(t.getProjectAssistantId(),checkinDay.getUserId())
                    || Objects.equals(t.getProjectAssistantId2(),checkinDay.getUserId()) || Objects.equals(t.getProjectAssistantId3(),checkinDay.getUserId())
                    || Objects.equals(t.getProjectAssistantId4(),checkinDay.getUserId()) || Objects.equals(t.getProjectAssistantId5(),checkinDay.getUserId()))
                    && iPmsProjectService.matchProject(t, Convert.toDate(DateUtil.format(checkinDay.getStatDay(),DatePattern.NORM_DATE_PATTERN) + " 00:00:00"), Convert.toDate(DateUtil.format(checkinDay.getStatDay(),DatePattern.NORM_DATE_PATTERN) + " 23:59:59")));

            if (!validFlag){
                //判断公共项目
                validFlag = allProjectList.stream().anyMatch(t -> StrUtil.equals(t.getProjectMode(), PmsProjectMode.publicProject.getCode()) && iPmsProjectService.matchProject(t, Convert.toDate(DateUtil.format(checkinDay.getStatDay(),DatePattern.NORM_DATE_PATTERN) + " 00:00:00"), Convert.toDate(DateUtil.format(checkinDay.getStatDay(),DatePattern.NORM_DATE_PATTERN) + " 23:59:59")));
            }

            if (!validFlag){
                List<PmsProjectUser> pmsProjectUsers = projectUserMap.get(checkinDay.getUserId());
                if (CollUtil.isEmpty(pmsProjectUsers)) {
                    continue; // 没有项目用户，跳过
                }

                // 过滤符合条件的项目用户
                validFlag = pmsProjectUsers.stream()
                        .anyMatch(projectUser -> {
                            PmsProject project = projectMap.get(projectUser.getProjectId());
                            return project != null && iPmsProjectService.matchProject(project, Convert.toDate(DateUtil.format(checkinDay.getStatDay(),DatePattern.NORM_DATE_PATTERN) + " 00:00:00"), Convert.toDate(DateUtil.format(checkinDay.getStatDay(),DatePattern.NORM_DATE_PATTERN) + " 23:59:59"));
                        });
            }

            if (!validFlag) {
                continue; // 没有匹配的项目用户，跳过
            }
            //判断是否有填报记录
            List<SysWorkRecord> records = recordMap.get(checkinDay.getUserId());
            if (CollUtil.isNotEmpty(records)){
                continue;
            }
            Map<String,Object> map = new HashMap<>();
            map.put("userName",checkinDay.getUserName());
            map.put("nickName",checkinDay.getNickName());
            map.put("deptName",checkinDay.getDeptName());
            mapList.add(map);
        }
        return mapList;
    }

    @Override
    public List<UserReportAnalysisDTO> userReportAnalysis(Map<String, Object> params) {
        if (StrUtil.equals(Convert.toStr(params.get("filterPublicUserFlag")), "Y")) {
            List<PmsPublicUser> publicUsers = iPmsPublicUserService.list();
            if (CollUtil.isNotEmpty(publicUsers)) {
                List<Long> userIds = publicUsers.stream().map(PmsPublicUser::getUserId).collect(Collectors.toList());
                params.put("filterPublicUserIds", userIds);
            }
        }
        if (Convert.toBool(params.get("pageFlag"))) {
            PageUtils.startPage();
        }
        List<UserReportAnalysisDTO> userReportAnalysisDTOS = iSysWorkRecordService.userReportAnalysis(params);
        List<String> approveIds = userReportAnalysisDTOS.stream().map(UserReportAnalysisDTO::getApproveId).filter(StrUtil::isNotEmpty).collect(Collectors.toList());
        List<WfFlowTaskVo> taskVoList = iWfRuntimeService.findRunningByBusinessKeys(approveIds);
        userReportAnalysisDTOS.forEach(t -> {
            if (StrUtil.isNotEmpty(t.getApproveId())) {
                Optional<String> first = taskVoList.stream().filter(k -> StrUtil.equals(k.getBusinessKey(), t.getApproveId()))
                        .map(k -> {
                            List<String> userNameList = k.getIdentitys().stream().map(WfTaskIdentityVo::getUserName).collect(Collectors.toList());
                            return StrUtil.join(",", userNameList);
                        }).findFirst();
                first.ifPresent(t::setApproveName);
            }
        });
        return userReportAnalysisDTOS;
    }

    @Override
    public List<UserReportAnalysisDTO> newUserReportAnalysis(Map<String, Object> params) {
        List<UserReportAnalysisDTO> userReportAnalysisDTOS = iSysWorkRecordService.userReportAnalysis(params);
        List<String> approveIds = userReportAnalysisDTOS.stream().map(UserReportAnalysisDTO::getApproveId).filter(StrUtil::isNotEmpty).collect(Collectors.toList());
        List<WfFlowTaskVo> taskVoList = iWfRuntimeService.findRunningByBusinessKeys(approveIds);
        userReportAnalysisDTOS.forEach(t -> {
            if (StrUtil.isNotEmpty(t.getApproveId())) {
                Optional<String> first = taskVoList.stream().filter(k -> StrUtil.equals(k.getBusinessKey(), t.getApproveId()))
                        .map(k -> {
                            List<String> userNameList = k.getIdentitys().stream().map(WfTaskIdentityVo::getUserName).collect(Collectors.toList());
                            return StrUtil.join(",", userNameList);
                        }).findFirst();
                first.ifPresent(t::setApproveName);
            }
            if ("hour".equals(params.get("unit"))) {
                t.setWorkDuration(Convert.toStr(WorkTimeUtil.transformHour(Convert.toInt(t.getWorkDuration()))));
            }
        });
        return userReportAnalysisDTOS;
    }

    @Override
    public void sendUnreportedUserReminder(Map<String, Object> params) {
        Date startDate = Convert.toDate(params.get("startDate"));
        Date endDate = Convert.toDate(params.get("endDate"));

        // 获取考勤数据和项目数据
        List<SysCheckinDay> checkinDayList = sysCheckinDayService.findCheckinDaysByDate(params);
        Map<Long, List<SysCheckinDay>> checkinDayMap = checkinDayList.stream().collect(Collectors.groupingBy(SysCheckinDay::getUserId));

        // 获取工作记录
        List<SysWorkRecord> workRecordList = iSysWorkRecordService.findWorkRecordByDate(params);

        // 获取用户项目数据
        List<Long> userIds = checkinDayList.stream().map(SysCheckinDay::getUserId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<PmsProjectUser> projectUsers = iPmsProjectUserService.getProjectUserByUserIds(userIds, Constants.Y);
        Map<Long, List<PmsProjectUser>> projectUserMap = projectUsers.stream().collect(Collectors.groupingBy(PmsProjectUser::getUserId));

        // 获取项目数据
        List<Long> projectIds = projectUsers.stream().map(PmsProjectUser::getProjectId).distinct().collect(Collectors.toList());
        List<PmsProject> projectList = iPmsProjectService.findByIds(projectIds);
        Map<Long, PmsProject> projectMap = projectList.stream().collect(Collectors.toMap(PmsProject::getId, t -> t));

        //获取公共人员
        List<PmsPublicUserLine> publicUserLines = iPmsPublicUserService.findEffectiveProjects(startDate, endDate);
        Map<Long, List<PmsPublicUserLine>> publicUserMap = publicUserLines.stream().collect(Collectors.groupingBy(PmsPublicUserLine::getUserId));
        List<PmsProject> allProjectList = iPmsProjectService.list();

        List<SysCheckinDay> dataList = new ArrayList<>();

        for (Long userId : userIds) {
            List<SysCheckinDay> checkinDays = checkinDayMap.get(userId);
            if (CollUtil.isEmpty(checkinDays)) {
                continue;
            }
            for (SysCheckinDay checkinDay : checkinDays) {
                if (checkinDay.getRegularWorkSec() == 0) {
                    continue;
                }
                //是否是公共人员
                List<PmsPublicUserLine> pmsPublicUserLines = publicUserMap.get(userId);
                if (CollUtil.isNotEmpty(pmsPublicUserLines)) {
                    PmsPublicUserLine pmsPublicUserLine = pmsPublicUserLines.stream()
                            .filter(d -> d.getStartTime().getTime() <= checkinDay.getStatDay().getTime() && d.getEndTime().getTime() >= checkinDay.getStatDay().getTime())
                            .findFirst().orElse(null);
                    if (Objects.nonNull(pmsPublicUserLine)) {
                        continue;
                    }
                }

                boolean validFlag = false;
                //验证是否对应的负责人 可以填报的
                validFlag = allProjectList.stream().anyMatch(t -> (Objects.equals(t.getProjectManagerId(), checkinDay.getUserId())
                        || Objects.equals(t.getProjectSupervisorId(), checkinDay.getUserId()) || Objects.equals(t.getProjectAssistantId(), checkinDay.getUserId())
                        || Objects.equals(t.getProjectAssistantId2(), checkinDay.getUserId()) || Objects.equals(t.getProjectAssistantId3(), checkinDay.getUserId())
                        || Objects.equals(t.getProjectAssistantId4(), checkinDay.getUserId()) || Objects.equals(t.getProjectAssistantId5(), checkinDay.getUserId()))
                        && iPmsProjectService.matchProject(t, Convert.toDate(DateUtil.format(checkinDay.getStatDay(), DatePattern.NORM_DATE_PATTERN) + " 00:00:00"), Convert.toDate(DateUtil.format(checkinDay.getStatDay(), DatePattern.NORM_DATE_PATTERN) + " 23:59:59")));

                if (!validFlag) {
                    //判断公共项目
                    validFlag = allProjectList.stream().anyMatch(t -> StrUtil.equals(t.getProjectMode(), PmsProjectMode.publicProject.getCode()) && iPmsProjectService.matchProject(t, Convert.toDate(DateUtil.format(checkinDay.getStatDay(), DatePattern.NORM_DATE_PATTERN) + " 00:00:00"), Convert.toDate(DateUtil.format(checkinDay.getStatDay(), DatePattern.NORM_DATE_PATTERN) + " 23:59:59")));
                }

                if (!validFlag) {
                    List<PmsProjectUser> pmsProjectUsers = projectUserMap.get(checkinDay.getUserId());
                    if (CollUtil.isEmpty(pmsProjectUsers)) {
                        continue; // 没有项目用户，跳过
                    }

                    // 过滤符合条件的项目用户
                    validFlag = pmsProjectUsers.stream()
                            .anyMatch(projectUser -> {
                                PmsProject project = projectMap.get(projectUser.getProjectId());
                                return project != null && iPmsProjectService.matchProject(project, Convert.toDate(DateUtil.format(checkinDay.getStatDay(), DatePattern.NORM_DATE_PATTERN) + " 00:00:00"), Convert.toDate(DateUtil.format(checkinDay.getStatDay(), DatePattern.NORM_DATE_PATTERN) + " 23:59:59"));
                            });
                }

                if (!validFlag) {
                    continue; // 没有匹配的项目用户，跳过
                }

                // 获取对应日期的工作记录
                List<SysWorkRecord> records = workRecordList.stream().filter(t -> Objects.equals(t.getUserId(), userId) && DateUtil.isSameDay(t.getWorkTime(), checkinDay.getStatDay())).collect(Collectors.toList());
                // 判断是否存在该用户的工作记录
                boolean isPresent = CollUtil.isNotEmpty(records) &&
                        records.stream().anyMatch(record -> record.getUserId().equals(checkinDay.getUserId()));
                if (!isPresent) {
                    dataList.add(checkinDay);
                }
            }
        }

        if (CollUtil.isNotEmpty(dataList)) {
            Map<Long, List<SysCheckinDay>> collect = dataList.stream()
                    .collect(Collectors.groupingBy(
                            SysCheckinDay::getUserId,
                            Collectors.collectingAndThen(
                                    Collectors.toList(),
                                    list -> list.stream()
                                            .sorted(Comparator.comparing(SysCheckinDay::getStatDay))
                                            .collect(Collectors.toList())
                            )
                    ));
            JSONObject obj = new JSONObject();
            obj.set("data", collect);
            iSysMessageService.sendMessage(MsgTypeEnum.unreportedReminder.getCode(), "未填报提醒", "未填报提醒", new ArrayList<>(collect.keySet()), obj);
        }
    }
}
