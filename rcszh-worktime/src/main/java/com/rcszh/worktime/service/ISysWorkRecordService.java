package com.rcszh.worktime.service;


import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.extension.service.IService;
import com.rcszh.base.common.core.page.TableDataInfo;
import com.rcszh.worktime.domain.SysWorkRecord;
import com.rcszh.worktime.domain.dto.expect_work_time.EveryMonthWorkTime;
import com.rcszh.worktime.domain.dto.expect_work_time.ExpectWorkTimeCompareDto;

import com.rcszh.basic.domain.dto.BatchSaveWorkRecordDto;
import com.rcszh.basic.domain.dto.EntryWorkRecordDto;
import com.rcszh.basic.domain.dto.UserReportAnalysisDTO;
import com.rcszh.basic.domain.dto.WorkRecordMonthStatDto;
import com.rcszh.worktime.domain.dto.SysWorkRecordDto;
import com.rcszh.worktime.domain.vo.ProjectUserWorkTimeProportionVo;
import com.rcszh.worktime.domain.vo.SysWorkRecordVo;
import com.rcszh.worktime.domain.vo.WorkTimeBarVo;
import jakarta.validation.Valid;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 项目管理Service接口
 *
 * <AUTHOR>
 * @date 2024-06-07
 */
public interface ISysWorkRecordService extends IService<SysWorkRecord> {

    List<SysWorkRecord> findByUserId(Long userId, Date startTime, Date endTime);

    List<SysWorkRecordVo> find(SysWorkRecordDto sysWorkRecordDto);

    void checkWorkRecordTimeEntryLimit(Date workTime);

    void checkWorkHoursReportingDeadline(Long userId, List<Date> workTimes);

    boolean saveWorkRecord(EntryWorkRecordDto entryWorkRecordDto);

    List<SysWorkRecord> batchSaveWorkRecord(BatchSaveWorkRecordDto batchSaveWorkRecordDto);

    List<SysWorkRecord> saveOrUpdateWorkRecord(EntryWorkRecordDto entryWorkRecordDto, List<SysWorkRecord> dbWorkRecords,
                                               Integer regularWorkDuration, Integer standardWorkDuration, Integer maxWorkDuration);

    int removeWorkRecord(Long workRecordId);

    Map<Long, List<SysWorkRecord>> findWorkRecordsByUserIds(List<Long> userIds, Date startTime, Date endTime);

    List<SysWorkRecord> findByProjectAndUser(Long userId, Long projectId, Date startTime, Date endTime);

    List<SysWorkRecord> monthStat(WorkRecordMonthStatDto workRecordMonthStat);

    void importWorkRecord(List<Map<String, String>> data, String workTimeUnit);

    List<Long> findWorkRecordUserIds(WorkRecordMonthStatDto workRecordMonthStat);

    List<SysWorkRecord> projectWorkTimeTotal(Date startTime, Date endTime);

    List<Map<String, Object>> queryUserSubmissionStatus(String startDate, String endDate);

    List<UserReportAnalysisDTO> userReportAnalysis(Map<String, Object> params);

    void initWorkDurationPercent();

    List<SysWorkRecord> findWorkRecordByDate(Map<String, Object> params);

    List<SysWorkRecord> findWorkRecordByDepartment(Map<String, Object> query);

    TableDataInfo jiMuPageList(SysWorkRecordDto sysWorkRecordDto);

    // 获取每个月的已审批的工时
    List<EveryMonthWorkTime> getEveryMonthWorkTime(DateTime start, DateTime end, ExpectWorkTimeCompareDto param);
    // 获取每个月的已审批的工时
    WorkTimeBarVo getYearWorkTimeBar(ExpectWorkTimeCompareDto param);
    // 获取单个项目中每个成员占所有工时的比例
    List<ProjectUserWorkTimeProportionVo> getProjectUserWorkTimeDiffPie(@Valid ExpectWorkTimeCompareDto param);
}
