package com.rcszh.worktime.service;



import com.rcszh.basic.domain.PmsConfig;
import com.rcszh.worktime.domain.dto.expect_work_time.ExpectWorkTimeCompareDto;
import com.rcszh.worktime.domain.dto.expect_work_time.SysExpectProjectWorkTimeDto;
import com.rcszh.worktime.domain.dto.expect_work_time.SysExpectUserWorkTimeDto;
import com.rcszh.worktime.domain.expect_work.SysExpectProjectWorkTime;
import com.rcszh.worktime.domain.vo.ExpectWorkTimeComparePieVo;
import com.rcszh.worktime.domain.vo.ExpectWorkTimeCompareVo;

import java.util.Date;
import java.util.List;

public interface ISysExpectWorkTimeService {
    // 保存项目工时配置
    void saveExpectProjectWorkTime(SysExpectProjectWorkTimeDto sysExpectProjectWorkTime);

    // 根据id删除项目工时配置
    void deleteExpectProjectWorkTimeById(String id);

    // 更新项目预估工时配置
    void updateExpectProjectWorkTime(SysExpectProjectWorkTimeDto sysExpectProjectWorkTime);

    // 分页查询项目预估工时配置
    List<SysExpectProjectWorkTimeDto> getExpectProjectWorkTime(SysExpectProjectWorkTime sysExpectProjectWorkTime);

    // 根据id查询项目预估工时配置
    SysExpectProjectWorkTimeDto getExpectProjectWorkTimeById(String id);

    // 根据项目id和预期时间查询项目工时配置
    SysExpectProjectWorkTime getExpectProjectWorkTimeByProjectIdAndDate(Long id, Date expectDate);
    // 保存用户工时配置
    void saveExpectUserWorkTime(SysExpectUserWorkTimeDto sysExpectUserWorkTime);

    // 查询用户工时配置
    List<SysExpectUserWorkTimeDto> getExpectUserWorkTime(SysExpectUserWorkTimeDto sysExpectUserWorkTime);

    // 根据项目id查询用户工时配置
    SysExpectUserWorkTimeDto getExpectUserWorkTime(String id);

    // 根据id删除用户工时配置
    void deleteExpectUserWorkTimeById(String id);

    // 批量保存用户工时配置
    void saveBatchExpectUserWorkTime(List<SysExpectUserWorkTimeDto> list);

    // 获取配置
    PmsConfig getConfigByKey(String key);

    // 获取预计工时和实际工时对比
    ExpectWorkTimeCompareVo getWorkTimeCompareBar(ExpectWorkTimeCompareDto param);

    // 获取预计工时和实际工时对比折线图
    ExpectWorkTimeCompareVo getWorkTimeCompareLine(ExpectWorkTimeCompareDto param);

    // 获取项目工时差异分布饼图
    List<ExpectWorkTimeComparePieVo> getProjectDiffPie(ExpectWorkTimeCompareDto param);

}
