package com.rcszh.worktime.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.rcszh.worktime.domain.SysWorkHourIgnoreUsers;

import java.util.List;
import java.util.Set;

public interface ISysWorkHourIgnoreUsersService extends IService<SysWorkHourIgnoreUsers> {
    List<SysWorkHourIgnoreUsers> queryPage(SysWorkHourIgnoreUsers sysWorkHourIgnoreUsers);

    void batchSave(List<Long> userIds);

    Set<Long> findAllUserIds();
}
