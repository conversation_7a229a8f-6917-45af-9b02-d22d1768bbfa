package com.rcszh.worktime.service;

import com.rcszh.base.common.core.page.TableDataInfo;
import com.rcszh.basic.domain.dto.UserReportAnalysisDTO;

import java.util.List;
import java.util.Map;

public interface IPmsWorkRecordService {

    TableDataInfo queryReportWarning(Map<String, Object> params);

    Map<String, List<Map<String, Object>>> queryReportWarningExport(Map<String, Object> params);

    List<Map<String,Object>> getAttendanceList(String date,Long deptId);

    List<Map<String,Object>> getUnreportedPersons(String date,Long deptId);

    List<UserReportAnalysisDTO> userReportAnalysis(Map<String, Object> params);

    List<UserReportAnalysisDTO> newUserReportAnalysis(Map<String, Object> params);

    void sendUnreportedUserReminder(Map<String, Object> params);
}
