package com.rcszh.worktime.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rcszh.attendance.domain.SysCheckinDay;
import com.rcszh.attendance.domain.SysVacationDay;
import com.rcszh.attendance.domain.dto.SysCheckinDayDTO;
import com.rcszh.attendance.enums.CheckinStatus;
import com.rcszh.attendance.enums.VacationType;
import com.rcszh.attendance.service.ISysCheckinDayService;
import com.rcszh.attendance.service.ISysVacationDayService;
import com.rcszh.base.common.constant.TimeConstant;
import com.rcszh.base.common.core.domain.entity.SysUser;
import com.rcszh.base.common.core.page.TableDataInfo;
import com.rcszh.base.common.exception.ServiceException;
import com.rcszh.basic.domain.SysHoliday;
import com.rcszh.basic.domain.dto.*;
import com.rcszh.basic.service.IPmsConfigService;
import com.rcszh.basic.service.ISysCommonService;
import com.rcszh.basic.service.ISysHolidayService;
import com.rcszh.cache.BaseDataTranslateUtils;
import com.rcszh.cache.CacheUtil;
import com.rcszh.cache.SimpleObjCacheDto;
import com.rcszh.cache.service.CacheService;
import com.rcszh.common.constant.Constants;
import com.rcszh.common.util.PageUtil;
import com.rcszh.common.util.WorkTimeUtil;
import com.rcszh.project.domain.PmsProject;
import com.rcszh.project.service.IPmsProjectService;
import com.rcszh.worktime.domain.PmsPublicUserLine;
import com.rcszh.worktime.domain.SysWorkEntry;
import com.rcszh.worktime.domain.SysWorkRecord;
import com.rcszh.worktime.domain.dto.ProjectUserWorkTimeProportionDto;
import com.rcszh.worktime.domain.dto.SysWorkRecordDto;
import com.rcszh.worktime.domain.dto.expect_work_time.EveryMonthWorkTime;
import com.rcszh.worktime.domain.dto.expect_work_time.ExpectWorkTimeCompareDto;
import com.rcszh.worktime.domain.vo.ProjectUserWorkTimeProportionVo;
import com.rcszh.worktime.domain.vo.SysWorkRecordItemVo;
import com.rcszh.worktime.domain.vo.SysWorkRecordVo;
import com.rcszh.worktime.domain.vo.WorkTimeBarVo;
import com.rcszh.worktime.enums.WorkRecordStatus;
import com.rcszh.worktime.enums.WorkRecordType;
import com.rcszh.worktime.mapper.SysWorkRecordMapper;
import com.rcszh.worktime.service.IPmsPublicUserService;
import com.rcszh.worktime.service.ISysWorkEntryService;
import com.rcszh.worktime.service.ISysWorkRecordService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 项目管理Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysWorkRecordServiceImpl extends ServiceImpl<SysWorkRecordMapper, SysWorkRecord> implements ISysWorkRecordService {

    @Autowired
    private SysWorkRecordMapper sysWorkRecordMapper;

    @Autowired
    private ISysCheckinDayService sysCheckinDayService;

    @Autowired
    private ISysVacationDayService sysVacationDayService;

    @Autowired
    private ISysHolidayService sysHolidayService;

    @Autowired
    private IPmsConfigService pmsConfigService;

    @Autowired
    private IPmsProjectService pmsProjectService;

    @Autowired
    private ISysCommonService commonService;

    @Autowired
    private IPmsPublicUserService pmsPublicUserService;

    @Autowired
    private ISysWorkEntryService sysWorkEntryService;

    @Autowired
    private BaseDataTranslateUtils baseDataTranslateUtils;

    @Autowired
    private CacheUtil cacheUtil;

    @Autowired
    private CacheService cacheService;

    @Override
    public List<SysWorkRecord> findByUserId(Long userId, Date startTime, Date endTime) {
        LambdaQueryWrapper<SysWorkRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysWorkRecord::getUserId, userId);
        queryWrapper.between(SysWorkRecord::getWorkTime, DateUtil.beginOfDay(startTime), DateUtil.endOfDay(endTime));
        return sysWorkRecordMapper.selectList(queryWrapper);
    }

    @Override
    public List<SysWorkRecordVo> find(SysWorkRecordDto sysWorkRecordDto) {
        LambdaQueryWrapper<SysWorkRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysWorkRecord::getUserId, sysWorkRecordDto.getUserId());
        queryWrapper.between(SysWorkRecord::getWorkTime, DateUtil.beginOfDay(sysWorkRecordDto.getStartTime()), DateUtil.endOfDay(sysWorkRecordDto.getEndTime()));
        List<SysWorkRecord> sysWorkRecords = sysWorkRecordMapper.selectList(queryWrapper);
        baseDataTranslateUtils.baseDataTranslate(sysWorkRecords);
//        this.fullField(sysWorkRecords);
        Map<Date, List<SysWorkRecord>> workRecordGroup = sysWorkRecords.stream().collect(Collectors.groupingBy(SysWorkRecord::getWorkTime));
        SysCheckinDayDTO checkinDayDTO = new SysCheckinDayDTO();
        checkinDayDTO.setUserId(sysWorkRecordDto.getUserId());
        checkinDayDTO.setStartTime(sysWorkRecordDto.getStartTime());
        checkinDayDTO.setEndTime(sysWorkRecordDto.getEndTime());

        List<SysVacationDay> vacationDays = sysVacationDayService.findVacationDaysByUserId(sysWorkRecordDto.getStartTime(), sysWorkRecordDto.getEndTime(), sysWorkRecordDto.getUserId());
        Map<Date, List<SysVacationDay>> vacationGroup = vacationDays.stream().collect(Collectors.groupingBy(SysVacationDay::getStatDay));

        // 拿配置，获取最大填报工时; 未配置：没有限制
        Integer maxWorkDuration = pmsConfigService.getMaxWorkMinute();
        Integer defaultStandardWorkDuration = Integer.parseInt(pmsConfigService.selectByConfigKey("defaultStandardWorkDuration"));
        String entryValidateCheckin = pmsConfigService.selectByConfigKey("entryValidateCheckin");
        String workHoursReportingEnabled = pmsConfigService.selectByConfigKey("workHoursReportingEnabled");
        String timeReportingDeadline = pmsConfigService.selectByConfigKey("timeReportingDeadline");

        Date startTime = DateUtil.beginOfDay(sysWorkRecordDto.getStartTime());
        Date endTime = DateUtil.beginOfDay(sysWorkRecordDto.getEndTime());

        Date today = DateUtil.beginOfDay(new Date());
        List<SysWorkRecordVo> result = new ArrayList<>();

        List<SysCheckinDay> checkinDays = sysCheckinDayService.findCheckinDays(checkinDayDTO);
        Map<Date, SysCheckinDay> checkinDayMap = checkinDays.stream().collect(Collectors.toMap(SysCheckinDay::getStatDay, t -> t, (k1, k2) -> k1));
        List<SysHoliday> holidays = sysHolidayService.findHolidaysByTime(startTime, endTime);
        Map<String, SysHoliday> holidayMap = holidays.stream().collect(Collectors.toMap(item -> String.valueOf(item.getDate()), t -> t, (k1, k2) -> k1));

        List<SysWorkEntry> sysWorkEntries = sysWorkEntryService.list(new LambdaQueryWrapper<SysWorkEntry>()
                .eq(SysWorkEntry::getUserId, sysWorkRecordDto.getUserId())
                .between(SysWorkEntry::getWorkTime, startTime, endTime));
        Map<Date, SysWorkEntry> sysWorkEntryMap = sysWorkEntries.stream().collect(Collectors.toMap(SysWorkEntry::getWorkTime, t -> t, (k1, k2) -> k1));

        List<PmsPublicUserLine> pmsPublicUserLines = pmsPublicUserService.findEffectiveProjects(sysWorkRecordDto.getUserId(), sysWorkRecordDto.getStartTime(), sysWorkRecordDto.getEndTime());

        while (startTime.getTime() <= endTime.getTime()) {
            SysWorkRecordVo sysWorkRecordVo = new SysWorkRecordVo();
            sysWorkRecordVo.setWorkTime(startTime);
            ArrayList<SysWorkRecordItemVo> items = new ArrayList<>();
            sysWorkRecordVo.setItems(items);
            SysCheckinDay sysCheckinDay = checkinDayMap.get(startTime);
            sysWorkRecordVo.setMaxWorkDuration(maxWorkDuration);

            Date finalStartTime = startTime;
            PmsPublicUserLine pmsPublicUserLine = pmsPublicUserLines.stream()
                    .filter(d -> d.getStartTime().getTime() <= finalStartTime.getTime() && d.getEndTime().getTime() >= finalStartTime.getTime())
                    .findFirst().orElse(null);
            sysWorkRecordVo.setIsPublicUser(Objects.nonNull(pmsPublicUserLine) ? Constants.Y : Constants.N);

            List<SysWorkRecord> workRecords = workRecordGroup.get(startTime);
            if (CollUtil.isNotEmpty(workRecords)) {
                List<String> priorityStatusList = Arrays.asList(WorkRecordStatus.audited.getCode(), WorkRecordStatus.pending.getCode());
                workRecords.sort((a, b) -> {
                    if (priorityStatusList.contains(a.getStatus()) && priorityStatusList.contains(b.getStatus())) {
                        return 0;
                    }
                    if (priorityStatusList.contains(a.getStatus())) {
                        return -1;
                    }
                    if (priorityStatusList.contains(b.getStatus())) {
                        return 1;
                    }
                    return 0;
                });
                workRecords.sort(Comparator.comparing(SysWorkRecord::getWorkTime));
                for (SysWorkRecord workRecord : workRecords) {
                    SysWorkRecordItemVo sysWorkRecordItemVo = new SysWorkRecordItemVo();
                    BeanUtils.copyProperties(workRecord, sysWorkRecordItemVo);
                    items.add(sysWorkRecordItemVo);
                }
            }

            if (Constants.Y.equals(entryValidateCheckin)) {
                if (Objects.nonNull(sysCheckinDay)) {
                    Integer oldRegularWorkMinute = null;
                    if (CollUtil.isNotEmpty(workRecords)) {
                        for (SysWorkRecord workRecord : workRecords) {
                            oldRegularWorkMinute = workRecord.getRegularWorkDuration();
                            if (Objects.nonNull(workRecord.getMaxWorkDuration()) && workRecord.getRegularWorkDuration() > workRecord.getMaxWorkDuration()) {
                                oldRegularWorkMinute = workRecord.getMaxWorkDuration();
                            }
                        }
                    }
                    // 待审批、已审批的使用历史 考勤时长、标准时长
                    boolean isAudited = false;
                    if (CollUtil.isNotEmpty(workRecords)) {
                        for (SysWorkRecord item : workRecords) {
                            isAudited = Arrays.asList(WorkRecordStatus.audited.getCode(), WorkRecordStatus.pending.getCode()).contains(item.getStatus());
                            if (isAudited) {
                                if (Objects.nonNull(item.getMaxWorkDuration()) && item.getRegularWorkDuration() > item.getMaxWorkDuration()) {
                                    sysWorkRecordVo.setRegularWorkDuration(Convert.toBigDecimal(item.getMaxWorkDuration()));
                                } else {
                                    sysWorkRecordVo.setRegularWorkDuration(Convert.toBigDecimal(item.getRegularWorkDuration()));
                                }
                                sysWorkRecordVo.setStandardWorkDuration(item.getStandardWorkDuration());
                            }
                            break;
                        }
                    }
                    BigDecimal regularWorkMinute = Convert.toBigDecimal(WorkTimeUtil.transformMinuteBig(sysCheckinDay.getRegularWorkSec()));
                    if (Objects.nonNull(maxWorkDuration) && maxWorkDuration.doubleValue() < regularWorkMinute.doubleValue()) {
                        regularWorkMinute = Convert.toBigDecimal(maxWorkDuration);
                    }
                    if (!isAudited) {
                        sysWorkRecordVo.setRegularWorkDuration(regularWorkMinute);
                        sysWorkRecordVo.setStandardWorkDuration4Sec(sysCheckinDay.getStandardWorkSec());
                    }
                    sysWorkRecordVo.setHasCheckin(true);
                    // 当天没有在途的数据，且考勤时长有变动：场景: 存在草稿状态的填报记录，后面考勤变动了
                    sysWorkRecordVo.setRegularChanged(!isAudited && Objects.nonNull(oldRegularWorkMinute) && oldRegularWorkMinute.doubleValue() != regularWorkMinute.doubleValue());

                    List<SysVacationDay> sysVacationDays = vacationGroup.get(startTime);
                    if (CollUtil.isNotEmpty(sysVacationDays)) {
                        for (SysVacationDay sysVacationDay : sysVacationDays) {
                            if (VacationType.vacation.getCode().equals(sysVacationDay.getType())) {
                                sysWorkRecordVo.setStatusType(CheckinStatus.vacation.name());
                                sysWorkRecordVo.setStatusTag(sysVacationDay.getName());
                            } else if (VacationType.travel.getCode().equals(sysVacationDay.getType())) {
                                sysWorkRecordVo.setStatusType(CheckinStatus.travel.name());
                                sysWorkRecordVo.setStatusTag(sysVacationDay.getName());
                            }
                        }
                    }
                    // 考勤异常时
                    if (Objects.equals(regularWorkMinute, BigDecimal.ZERO)) {
                        if (StrUtil.isBlank(sysWorkRecordVo.getStatusType())) {
                            // 如果是法定节假日
                            SysHoliday holiday = holidayMap.get(DateUtil.format(startTime, "yyyyMMdd"));
                            if (Objects.nonNull(holiday) && holiday.getHolidayRecess() == 1 && holiday.getWorkday() != 1) {
                                sysWorkRecordVo.setStatusType(CheckinStatus.legalHoliday.name());
                                sysWorkRecordVo.setStatusTag(holiday.getHolidayCn());
                            } else if (sysWorkRecordVo.getStandardWorkDuration() == 0) {
                                sysWorkRecordVo.setStatusType(CheckinStatus.holiday.name());
                                sysWorkRecordVo.setStatusTag("休息");
                            } else {
                                sysWorkRecordVo.setStatusType(CheckinStatus.unusual.name());
                                sysWorkRecordVo.setStatusTag("考勤异常");
                            }
                        }
                    } else {
                        String status = null;
                        // 按状态优先级显示statusTag
                        for (SysWorkRecordItemVo item : sysWorkRecordVo.getItems()) {
                            if (WorkRecordStatus.rejected.getCode().equals(item.getStatus())) {
                                status = item.getStatus();
                                sysWorkRecordVo.setStatusType(CheckinStatus.normal.name());
                                sysWorkRecordVo.setStatusTag(WorkRecordStatus.getNameByCode(item.getStatus()));
                                break;
                            }
                            if (WorkRecordStatus.cancel.getCode().equals(item.getStatus())) {
                                status = item.getStatus();
                                sysWorkRecordVo.setStatusType(CheckinStatus.normal.name());
                                sysWorkRecordVo.setStatusTag(WorkRecordStatus.getNameByCode(item.getStatus()));
                                break;
                            }
                            if (WorkRecordStatus.confirming.getCode().equals(item.getStatus())) {
                                status = item.getStatus();
                                sysWorkRecordVo.setStatusType(CheckinStatus.normal.name());
                                sysWorkRecordVo.setStatusTag(WorkRecordStatus.getNameByCode(item.getStatus()));
                                break;
                            }
                            if (WorkRecordStatus.pending.getCode().equals(item.getStatus())) {
                                status = item.getStatus();
                                sysWorkRecordVo.setStatusType(CheckinStatus.normal.name());
                                sysWorkRecordVo.setStatusTag(WorkRecordStatus.getNameByCode(item.getStatus()));
                                break;
                            }
                        }
                        if (status == null) {
                            for (SysWorkRecordItemVo item : sysWorkRecordVo.getItems()) {
                                status = item.getStatus();
                                sysWorkRecordVo.setStatusType(CheckinStatus.normal.name());
                                sysWorkRecordVo.setStatusTag(WorkRecordStatus.getNameByCode(item.getStatus()));
                            }
                        }
                        // 设置逾期：1、没有填报  2、已填报，但是状态未提交、已撤回
                        if (StrUtil.isEmpty(status) || Arrays.asList(WorkRecordStatus.confirming.getCode(), WorkRecordStatus.rejected.getCode(), WorkRecordStatus.cancel.getCode()).contains(status)) {
                            SysWorkEntry sysWorkEntry = sysWorkEntryMap.get(startTime);
                            if (Objects.nonNull(sysWorkEntry)) {
                                sysWorkRecordVo.setStatusType(CheckinStatus.normal.name());
                            } else if (StrUtil.isNotBlank(workHoursReportingEnabled) && StrUtil.equals(workHoursReportingEnabled, Constants.Y)) {
                                Date submissionTimeLimit = DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), -Convert.toInt(timeReportingDeadline)));
                                if (submissionTimeLimit.compareTo(startTime) > 0) {
                                    sysWorkRecordVo.setStatusType(CheckinStatus.delayed.name());
                                    sysWorkRecordVo.setStatusTag("已逾期");
                                }
                            }
                        }
                        if (StrUtil.isBlank(sysWorkRecordVo.getStatusType())) {
                            sysWorkRecordVo.setStatusType(CheckinStatus.normal.name());
                        }
                    }
                } else {
                    sysWorkRecordVo.setRegularWorkDuration(BigDecimal.ZERO);
                    sysWorkRecordVo.setStandardWorkDuration(0);
                    sysWorkRecordVo.setHasCheckin(false);
                    sysWorkRecordVo.setRegularChanged(false);
                    sysWorkRecordVo.setStatusType(CheckinStatus.miss.name());
                }
            } else {
                if (startTime.getTime() > today.getTime()) {
                    sysWorkRecordVo.setRegularWorkDuration(BigDecimal.ZERO);
                    sysWorkRecordVo.setStandardWorkDuration(0);
                    sysWorkRecordVo.setHasCheckin(false);
                    sysWorkRecordVo.setRegularChanged(false);
                    sysWorkRecordVo.setStatusType(CheckinStatus.miss.name());
                } else {
                    sysWorkRecordVo.setRegularWorkDuration(Convert.toBigDecimal(defaultStandardWorkDuration));
                    sysWorkRecordVo.setStandardWorkDuration(defaultStandardWorkDuration);
                    sysWorkRecordVo.setHasCheckin(true);
                    sysWorkRecordVo.setRegularChanged(false);
                    sysWorkRecordVo.setStatusType(CheckinStatus.normal.name());
                }
                for (SysWorkRecordItemVo item : sysWorkRecordVo.getItems()) {
                    String statusName = WorkRecordStatus.getNameByCode(item.getStatus());
//                    sysWorkRecordVo.setStatusType(CheckinStatus.normal.name());
                    sysWorkRecordVo.setStatusTag(statusName);
                    break;
                }
            }

            result.add(sysWorkRecordVo);
            startTime = DateUtil.offsetDay(startTime, 1);
        }
        return result;
    }

    @Override
    public void checkWorkRecordTimeEntryLimit(Date workTime) {
        String flag = pmsConfigService.selectByConfigKey("workTimeEntryLimit");
        int workTimeEntryLimit = 2;
        if (Objects.nonNull(flag)) {
            workTimeEntryLimit = Integer.parseInt(flag);
        }
        Date minMonth = DateUtil.offsetMonth(DateUtil.date(), -workTimeEntryLimit);
        if (DateUtil.date(workTime).compareTo(minMonth) < 0) {
            throw new ServiceException("只允许填报近" + flag + "个月的工时");
        }
    }

    /**
     * 未提交、已撤回状态需要校验逾期
     */
    @Override
    public void checkWorkHoursReportingDeadline(Long userId, List<Date> workTimes) {
        String workHoursReportingEnabled = pmsConfigService.selectByConfigKey("workHoursReportingEnabled");
        String timeReportingDeadline = pmsConfigService.selectByConfigKey("timeReportingDeadline");
        List<SysWorkEntry> sysWorkEntries = sysWorkEntryService.list(new LambdaQueryWrapper<SysWorkEntry>()
                .eq(SysWorkEntry::getUserId, userId)
                .in(SysWorkEntry::getWorkTime, workTimes));
        Map<Date, SysWorkEntry> sysWorkEntryMap = sysWorkEntries.stream().collect(Collectors.toMap(SysWorkEntry::getWorkTime, t -> t, (k1, k2) -> k1));
        for (Date workTime : workTimes) {
            SysWorkEntry sysWorkEntry = sysWorkEntryMap.get(workTime);
            if (Objects.isNull(sysWorkEntry)) {
                if (StrUtil.isNotBlank(workHoursReportingEnabled) && StrUtil.equals(workHoursReportingEnabled, Constants.Y)) {
                    Date submissionTimeLimit = DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), -Convert.toInt(timeReportingDeadline)));
                    if (submissionTimeLimit.compareTo(workTime) > 0) {
                        throw new ServiceException("只允许填报" + timeReportingDeadline + "天内的工时");
                    }
                }
            }
        }
    }

    private boolean needCheckWorkDurationSum() {
        String workDurationEqCheckinDuration = pmsConfigService.selectByConfigKey("workDurationEqCheckinDuration");
        return Constants.Y.equals(workDurationEqCheckinDuration);
    }

    private void checkWorkLogRequired(EntryWorkRecordDto workRecordDto, boolean isBatch) {
        if (CollUtil.isEmpty(workRecordDto.getItems())) {
            return;
        }
        String workLogRequired = pmsConfigService.selectByConfigKey("workLogRequired");
        if (Constants.Y.equals(workLogRequired)) {
            for (EntryWorkRecordItemDto item : workRecordDto.getItems()) {
                if (StrUtil.isBlank(item.getRemark())) {
                    SimpleObjCacheDto projectCache = cacheUtil.findProjectById(item.getProjectId());
                    String message = projectCache.getName() + "的工作日志必须填写";
                    if (isBatch) {
                        message = StrUtil.format("{} {}的工作日志必须填写", DateUtil.formatDate(workRecordDto.getWorkTime()), projectCache.getName());
                    }
                    throw new ServiceException(message);
                }
            }
        }
    }

    /**
     * 存在未提交、已撤回、已驳回状态的工时记录
     */
    private boolean existWorkRecordChange(List<SysWorkRecord> dbWorkRecords, EntryWorkRecordDto entryWorkRecordDto) {
        Set<String> recordSet = dbWorkRecords.stream().map(item -> StrUtil.format("{}_{}_{}", item.getProjectId(), entryWorkRecordDto.getUserId(), entryWorkRecordDto.getWorkTime())).collect(Collectors.toSet());
        for (EntryWorkRecordItemDto item : entryWorkRecordDto.getItems()) {
            String key = StrUtil.format("{}_{}_{}", item.getProjectId(), entryWorkRecordDto.getUserId(), entryWorkRecordDto.getWorkTime());
            if (!recordSet.contains(key)) {
                return true;
            }
        }
        return false;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveWorkRecord(EntryWorkRecordDto entryWorkRecordDto) {
        this.checkWorkRecordTimeEntryLimit(entryWorkRecordDto.getWorkTime());
        this.checkWorkLogRequired(entryWorkRecordDto, false);
        this.checkWorkHoursReportingDeadline(entryWorkRecordDto.getUserId(), Collections.singletonList(entryWorkRecordDto.getWorkTime()));

        Integer maxWorkDuration = pmsConfigService.getMaxWorkMinute();
        int defaultStandardWorkDuration = Integer.parseInt(pmsConfigService.selectByConfigKey("defaultStandardWorkDuration"));
        int regularWorkDuration = 0;
        int standardWorkDuration = 0;
        String entryValidateCheckin = pmsConfigService.selectByConfigKey("entryValidateCheckin");
        if (Constants.Y.equals(entryValidateCheckin)) {
            SysCheckinDay checkinDay = sysCheckinDayService.getCheckinDay(entryWorkRecordDto.getUserId(), entryWorkRecordDto.getWorkTime());
            if (Objects.isNull(checkinDay)) {
                throw new ServiceException("考勤记录不存在");
            }
            regularWorkDuration = WorkTimeUtil.transformMinute(checkinDay.getRegularWorkSec());
            standardWorkDuration = WorkTimeUtil.transformMinute(checkinDay.getStandardWorkSec());
        } else {
            regularWorkDuration = defaultStandardWorkDuration;
            standardWorkDuration = defaultStandardWorkDuration;
        }
        // 在途的工时
        LambdaQueryWrapper<SysWorkRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysWorkRecord::getWorkTime, entryWorkRecordDto.getWorkTime());
        queryWrapper.eq(SysWorkRecord::getUserId, entryWorkRecordDto.getUserId());
        queryWrapper.in(SysWorkRecord::getStatus, Arrays.asList(WorkRecordStatus.pending, WorkRecordStatus.audited));
        List<SysWorkRecord> dbWorkRecords = sysWorkRecordMapper.selectList(queryWrapper);

        if (this.existWorkRecordChange(dbWorkRecords, entryWorkRecordDto)) {
            int sumWorkDuration = entryWorkRecordDto.getItems().stream().mapToInt(EntryWorkRecordItemDto::getWorkDuration).sum();
            if (sumWorkDuration > regularWorkDuration) {
                throw new ServiceException("工时超出考勤总工时");
            }
            if (Objects.nonNull(maxWorkDuration) && sumWorkDuration > maxWorkDuration) {
                throw new ServiceException("工时超出工时上限");
            }
            if (CollUtil.isNotEmpty(entryWorkRecordDto.getItems()) && this.needCheckWorkDurationSum()) {
                PmsPublicUserLine publicUserLine = pmsPublicUserService.getEffectiveProject(entryWorkRecordDto.getUserId(), entryWorkRecordDto.getWorkTime());
                checkWorkDurationSum(publicUserLine, maxWorkDuration, regularWorkDuration, sumWorkDuration, entryWorkRecordDto.getWorkTime());
            }
        }
        // 删除待提交、驳回、撤回
        LambdaQueryWrapper<SysWorkRecord> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.eq(SysWorkRecord::getWorkTime, entryWorkRecordDto.getWorkTime());
        deleteWrapper.eq(SysWorkRecord::getUserId, entryWorkRecordDto.getUserId());
        deleteWrapper.in(SysWorkRecord::getStatus, Arrays.asList(WorkRecordStatus.confirming, WorkRecordStatus.rejected, WorkRecordStatus.cancel.getCode()));
        sysWorkRecordMapper.delete(deleteWrapper);

        this.saveOrUpdateWorkRecord(entryWorkRecordDto, dbWorkRecords, regularWorkDuration, standardWorkDuration, maxWorkDuration);

        Set<Long> projectIdSet = new HashSet<>();
        for (EntryWorkRecordItemDto item : entryWorkRecordDto.getItems()) {
            projectIdSet.add(item.getProjectId());
        }
        this.addUserProjectIdRecord(projectIdSet);
        return true;
    }

    public static void checkWorkDurationSum(PmsPublicUserLine publicUserLine, Integer maxWorkDuration, int regularWorkDuration, int sumWorkDuration, Date workTime) {
        if (publicUserLine == null) {
            if (Objects.nonNull(maxWorkDuration) && maxWorkDuration > 0
                    && regularWorkDuration > maxWorkDuration) {
                if (sumWorkDuration != maxWorkDuration) {
                    throw new ServiceException(DateUtil.formatDate(workTime) + "项目工时占比之和不等于100%，请修改");
                }
            } else if (sumWorkDuration != regularWorkDuration) {
                throw new ServiceException(DateUtil.formatDate(workTime) + "项目工时占比之和不等于100%，请修改");
            }
        } else {
            // TODO
        }
    }

    @Override
    public List<SysWorkRecord> saveOrUpdateWorkRecord(EntryWorkRecordDto entryWorkRecordDto, List<SysWorkRecord> dbWorkRecords, Integer regularWorkDuration, Integer standardWorkDuration, Integer maxWorkDuration) {
        Map<String, SysWorkRecord> dbWorkRecordMap = dbWorkRecords.stream().collect(Collectors.toMap(item -> StrUtil.format("{}_{}_{}", item.getProjectId(), item.getUserId(), item.getWorkTime()), t -> t, (k1, k2) -> k1));

        // TODO 多个页面提交时，如何做校验
        List<SysWorkRecord> workRecords = new ArrayList<>();
        for (EntryWorkRecordItemDto workRecordItem : entryWorkRecordDto.getItems()) {
            if (workRecordItem.getWorkDuration() == 0) {
                continue;
            }
            SysWorkRecord dbWorkRecord = dbWorkRecordMap.get(StrUtil.format("{}_{}_{}", workRecordItem.getProjectId(), entryWorkRecordDto.getUserId(), entryWorkRecordDto.getWorkTime()));
            if (Objects.nonNull(dbWorkRecord)) {
//                if (WorkRecordStatus.pending.getCode().equals(dbWorkRecord.getStatus())) {
//                    SysProject sysProject = sysProjectService.getById(workRecordItem.getProjectId());
//                    throw new ServiceException(StrUtil.format("项目-{}的工时处于待审核状态，不能修改", sysProject.getName()));
//                }
//                if (WorkRecordStatus.audited.getCode().equals(dbWorkRecord.getStatus())) {
//                    SysProject sysProject = sysProjectService.getById(workRecordItem.getProjectId());
//                    throw new ServiceException(StrUtil.format("项目-{}的工时处于已审核状态，不能修改", sysProject.getName()));
//                }
                List<String> editableStatusList = Arrays.asList(WorkRecordStatus.confirming.getCode(), WorkRecordStatus.rejected.getCode(), WorkRecordStatus.cancel.getCode());
                if (!editableStatusList.contains(dbWorkRecord.getStatus())) {
                    continue;
                }
            } else {
                dbWorkRecord = new SysWorkRecord();
                dbWorkRecord.setProjectId(workRecordItem.getProjectId());
                dbWorkRecord.setWorkTime(DateUtil.beginOfDay(entryWorkRecordDto.getWorkTime()));
                dbWorkRecord.setUserId(entryWorkRecordDto.getUserId());
                dbWorkRecord.setType(WorkRecordType.entry.getCode());
                dbWorkRecord.setStatus(WorkRecordStatus.confirming.getCode());
            }

            dbWorkRecord.setWorkDuration(workRecordItem.getWorkDuration());
            dbWorkRecord.setWorkDurationPercent(workRecordItem.getWorkDurationPercent());
            dbWorkRecord.fillBaseFields();
            dbWorkRecord.setRegularWorkDuration(regularWorkDuration);
            dbWorkRecord.setStandardWorkDuration(standardWorkDuration);
            dbWorkRecord.setMaxWorkDuration(maxWorkDuration);
            dbWorkRecord.setRemark(workRecordItem.getRemark());
            dbWorkRecord.setWorkLogId(workRecordItem.getWorkLogId());
            this.saveOrUpdate(dbWorkRecord);
            workRecords.add(dbWorkRecord);
        }
        return workRecords;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<SysWorkRecord> batchSaveWorkRecord(BatchSaveWorkRecordDto batchSaveWorkRecordDto) {
        Integer maxWorkDuration = pmsConfigService.getMaxWorkMinute();
        List<EntryWorkRecordDto> workRecordDtos = batchSaveWorkRecordDto.getWorkRecordDtos();
        List<Date> workTimes = workRecordDtos.stream().map(EntryWorkRecordDto::getWorkTime).toList();

        this.checkWorkHoursReportingDeadline(batchSaveWorkRecordDto.getUserId(), workTimes);
        for (EntryWorkRecordDto workRecordDto : workRecordDtos) {
            this.checkWorkRecordTimeEntryLimit(workRecordDto.getWorkTime());
            // 填报记录不跨月，校验一次即可
            break;
        }
        Date startTime = null;
        Date endTime = null;
        for (EntryWorkRecordDto workRecordDto : workRecordDtos) {
            this.checkWorkLogRequired(workRecordDto, true);
            if (CollUtil.isNotEmpty(workRecordDto.getItems())) {
                for (EntryWorkRecordItemDto item : workRecordDto.getItems()) {
                    if (startTime == null) {
                        startTime = workRecordDto.getWorkTime();
                    }
                    if (endTime == null) {
                        endTime = workRecordDto.getWorkTime();
                    }
                    if (startTime.compareTo(workRecordDto.getWorkTime()) > 0) {
                        startTime = workRecordDto.getWorkTime();
                    }
                    if (endTime.compareTo(workRecordDto.getWorkTime()) < 0) {
                        endTime = workRecordDto.getWorkTime();
                    }
                }
            }
        }
        LambdaQueryWrapper<SysCheckinDay> checkinDayLambdaQueryWrapper = new LambdaQueryWrapper<>();
        checkinDayLambdaQueryWrapper.eq(SysCheckinDay::getUserId, batchSaveWorkRecordDto.getUserId());
        checkinDayLambdaQueryWrapper.in(SysCheckinDay::getStatDay, workTimes);
        List<SysCheckinDay> checkinDays = sysCheckinDayService.list(checkinDayLambdaQueryWrapper);
        Map<String, SysCheckinDay> checkinDayMap = checkinDays.stream().collect(Collectors.toMap(item -> DateUtil.formatDate(item.getStatDay()), t -> t, (k1, k2) -> k1));

        List<PmsPublicUserLine> pmsPublicUserLines = pmsPublicUserService.findEffectiveProjects(batchSaveWorkRecordDto.getUserId(), startTime, endTime);

        int defaultStandardWorkDuration = Integer.parseInt(pmsConfigService.selectByConfigKey("defaultStandardWorkDuration"));
        String entryValidateCheckin = pmsConfigService.selectByConfigKey("entryValidateCheckin");
        for (EntryWorkRecordDto workRecordDto : workRecordDtos) {
            String curDate = DateUtil.formatDate(workRecordDto.getWorkTime());
            int regularWorkDuration = 0;
            if (Constants.Y.equals(entryValidateCheckin)) {
                SysCheckinDay checkinDay = checkinDayMap.get(curDate);
                if (Objects.isNull(checkinDay)) {
                    throw new ServiceException("考勤记录不存在");
                }
                regularWorkDuration = WorkTimeUtil.transformMinute(checkinDay.getRegularWorkSec());
            } else {
                regularWorkDuration = defaultStandardWorkDuration;
            }
            int sumWorkDuration = workRecordDto.getItems().stream().mapToInt(EntryWorkRecordItemDto::getWorkDuration).sum();
            if (sumWorkDuration > regularWorkDuration) {
                throw new ServiceException(curDate + "工时超出考勤总工时");
            }
            if (Objects.nonNull(maxWorkDuration) && sumWorkDuration > maxWorkDuration) {
                throw new ServiceException(curDate + "工时超出工时上限");
            }
            if (CollUtil.isNotEmpty(workRecordDto.getItems()) && this.needCheckWorkDurationSum()) {
                PmsPublicUserLine pmsPublicUserLine = pmsPublicUserLines.stream()
                        .filter(d -> d.getStartTime().getTime() <= workRecordDto.getWorkTime().getTime() && d.getEndTime().getTime() >= workRecordDto.getWorkTime().getTime())
                        .findFirst().orElse(null);
                checkWorkDurationSum(pmsPublicUserLine, maxWorkDuration, regularWorkDuration, sumWorkDuration, workRecordDto.getWorkTime());
            }
        }

        // 删除待提交、已驳回、已撤回
        LambdaQueryWrapper<SysWorkRecord> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.in(SysWorkRecord::getWorkTime, workTimes);
        deleteWrapper.eq(SysWorkRecord::getUserId, batchSaveWorkRecordDto.getUserId());
        deleteWrapper.in(SysWorkRecord::getStatus, Arrays.asList(WorkRecordStatus.confirming, WorkRecordStatus.rejected, WorkRecordStatus.cancel.getCode()));
        sysWorkRecordMapper.delete(deleteWrapper);

        LambdaQueryWrapper<SysWorkRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SysWorkRecord::getWorkTime, workTimes);
        queryWrapper.eq(SysWorkRecord::getUserId, batchSaveWorkRecordDto.getUserId());
        List<SysWorkRecord> dbWorkRecords = sysWorkRecordMapper.selectList(queryWrapper);

        List<SysWorkRecord> result = new ArrayList<>();
        for (EntryWorkRecordDto workRecordDto : workRecordDtos) {
            String curDate = DateUtil.formatDate(workRecordDto.getWorkTime());
            int regularWorkDuration = 0;
            int standardWorkDuration = 0;
            if (Constants.Y.equals(entryValidateCheckin)) {
                SysCheckinDay checkinDay = checkinDayMap.get(curDate);
                if (Objects.isNull(checkinDay)) {
                    throw new ServiceException("考勤记录不存在");
                }
                regularWorkDuration = WorkTimeUtil.transformMinute(checkinDay.getRegularWorkSec());
                standardWorkDuration = WorkTimeUtil.transformMinute(checkinDay.getStandardWorkSec());
            } else {
                regularWorkDuration = defaultStandardWorkDuration;
                standardWorkDuration = defaultStandardWorkDuration;
            }
            List<SysWorkRecord> workRecords = this.saveOrUpdateWorkRecord(workRecordDto, dbWorkRecords, regularWorkDuration, standardWorkDuration, maxWorkDuration);
            result.addAll(workRecords);
        }
        if (CollUtil.isEmpty(result)) {
            throw new ServiceException("请填入有效工时时长");
        }
        Set<Long> projectIdSet = new HashSet<>();
        for (EntryWorkRecordDto workRecordDto : workRecordDtos) {
            for (EntryWorkRecordItemDto item : workRecordDto.getItems()) {
                projectIdSet.add(item.getProjectId());
            }
        }
        this.addUserProjectIdRecord(projectIdSet);
        return result;
    }

    /**
     * 记录用户的项目填报记录
     *  后续用于myProject接口的排序
     *  最近填报的项目排在前面
     */
    private void addUserProjectIdRecord(Set<Long> projectIdSet) {
        // 1. 获取 Set 和顺序 String
        Set existingProjectIds = cacheService.get("userEntityProjectPriority:set", Set.class);
        if (existingProjectIds == null) {
            existingProjectIds = new HashSet<>();
        }

        String orderStr = cacheService.get("userEntityProjectPriority:order", String.class);
        List<Long> orderList = orderStr == null ? new ArrayList<>() : Arrays.stream(orderStr.split(","))
                .map(Long::valueOf)
                .collect(Collectors.toList());

        // 2. 遍历传入的 projectIdSet
        for (Long projectId : projectIdSet) {
            if (existingProjectIds.contains(projectId)) {
                // 如果已存在，先从顺序列表中移除，再添加到头部
                orderList.remove(projectId);
                orderList.addFirst(projectId);
            } else {
                // 如果不存在，直接添加到顺序列表头部和 Set
                orderList.addFirst(projectId);
                existingProjectIds.add(projectId);
            }

            // 如果顺序列表超过 5 个，移除尾部元素
            if (orderList.size() > 5) {
                Long removedProjectId = orderList.removeLast();
                existingProjectIds.remove(removedProjectId);
            }
        }

        // 3. 更新 CacheService
        cacheService.set("userEntityProjectPriority:set", existingProjectIds);
        cacheService.set("userEntityProjectPriority:order", orderList.stream()
                .map(String::valueOf)
                .collect(Collectors.joining(",")));
    }


    @Override
    public int removeWorkRecord(Long workRecordId) {
        SysWorkRecord sysWorkRecord = sysWorkRecordMapper.selectById(workRecordId);
        if (Objects.isNull(sysWorkRecord)) {
            throw new ServiceException("工时填报记录不存在");
        }
        if (WorkRecordStatus.pending.getCode().equals(sysWorkRecord.getStatus())) {
            throw new ServiceException("工时填报记录已提交，不能删除");
        }
        if (WorkRecordStatus.audited.getCode().equals(sysWorkRecord.getStatus())) {
            throw new ServiceException("工时填报记录已审核，不能删除");
        }
        return sysWorkRecordMapper.deleteById(workRecordId);
    }


    @Override
    public Map<Long, List<SysWorkRecord>> findWorkRecordsByUserIds(List<Long> userIds, Date startTime, Date endTime) {
        LambdaQueryWrapper<SysWorkRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SysWorkRecord::getUserId, userIds);
        queryWrapper.eq(SysWorkRecord::getStatus, WorkRecordStatus.audited.getCode());
        queryWrapper.eq(SysWorkRecord::getType, WorkRecordType.entry.getCode());
        queryWrapper.between(SysWorkRecord::getWorkTime, startTime, endTime);
        List<SysWorkRecord> workRecords = sysWorkRecordMapper.selectList(queryWrapper);
        return workRecords.stream().collect(Collectors.groupingBy(SysWorkRecord::getUserId));
    }

    @Override
    public List<SysWorkRecord> findByProjectAndUser(Long userId, Long projectId, Date startTime, Date endTime) {
        LambdaQueryWrapper<SysWorkRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysWorkRecord::getProjectId, projectId);
        queryWrapper.eq(SysWorkRecord::getUserId, userId);
        queryWrapper.eq(SysWorkRecord::getStatus, WorkRecordStatus.audited.getCode());
        queryWrapper.eq(SysWorkRecord::getType, WorkRecordType.entry.getCode());
        queryWrapper.between(SysWorkRecord::getWorkTime, startTime, endTime);
        return sysWorkRecordMapper.selectList(queryWrapper);
    }

    @Override
    public List<SysWorkRecord> monthStat(WorkRecordMonthStatDto workRecordMonthStat) {
        return sysWorkRecordMapper.monthStat(workRecordMonthStat);
    }

    @Override
    public void importWorkRecord(List<Map<String, String>> data, String workTimeUnit) {
        List<SysUser> allUsers = commonService.findAllUser();
        Map<String, SysUser> sysUserMap = allUsers.stream().collect(Collectors.toMap(SysUser::getNickName, t -> t, (k1, k2) -> k1));
        List<PmsProject> pmsProjects = pmsProjectService.list();
        Map<String, PmsProject> pmsProjectMap = pmsProjects.stream().collect(Collectors.toMap(PmsProject::getCode, t -> t, (k1, k2) -> k1));
        List<SysWorkRecord> list = new ArrayList<>();
        for (Map<String, String> item : data) {
            Date statDay = org.apache.poi.ss.usermodel.DateUtil.getJavaDate(Double.parseDouble(item.get("日期")));
            String nickName = item.get("姓名");
            SysUser sysUser = sysUserMap.get(nickName);
            if (Objects.isNull(sysUser)) {
                throw new ServiceException(nickName + "用户不存在");
            }
            for (Map.Entry<String, String> entry : item.entrySet()) {
                if (entry.getKey().equals("日期") || entry.getKey().equals("姓名") || entry.getKey().equals("手机号")) {
                    continue;
                }
                if (StrUtil.isBlank(entry.getValue())) {
                    continue;
                }
                BigDecimal bigDecimal = new BigDecimal(entry.getValue());
                if (bigDecimal.compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }
                PmsProject sysProject = pmsProjectMap.get(entry.getKey());
                if (Objects.isNull(sysProject)) {
                    log.error(StrUtil.format("{}项目不存在", entry.getKey()));
                    continue;
//                    throw new ServiceException(entry.getKey() + "项目不存在");
                }
                // 默认：分钟
                int workDuration = bigDecimal.intValue();
                if (StrUtil.isNotBlank(workTimeUnit) && workTimeUnit.equals("hour")) {
                    // 小时转分钟
                    workDuration = bigDecimal.multiply(new BigDecimal(60)).intValue();
                }

                SysWorkRecord sysWorkRecord = new SysWorkRecord();
                sysWorkRecord.setWorkTime(statDay);
                sysWorkRecord.setUserId(sysUser.getUserId());
                sysWorkRecord.setProjectId(sysProject.getId());
                sysWorkRecord.setWorkDuration(workDuration);
                sysWorkRecord.setStatus(WorkRecordStatus.audited.getCode());
                sysWorkRecord.setType(WorkRecordType.entry.getCode());
                sysWorkRecord.setIsInitData(Constants.Y);
                sysWorkRecord.fillBaseFields();
                list.add(sysWorkRecord);
            }
        }

        // 计算工时百分比
        Map<String, List<SysWorkRecord>> workRecordGroup = list.stream().collect(Collectors.groupingBy(item -> StrUtil.format("{}_{}", item.getUserId(), DateUtil.formatDate(item.getWorkTime()))));
        for (Map.Entry<String, List<SysWorkRecord>> entry : workRecordGroup.entrySet()) {
            List<SysWorkRecord> records = entry.getValue();
            int totalWorkDuration = 0;
            for (SysWorkRecord record : records) {
                totalWorkDuration += record.getWorkDuration();
            }
            BigDecimal calcAddWorkDurationPercent = BigDecimal.ZERO;
            for (int i = 0; i < records.size(); i++) {
                SysWorkRecord record = records.get(i);
                if (record.getWorkDuration() == 0) {
                    record.setWorkDurationPercent(BigDecimal.ZERO);
                    continue;
                }
                if (i == records.size() - 1) {
                    record.setWorkDurationPercent(new BigDecimal(100).subtract(calcAddWorkDurationPercent));
                } else {
                    BigDecimal workDurationPercent = new BigDecimal(record.getWorkDuration() * 100).divide(new BigDecimal(totalWorkDuration), 2, RoundingMode.FLOOR);
                    calcAddWorkDurationPercent = calcAddWorkDurationPercent.add(workDurationPercent);
                    record.setWorkDurationPercent(calcAddWorkDurationPercent);
                }
            }
        }

        System.out.println("over = " + list.size());
        this.saveBatch(list);
    }

    @Override
    public List<Long> findWorkRecordUserIds(WorkRecordMonthStatDto workRecordMonthStat) {
        return sysWorkRecordMapper.findWorkRecordUserIds(workRecordMonthStat);
    }

    @Override
    public List<SysWorkRecord> projectWorkTimeTotal(Date startTime, Date endTime) {
        return sysWorkRecordMapper.projectWorkTimeTotal(startTime, endTime);
    }

    @Override
    public List<Map<String, Object>> queryUserSubmissionStatus(String startDate, String endDate) {
        startDate = startDate + " 00:00:00";
        endDate = endDate + " 23:59:59";
        return sysWorkRecordMapper.queryUserSubmissionStatus(startDate, endDate);
    }

    @Override
    public List<UserReportAnalysisDTO> userReportAnalysis(Map<String, Object> params) {
        List<UserReportAnalysisDTO> userReportAnalysisDTOS = sysWorkRecordMapper.userReportAnalysis(params);
        baseDataTranslateUtils.baseDataTranslate(userReportAnalysisDTOS);
        return userReportAnalysisDTOS;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void initWorkDurationPercent() {
        List<SysWorkRecord> sysWorkRecords = sysWorkRecordMapper.selectList(new LambdaQueryWrapper<SysWorkRecord>()
                .lt(SysWorkRecord::getCreateTime, DateUtil.beginOfDay(new Date()))
        );
        Map<String, List<SysWorkRecord>> sysWorkRecordGroup = sysWorkRecords.stream().collect(Collectors.groupingBy(item -> StrUtil.format("{}_{}", item.getUserId(), DateUtil.formatDate(item.getWorkTime()))));
        for (Map.Entry<String, List<SysWorkRecord>> entry : sysWorkRecordGroup.entrySet()) {
            List<SysWorkRecord> records = entry.getValue();
            int totalWorkDuration = 0;
            int totalRegularWorkDuration = 0;
            for (SysWorkRecord record : records) {
                totalWorkDuration += record.getWorkDuration();
                totalRegularWorkDuration = record.getRegularWorkDuration();
            }
            BigDecimal calcAddWorkDurationPercent = BigDecimal.ZERO;
            for (int i = 0; i < records.size(); i++) {
                SysWorkRecord record = records.get(i);
                if (i == records.size() - 1 && totalRegularWorkDuration == totalWorkDuration) {
                    record.setWorkDurationPercent(new BigDecimal(100).subtract(calcAddWorkDurationPercent));
                } else {
                    BigDecimal workDurationPercent = new BigDecimal(record.getWorkDuration() * 100).divide(new BigDecimal(totalRegularWorkDuration), 2, RoundingMode.FLOOR);
                    calcAddWorkDurationPercent = calcAddWorkDurationPercent.add(workDurationPercent);
                    record.setWorkDurationPercent(workDurationPercent);
                }
            }
        }
        this.updateBatchById(sysWorkRecords);
    }

    /**
     * 获取时间范围的填报记录
     * @param params
     * @return
     */
    public List<SysWorkRecord> findWorkRecordByDate(Map<String, Object> params){
        LambdaQueryWrapper<SysWorkRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.ge(SysWorkRecord::getWorkTime,Convert.toDate(params.get("startDate")));
        wrapper.le(SysWorkRecord::getWorkTime,Convert.toDate(params.get("endDate")));
        return this.list(wrapper);
    }

    @Override
    public List<SysWorkRecord> findWorkRecordByDepartment(Map<String, Object> query) {
        return sysWorkRecordMapper.findWorkRecordByDepartment(query);
    }

    @Override
    public TableDataInfo jiMuPageList(SysWorkRecordDto sysWorkRecordDto) {
        List<SysWorkRecord> sysWorkRecords = sysWorkRecordMapper.selectList(new LambdaQueryWrapper<SysWorkRecord>()
                .eq(SysWorkRecord::getUserId, sysWorkRecordDto.getUserId())
                .eq(StrUtil.isNotBlank(sysWorkRecordDto.getStatus()), SysWorkRecord::getStatus, sysWorkRecordDto.getStatus())
                .gt(Objects.nonNull(sysWorkRecordDto.getStartTime()), SysWorkRecord::getWorkTime, DateUtil.beginOfDay(sysWorkRecordDto.getStartTime()))
                .lt(Objects.nonNull(sysWorkRecordDto.getEndTime()), SysWorkRecord::getWorkTime, DateUtil.endOfDay(sysWorkRecordDto.getEndTime()))
        );
        if (CollUtil.isEmpty(sysWorkRecords)) {
            return PageUtil.getDataTable(sysWorkRecords);
        }
        return PageUtil.getDataTable(sysWorkRecords, baseDataTranslateUtils.baseDataTranslate(sysWorkRecords));
    }

    @Override
    public List<EveryMonthWorkTime> getEveryMonthWorkTime(DateTime start, DateTime end, ExpectWorkTimeCompareDto param) {
        return getBaseMapper().getEveryMonthWorkTime(start, end, param);
    }

    @Override
    public WorkTimeBarVo getYearWorkTimeBar(ExpectWorkTimeCompareDto param) {
        String unit = param.getUnit();
        Date time = param.getTime();
        DateTime start = DateUtil.beginOfYear(time);
        DateTime end = DateUtil.endOfYear(time);
        Map<Integer, BigDecimal> collect = getEveryMonthWorkTime(start, end, param)
                .stream()
                .collect(Collectors.toMap(i -> Integer.parseInt(i.getMonth()), EveryMonthWorkTime::getWorkTime));
        WorkTimeBarVo workTimeBarVo = new WorkTimeBarVo();
        for (int i = 1; i <= 12; i++) {
            BigDecimal workTime = collect.getOrDefault(i, BigDecimal.ZERO);
            workTimeBarVo.getMonths().add(i);
            workTimeBarVo.getActualWorkTimes().add(TimeConstant.HOUR.equals(unit) ? workTime.divide(new BigDecimal(60), 2, RoundingMode.HALF_DOWN) : workTime);
        }
        return workTimeBarVo;
    }

    @Override
    public List<ProjectUserWorkTimeProportionVo> getProjectUserWorkTimeDiffPie(ExpectWorkTimeCompareDto param) {
        String unit = param.getUnit();
        Long projectId = param.getProjectId();
        Date time = param.getTime();
        DateTime start = DateUtil.beginOfMonth(time);
        DateTime end = DateUtil.endOfMonth(time);
        Long total =  getBaseMapper().findProjectTotalWorkTime(start,end,projectId);
        List<ProjectUserWorkTimeProportionDto>  userWorkTimeList = getBaseMapper().findProjectUserTotalWorkTime(start,end,projectId);
        List<ProjectUserWorkTimeProportionVo> result = new ArrayList<>();
        for (ProjectUserWorkTimeProportionDto item : userWorkTimeList) {
            ProjectUserWorkTimeProportionVo vo = new ProjectUserWorkTimeProportionVo();
            BigDecimal workTime = item.getWorkTime();
            vo.setName(item.getNickName());
            vo.setValue(item.getWorkTime().divide(new BigDecimal(total),2,RoundingMode.HALF_UP).multiply(new BigDecimal(100)));
            vo.setWorkTime(TimeConstant.HOUR.equals(unit) ? workTime.divide(new BigDecimal(60), 2, RoundingMode.HALF_DOWN) : workTime);
            result.add(vo);
        }
        return result;
    }
}
