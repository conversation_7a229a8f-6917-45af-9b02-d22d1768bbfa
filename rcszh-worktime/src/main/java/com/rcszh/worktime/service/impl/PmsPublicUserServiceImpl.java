package com.rcszh.worktime.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rcszh.base.common.core.domain.entity.SysUser;
import com.rcszh.base.common.core.page.TableDataInfo;
import com.rcszh.base.common.utils.PageUtils;
import com.rcszh.basic.service.ISysCommonService;
import com.rcszh.cache.BaseDataTranslateUtils;
import com.rcszh.common.constant.Constants;
import com.rcszh.common.util.PageUtil;
import com.rcszh.worktime.domain.PmsPublicUser;
import com.rcszh.worktime.domain.PmsPublicUserLine;
import com.rcszh.worktime.domain.dto.PmsPublicUserDto;
import com.rcszh.worktime.mapper.PmsPublicUserMapper;
import com.rcszh.worktime.service.IPmsPublicUserLineService;
import com.rcszh.worktime.service.IPmsPublicUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
public class PmsPublicUserServiceImpl extends ServiceImpl<PmsPublicUserMapper, PmsPublicUser> implements IPmsPublicUserService {

    @Autowired
    private PmsPublicUserMapper pmsPublicUserMapper;

    @Autowired
    private IPmsPublicUserLineService pmsPublicUserLineService;

    @Autowired
    private BaseDataTranslateUtils baseDataTranslateUtils;

    @Autowired
    private ISysCommonService sysCommonService;

    @Override
    public TableDataInfo find(PmsPublicUserDto publicUserDto) {
        LambdaQueryWrapper<PmsPublicUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PmsPublicUser::getDeleted, Constants.N);
        if (StrUtil.isNotBlank(publicUserDto.getUserName())) {
            List<Long> userIds = sysCommonService.findUserIdsByUserName(publicUserDto.getUserName());
            if (CollUtil.isEmpty(userIds)) {
                userIds.add(-1L);
            }
            queryWrapper.in(PmsPublicUser::getUserId, userIds);
        }
        PageUtils.startPage();
        List<PmsPublicUser> pmsPublicUsers = pmsPublicUserMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(pmsPublicUsers)) {
            return PageUtil.getDataTable(pmsPublicUsers);
        }
        return PageUtil.getDataTable(pmsPublicUsers, baseDataTranslateUtils.baseDataTranslate(pmsPublicUsers));
    }

    @Override
    public List<SysUser> selectUnallocatedList(SysUser SysUser) {
        List<SysUser> sysUsers = pmsPublicUserMapper.selectUnallocatedList(SysUser);
        baseDataTranslateUtils.baseDataTranslate(sysUsers);
        return sysUsers;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean addPublicUser(PmsPublicUser publicUserDto) {
        LambdaQueryWrapper<PmsPublicUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PmsPublicUser::getUserId, publicUserDto.getUserId());
        PmsPublicUser pmsPublicUser = this.getOne(queryWrapper, false);
        if (Objects.isNull(pmsPublicUser)) {
            pmsPublicUser = new PmsPublicUser();
            pmsPublicUser.setUserId(publicUserDto.getUserId());
        }
        pmsPublicUser.setDeleted(Constants.N);
        pmsPublicUser.fillBaseFields();
        return this.saveOrUpdate(pmsPublicUser);
    }

    @Override
    public List<PmsPublicUserLine> findEffectiveProjects(Date startTime, Date endTime) {
        List<PmsPublicUser> pmsPublicUsers = pmsPublicUserMapper.selectList(new LambdaQueryWrapper<PmsPublicUser>().eq(PmsPublicUser::getDeleted, Constants.N));
        if (CollUtil.isEmpty(pmsPublicUsers)) {
            return new ArrayList<>();
        }
        List<Long> pmsPublicUserIds = pmsPublicUsers.stream().map(PmsPublicUser::getId).toList();
        return pmsPublicUserLineService.findEffectiveProjects(pmsPublicUserIds, startTime, endTime);
    }

    @Override
    public List<PmsPublicUserLine> findEffectiveProjects(Long userId, Date startTime, Date endTime) {
        List<PmsPublicUser> pmsPublicUsers = pmsPublicUserMapper.selectList(new LambdaQueryWrapper<PmsPublicUser>()
                .eq(PmsPublicUser::getUserId, userId)
                .eq(PmsPublicUser::getDeleted, Constants.N));
        if (CollUtil.isEmpty(pmsPublicUsers)) {
            return new ArrayList<>();
        }
        List<Long> pmsPublicUserIds = pmsPublicUsers.stream().map(PmsPublicUser::getId).toList();
        return pmsPublicUserLineService.findEffectiveProjects(pmsPublicUserIds, startTime, endTime);
    }

    @Override
    public PmsPublicUserLine getEffectiveProject(Long userId, Date workTime) {
        PmsPublicUser pmsPublicUser = pmsPublicUserMapper.selectOne(new LambdaQueryWrapper<PmsPublicUser>()
                .eq(PmsPublicUser::getUserId, userId)
                .eq(PmsPublicUser::getDeleted, Constants.N));
        if (pmsPublicUser == null) {
            return null;
        }
        List<Long> pmsPublicUserIds = Collections.singletonList(pmsPublicUser.getId());
        List<PmsPublicUserLine> publicUserLines = pmsPublicUserLineService.findEffectiveProjects(pmsPublicUserIds, workTime, workTime);
        if (CollUtil.isEmpty(publicUserLines)) {
            return null;
        }
        return publicUserLines.get(0);
    }
}
