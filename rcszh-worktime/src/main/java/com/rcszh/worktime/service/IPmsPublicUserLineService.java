package com.rcszh.worktime.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.rcszh.worktime.domain.PmsPublicUserLine;
import com.rcszh.worktime.domain.dto.PmsPublicUserLineDto;

import java.util.Date;
import java.util.List;


public interface IPmsPublicUserLineService extends IService<PmsPublicUserLine> {

    int addPmsPublicUserLine(PmsPublicUserLineDto publicUserDto);

    int updatePmsPublicUserLine(PmsPublicUserLineDto publicUserDto);

    int removePmsPublicUserLine(List<Long> list);

    List<PmsPublicUserLine> findEffectiveProjects(List<Long> pmsPublicUserIds, Date startTime, Date endTime);
}
