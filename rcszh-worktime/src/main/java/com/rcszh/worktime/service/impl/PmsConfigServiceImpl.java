package com.rcszh.worktime.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rcszh.basic.domain.PmsConfig;
import com.rcszh.basic.mapper.PmsConfigMapper;
import com.rcszh.basic.service.IPmsConfigService;
import com.rcszh.cache.service.CacheService;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Service
public class PmsConfigServiceImpl extends ServiceImpl<PmsConfigMapper, PmsConfig> implements IPmsConfigService {
    @Autowired
    private PmsConfigMapper pmConfigMapper;

    @Resource
    private CacheService cacheService;

    private final String CACHE_KEY_PREFIX = "pms_config:";


    private String getCacheKey(String key) {
        return CACHE_KEY_PREFIX + key;
    }

    @Override
    public void clearPmsConfigCache() {
        cacheService.delete(cacheService.keys(CACHE_KEY_PREFIX + "*"));
    }

    /**
     * 通过 config_key 查询对应的配置 value
     * 注意：只有这个方法配置了缓存，其他方法是直接从数据库中获取
     */
    @Override
    public String selectByConfigKey(String configKey) {
        String cacheKey = getCacheKey(configKey);
        String cacheMap = cacheService.get(cacheKey);
        PmsConfig pmsConfig = null;
        if (cacheMap == null) {
            pmsConfig = pmConfigMapper.selectOne(new LambdaQueryWrapper<PmsConfig>().eq(PmsConfig::getConfigKey, configKey));
            cacheService.set(cacheKey, JSONUtil.toJsonStr(pmsConfig));
        } else {
            pmsConfig = JSONUtil.toBean(cacheMap, PmsConfig.class);
        }
        return getConfigValue(pmsConfig);
    }

    /**
     * 通过 config_key 查询对应的配置 对象
     * 注意：只有这个方法配置了缓存，其他方法是直接从数据库中获取
     */
    @Override
    public PmsConfig findByConfigKey(String configKey) {
        String cacheKey = getCacheKey(configKey);
        String cacheMap = cacheService.get(cacheKey);
        PmsConfig pmsConfig = null;
        if (cacheMap == null) {
            pmsConfig = pmConfigMapper.selectOne(new LambdaQueryWrapper<PmsConfig>().eq(PmsConfig::getConfigKey, configKey));
            cacheService.set(cacheKey, JSONUtil.toJsonStr(pmsConfig));
        } else {
            pmsConfig = JSONUtil.toBean(cacheMap, PmsConfig.class);
        }
        return pmsConfig;
    }

    /**
     * 通过 config_key 查询对应的配置 value
     * 注意：只有这个方法配置了缓存，其他方法是直接从数据库中获取
     */
    @Override
    public <T> T fetchByConfigKey(String configKey) {
        String result = this.selectByConfigKey(configKey);
        if (StrUtil.isBlank(result)) {
            return null;
        }
        return checkStringFormat(result);
    }

    public static <T> T checkStringFormat(String inputString) {
        // 判断字符串是否为数组格式
        if (JSONUtil.isTypeJSONArray(inputString)) {
            // 如果是数组，返回对应的JSONArray
            return (T) JSONUtil.parseArray(inputString);  // 返回JSONArray类型
        } else if (JSONUtil.isTypeJSON(inputString)) {
            // 如果是普通值（对象或基础类型），返回解析后的对象
            return (T) JSONUtil.parse(inputString);  // 返回Object类型
        } else {
            // 处理不能识别为JSON格式的情况
            throw new IllegalArgumentException("无法识别的格式");
        }
    }

    /**
     * 数据格式[member, product]
     *
     * @param configKey
     * @return
     */
    @Override
    public List<String> getMultipleValueByConfigKey(String configKey) {
        String value = this.selectByConfigKey(configKey);
        if (StrUtil.isBlank(value)) {
            return new ArrayList<>();
        }
        return Arrays.stream(value.substring(1, value.length() - 1).split(",")).filter(StrUtil::isNotBlank).map(StrUtil::trim).toList();
    }

    @Override
    public Integer getMaxWorkMinute() {
        String configValue = this.selectByConfigKey("maxWorkDuration");
        if (StrUtil.isBlank(configValue)) {
            return null;
        }
        try {
            int maxWorkDuration = Integer.parseInt(configValue);
            if (maxWorkDuration > 0) {
                return maxWorkDuration;
            }
        } catch (Exception e) {
            return null;
        }
        return null;
    }

    /**
     * 根据configKey列表查询
     */
    @Override
    public List<PmsConfig> selectByConfigKeyList(List<String> configKeyList) {
        if (configKeyList == null || configKeyList.isEmpty()) {
            return List.of();
        }
        return pmConfigMapper.selectList(new LambdaQueryWrapper<PmsConfig>()
                .in(PmsConfig::getConfigKey, configKeyList));
    }

    /**
     * 获取 配置value
     */
    private String getConfigValue(PmsConfig pmsConfig) {
        if (Objects.isNull(pmsConfig)) {
            return "";
        }
        if (StrUtil.isEmpty(pmsConfig.getConfigValue())) {
            return pmsConfig.getDefaultValue();
        }
        return pmsConfig.getConfigValue();
    }

    @Override
    public PmsConfig getByKey(String value) {
        return getOne(new LambdaQueryWrapper<PmsConfig>().eq(PmsConfig::getConfigKey, value));
    }
}
