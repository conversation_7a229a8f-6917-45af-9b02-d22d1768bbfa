package com.rcszh.worktime.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rcszh.base.common.exception.ServiceException;
import com.rcszh.worktime.domain.dto.expect_work_time.EveryMonthWorkTime;
import com.rcszh.worktime.domain.dto.expect_work_time.ExpectWorkTimeCompareDto;
import com.rcszh.worktime.domain.dto.expect_work_time.ProjectDiffRangeDto;
import com.rcszh.worktime.domain.expect_work.SysExpectProjectWorkTime;
import com.rcszh.worktime.domain.expect_work.SysExpectUserWorkTime;
import com.rcszh.worktime.mapper.SysExpectProjectWorkTimeMapper;
import com.rcszh.worktime.service.ISysExpectConfigService;
import com.rcszh.worktime.service.ISysExpectProjectWorkTimeService;
import com.rcszh.worktime.service.ISysExpectUserWorkTimeService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class SysExpectProjectWorkTimeServiceImpl extends ServiceImpl<SysExpectProjectWorkTimeMapper, SysExpectProjectWorkTime> implements ISysExpectProjectWorkTimeService {
    @Resource
    private ISysExpectConfigService sysExpectConfigService;
    @Resource
    private ISysExpectUserWorkTimeService sysExpectUserWorkTimeService;


    /**
     * 保存项目工时配置
     */
    @Override
    public void saveExpectProjectWorkTime(SysExpectProjectWorkTime sysExpectProjectWorkTime) {
        // 校验唯一性
        DateTime beginOfMonth = DateUtil.beginOfMonth(sysExpectProjectWorkTime.getExpectDate());
        sysExpectProjectWorkTime.setExpectDate(beginOfMonth);
        SysExpectProjectWorkTime expectProjectWorkTime = getByProjectAndDate(sysExpectProjectWorkTime.getProjectId(), beginOfMonth);
        if (expectProjectWorkTime != null) {
            throw new ServiceException("当前项目已配置对应时间的预估工时");
        }
        sysExpectProjectWorkTime.fillBaseFields();
        save(sysExpectProjectWorkTime);
    }

    /**
     * 删除项目工时配置
     */
    @Override
    public void deleteExpectProjectWorkTimeById(String expectProjectId) {
        // 项目工时配置中不能存在用户工时配置
        List<SysExpectUserWorkTime> userWorkTimeList = sysExpectUserWorkTimeService.getByExpectProject(expectProjectId);
        if (CollUtil.isNotEmpty(userWorkTimeList)) {
            throw new ServiceException("当前配置存在用户预估工时配置，无法删除");
        }
        removeById(expectProjectId);
    }

    /**
     * 根据项目和时间查询项目工时配置
     */
    @Override
    public SysExpectProjectWorkTime getByProjectAndDate(Long projectId, Date date) {
        return getOne(new LambdaQueryWrapper<SysExpectProjectWorkTime>()
                .eq(SysExpectProjectWorkTime::getProjectId, projectId)
                .eq(SysExpectProjectWorkTime::getExpectDate, date)
        );
    }

    /**
     * 更新项目预估工时配置
     */
    @Override
    public void updateExpectProjectWorkTime(SysExpectProjectWorkTime sysExpectProjectWorkTime) {
        // 校验是否有配置用户预估工时
//        List<SysExpectUserWorkTime> userWorkTimeList = sysExpectUserWorkTimeService.getByExpectProject(sysExpectProjectWorkTime.getId());
//        if (CollUtil.isNotEmpty(userWorkTimeList)) {
//            throw new ServiceException("当前配置存在用户预估工时配置，无法更新");
//        }
        sysExpectProjectWorkTime.fillBaseFields();
        updateById(sysExpectProjectWorkTime);
    }

    /**
     * 查询项目预估工时配置
     */
    @Override
    public List<SysExpectProjectWorkTime> getExpectProjectWorkTime(SysExpectProjectWorkTime sysExpectProjectWorkTime) {
        //        if (result.stream().allMatch(i -> i.getExpectWorkTime() == null)){
//            // 所有的都是null，则预估工时值等于配置下所有user配置之和
//            return getBaseMapper().findAllWithUserWorkTime(sysExpectProjectWorkTime);
//        }
        return getBaseMapper().findAll(sysExpectProjectWorkTime);
    }

    /**
     * 根据id查询项目预估工时配置
     */
    @Override
    public SysExpectProjectWorkTime getExpectProjectWorkTimeById(String id) {
        SysExpectProjectWorkTime result = getById(id);
        if (result == null) {
            return null;
        }
        if (result.getExpectWorkTime() == null) {
            // 预估工时值等于配置下所有user配置之和
            Integer sum = sysExpectUserWorkTimeService.getByExpectProject(result.getId())
                    .stream()
                    .reduce(0, (a, b) -> a + b.getExpectWorkTime(), Integer::sum);
            result.setExpectWorkTime(sum);
        }
        return result;
    }

    /**
     * 获取每个月的预估工时
     */
    @Override
    public List<EveryMonthWorkTime> getEveryMonthWorkTime(DateTime start, DateTime end, ExpectWorkTimeCompareDto param) {
        return getBaseMapper().findEveryMonthWorkTime(start, end, param);
    }

    @Override
    public List<ProjectDiffRangeDto> getProjectDiffRange(DateTime start, DateTime end, ExpectWorkTimeCompareDto param) {
        return getBaseMapper().findProjectDiffRange(start, end, param);
    }

    @Override
    public SysExpectProjectWorkTime getExpectProjectWorkTimeByProjectIdAndDate(Long projectId, Date expectDate) {
        return getBaseMapper().selectOne(new LambdaQueryWrapper<SysExpectProjectWorkTime>()
                .eq(SysExpectProjectWorkTime::getProjectId, projectId)
                .eq(SysExpectProjectWorkTime::getExpectDate, expectDate));
    }
}
