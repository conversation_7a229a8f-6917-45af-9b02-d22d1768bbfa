package com.rcszh.worktime.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rcszh.base.common.exception.ServiceException;
import com.rcszh.base.common.utils.DictUtils;
import com.rcszh.common.constant.Constants;
import com.rcszh.worktime.domain.SysWorkLog;
import com.rcszh.worktime.mapper.SysWorkLogMapper;
import com.rcszh.worktime.service.ISysWorkLogService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 工作日志Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-09-11
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class SysWorkLogServiceImpl extends ServiceImpl<SysWorkLogMapper, SysWorkLog> implements ISysWorkLogService
{

    /**
     * 查询工作日志
     * 
     * @param id 工作日志主键
     * @return 工作日志
     */
    @Override
    public SysWorkLog getInfo(Long id)
    {
        SysWorkLog workLog = this.getById(id);
        SysWorkLog parentWorkLog = this.getById(workLog.getParentId());
        if (Objects.nonNull(parentWorkLog)){
            workLog.setParentName(parentWorkLog.getName());
        }
        workLog.setApplicableTypesArray(workLog.getApplicableTypes().split(","));
        String applicableTypes = DictUtils.getDictLabel("applicable_types", workLog.getApplicableTypes(), ",");
        workLog.setApplicableTypesName(applicableTypes);
        return workLog;
    }

    @Override
    public List<Tree<Long>> treeNodes(String isFullTime) {
        LambdaQueryWrapper<SysWorkLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysWorkLog::getDelFlag, "0");
        wrapper.eq(SysWorkLog::getStatus, "0");
        wrapper.like(StrUtil.isNotBlank(isFullTime),SysWorkLog::getApplicableTypes,isFullTime);
        List<SysWorkLog> datas = this.list(wrapper);
        if (CollUtil.isEmpty(datas)){
            return Collections.emptyList();
        }
        return buildCategoryTreeSelect(datas);
    }

    /**
     * 构造tree
     * @param sysWorkLogs
     * @return
     */
    public List<Tree<Long>> buildCategoryTreeSelect(List<SysWorkLog> sysWorkLogs) {
        List<TreeNode<Long>> list = sysWorkLogs.stream().map(item -> {
            TreeNode<Long> node = new TreeNode<>();
            node.setId(item.getId());
            node.setParentId(item.getParentId());
            node.setName(item.getName());
            Map<String,Object> extra = new HashMap<>(2);
            extra.put("code", item.getCode());
            extra.put("obj", item);
            node.setExtra(extra);
            return node;
        }).collect(Collectors.toList());
        return TreeUtil.build(list, 0L);
    }

    /**
     * 新增工作日志
     * 
     * @param sysWorkLog 工作日志
     * @return 结果
     */
    @Override
    public void saveSysWorkLog(SysWorkLog sysWorkLog)
    {
        //验证编码是否存在
        SysWorkLog workLog = this.getByCode(sysWorkLog.getCode());
        if (Objects.nonNull(workLog)){
            throw new ServiceException("该编码已存在");
        }
        String ancestors = "0";
        Integer level = 0;
        Long parentId = 0L;
        if (Objects.nonNull(sysWorkLog.getParentId()) && !Objects.equals(sysWorkLog.getParentId(),0L)){
            SysWorkLog parentWorkLog = this.getById(sysWorkLog.getParentId());
            ancestors = parentWorkLog.getAncestors()+Constants.DELIMITER_ANGLE_ESCAPE+parentWorkLog.getId();
            level = parentWorkLog.getLevel();
        }
        if (Objects.nonNull(sysWorkLog.getParentId())){
            parentId = sysWorkLog.getParentId();
        }
        sysWorkLog.setParentId(parentId);
        sysWorkLog.setLevel(level+1);
        sysWorkLog.setStatus("0");
        sysWorkLog.setDelFlag("0");
        sysWorkLog.setAncestors(ancestors);
        sysWorkLog.setApplicableTypes(StrUtil.join(",", (Object) sysWorkLog.getApplicableTypesArray()));
        sysWorkLog.fillBaseFields();
        this.save(sysWorkLog);
    }

    /**
     * 根据编码获取数据
     * @param code
     * @return
     */
    private SysWorkLog getByCode(String code){
        LambdaQueryWrapper<SysWorkLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysWorkLog::getCode,code);
        wrapper.eq(SysWorkLog::getDelFlag, Constants.STATUS_ENABLED);
        return this.getOne(wrapper);
    }

    /**
     * 修改工作日志
     * 
     * @param sysWorkLog 工作日志
     * @return 结果
     */
    @Override
    public void updateSysWorkLog(SysWorkLog sysWorkLog)
    {
        //验证编码是否存在
        SysWorkLog workLog = this.getByCode(sysWorkLog.getCode());
        if (Objects.nonNull(workLog) && !Objects.equals(workLog.getId(),sysWorkLog.getId())){
            throw new ServiceException("该编码已存在");
        }
        String ancestors = "0";
        Integer level = 0;
        if (Objects.nonNull(sysWorkLog.getParentId()) && !Objects.equals(sysWorkLog.getParentId(),0L)){
            SysWorkLog parentWorkLog = this.getById(sysWorkLog.getParentId());
            ancestors = parentWorkLog.getAncestors()+Constants.DELIMITER_ANGLE_ESCAPE+parentWorkLog.getId();
            level = parentWorkLog.getLevel();
        }
        sysWorkLog.setLevel(level+1);
        sysWorkLog.setStatus("0");
        sysWorkLog.setDelFlag("0");
        sysWorkLog.setAncestors(ancestors);
        sysWorkLog.setApplicableTypes(StrUtil.join(",", (Object) sysWorkLog.getApplicableTypesArray()));
        sysWorkLog.fillBaseFields();
        this.saveOrUpdate(sysWorkLog);
    }

    /**
     * 删除工作日志信息
     * 
     * @param id 工作日志主键
     * @return 结果
     */
    @Override
    public void deleteSysWorkLogById(Long id)
    {
        SysWorkLog workLog = this.getById(id);
        //查询判断有没有子级
        List<SysWorkLog> childList = getChildList(workLog.getId());
        if (CollUtil.isNotEmpty(childList)){
            throw new ServiceException("该节点存在子节点,无法删除");
        }
        workLog.setStatus("1");
        workLog.setDelFlag("1");
        workLog.fillBaseFields();
        this.saveOrUpdate(workLog);
    }

    /**
     * 根据父id查询是否有子级
     * @param parentId
     * @return
     */
    private List<SysWorkLog> getChildList(Long parentId){
        LambdaQueryWrapper<SysWorkLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysWorkLog::getParentId,parentId);
        wrapper.eq(SysWorkLog::getStatus,"0");
        wrapper.eq(SysWorkLog::getDelFlag,"0");
        return this.list(wrapper);
    }
}
