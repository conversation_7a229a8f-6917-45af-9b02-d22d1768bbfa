package com.rcszh.worktime.service;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.extension.service.IService;
import com.rcszh.worktime.domain.dto.expect_work_time.EveryMonthWorkTime;
import com.rcszh.worktime.domain.dto.expect_work_time.ExpectWorkTimeCompareDto;
import com.rcszh.worktime.domain.dto.expect_work_time.ProjectDiffRangeDto;
import com.rcszh.worktime.domain.expect_work.SysExpectProjectWorkTime;


import java.util.Date;
import java.util.List;

public interface ISysExpectProjectWorkTimeService extends IService<SysExpectProjectWorkTime> {
    // 保存项目工时配置
    void saveExpectProjectWorkTime(SysExpectProjectWorkTime sysExpectProjectWorkTime);

    // 根据id删除项目工时配置
    void deleteExpectProjectWorkTimeById(String id);

    // 根据项目和时间查询项目工时配置
    SysExpectProjectWorkTime getByProjectAndDate(Long projectId, Date date);

    // 更新项目预估工时配置
    void updateExpectProjectWorkTime(SysExpectProjectWorkTime sysExpectProjectWorkTime);

    // 分页查询项目预估工时配置
    List<SysExpectProjectWorkTime> getExpectProjectWorkTime(SysExpectProjectWorkTime sysExpectProjectWorkTime);

    // 根据id查询项目预估工时配置
    SysExpectProjectWorkTime getExpectProjectWorkTimeById(String id);

    // 获取每个月的预估工时
    List<EveryMonthWorkTime> getEveryMonthWorkTime(DateTime start, DateTime end, ExpectWorkTimeCompareDto param);

    // 获取指定差异范围内的预估工时
    List<ProjectDiffRangeDto> getProjectDiffRange(DateTime start, DateTime end, ExpectWorkTimeCompareDto param);

    // 根据项目id和预期时间查询项目工时配置
    SysExpectProjectWorkTime getExpectProjectWorkTimeByProjectIdAndDate(Long projectId, Date expectDate);
}
