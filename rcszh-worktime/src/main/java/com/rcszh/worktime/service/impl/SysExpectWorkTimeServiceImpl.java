package com.rcszh.worktime.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.rcszh.basic.domain.PmsConfig;
import com.rcszh.basic.enums.SysExpectConfigEnum;
import com.rcszh.basic.service.IPmsConfigService;
import com.rcszh.cache.BaseDataTranslateUtils;
import com.rcszh.worktime.domain.dto.expect_work_time.*;
import com.rcszh.worktime.domain.expect_work.SysExpectProjectWorkTime;
import com.rcszh.worktime.domain.expect_work.SysExpectUserWorkTime;
import com.rcszh.worktime.domain.vo.ExpectWorkTimeComparePieVo;
import com.rcszh.worktime.domain.vo.ExpectWorkTimeCompareVo;
import com.rcszh.worktime.service.ISysExpectProjectWorkTimeService;
import com.rcszh.worktime.service.ISysExpectUserWorkTimeService;
import com.rcszh.worktime.service.ISysExpectWorkTimeService;
import com.rcszh.worktime.service.ISysWorkRecordService;
import jakarta.annotation.Resource;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SysExpectWorkTimeServiceImpl implements ISysExpectWorkTimeService {
    @Resource
    private IPmsConfigService pmsConfigService;
    @Resource
    private ISysExpectUserWorkTimeService sysExpectUserWorkTimeService;
    @Resource
    private ISysExpectProjectWorkTimeService sysExpectProjectWorkTimeService;
    @Resource
    private ISysWorkRecordService sysWorkRecordService;
    @Resource
    private BaseDataTranslateUtils translateUtils;


    @Override
    public void saveExpectProjectWorkTime(SysExpectProjectWorkTimeDto dto) {
        PmsConfig config = pmsConfigService.getByKey(SysExpectConfigEnum.EXPECT_TIME_UNIT.getValue());
        SysExpectProjectWorkTime sysExpectProjectWorkTime = dto.toEntity(config);
        sysExpectProjectWorkTimeService.saveExpectProjectWorkTime(sysExpectProjectWorkTime);
    }

    @Override
    public void deleteExpectProjectWorkTimeById(String id) {
        sysExpectProjectWorkTimeService.deleteExpectProjectWorkTimeById(id);
    }

    @Override
    public void updateExpectProjectWorkTime(SysExpectProjectWorkTimeDto sysExpectProjectWorkTime) {
        PmsConfig config = pmsConfigService.getByKey(SysExpectConfigEnum.EXPECT_TIME_UNIT.getValue());
        sysExpectProjectWorkTimeService.updateExpectProjectWorkTime(sysExpectProjectWorkTime.toEntity(config));
    }

    @Override
    public List<SysExpectProjectWorkTimeDto> getExpectProjectWorkTime(SysExpectProjectWorkTime sysExpectProjectWorkTime) {
        PmsConfig unitConfig = getConfigByKey(SysExpectConfigEnum.EXPECT_TIME_UNIT.getValue());
        if (sysExpectProjectWorkTime.getExpectDate() != null) {
            sysExpectProjectWorkTime.setExpectDate(DateUtil.beginOfMonth(sysExpectProjectWorkTime.getExpectDate()));
        }
        List<SysExpectProjectWorkTime> list = sysExpectProjectWorkTimeService.getExpectProjectWorkTime(sysExpectProjectWorkTime);
        return list.stream().map(i -> i.toDto(unitConfig)).collect(Collectors.toList());
    }

    @Override
    public SysExpectProjectWorkTimeDto getExpectProjectWorkTimeById(String id) {
        // 查询配置
//        SysExpectConfig unitConfig = sysExpectConfigService.getByKey(SysExpectConfigEnum.EXPECT_TIME_UNIT.getValue());
        PmsConfig unitConfig = pmsConfigService.getByKey(SysExpectConfigEnum.EXPECT_TIME_UNIT.getValue());
        SysExpectProjectWorkTime config = sysExpectProjectWorkTimeService.getExpectProjectWorkTimeById(id);
        return config.toDto(unitConfig);
    }


    @Override
    public SysExpectProjectWorkTime getExpectProjectWorkTimeByProjectIdAndDate(Long projectId, Date expectDate) {
        return sysExpectProjectWorkTimeService.getExpectProjectWorkTimeByProjectIdAndDate(projectId, expectDate);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveExpectUserWorkTime(SysExpectUserWorkTimeDto dto) {
//        SysExpectConfig config = sysExpectConfigService.getByKey(SysExpectConfigEnum.EXPECT_TIME_UNIT.getValue());
        PmsConfig config = pmsConfigService.getByKey(SysExpectConfigEnum.EXPECT_TIME_UNIT.getValue());
        SysExpectUserWorkTime entity = dto.toEntity(config);
        String expectProjectId = entity.getExpectProjectId();
        SysExpectProjectWorkTime projectWorkTime = sysExpectProjectWorkTimeService.getById(expectProjectId);
        projectWorkTime.setExpectWorkTime(entity.getExpectWorkTime());
        sysExpectProjectWorkTimeService.updateExpectProjectWorkTime(projectWorkTime);
        sysExpectUserWorkTimeService.saveExpectUserWorkTime(entity);
    }

    @Override
    public List<SysExpectUserWorkTimeDto> getExpectUserWorkTime(SysExpectUserWorkTimeDto sysExpectUserWorkTime) {
        PmsConfig unitConfig = pmsConfigService.getByKey(SysExpectConfigEnum.EXPECT_TIME_UNIT.getValue());
        List<SysExpectUserWorkTime> list = sysExpectUserWorkTimeService.getExpectUserWorkTime(sysExpectUserWorkTime);
        List<SysExpectUserWorkTimeDto> result = list.stream().map(i -> i.toDto(unitConfig)).collect(Collectors.toList());
        translateUtils.baseDataTranslate(result);
        return result;
    }

    @Override
    public SysExpectUserWorkTimeDto getExpectUserWorkTime(String id) {
        // 查询配置
        PmsConfig unitConfig = pmsConfigService.getByKey(SysExpectConfigEnum.EXPECT_TIME_UNIT.getValue());
        SysExpectUserWorkTime config = sysExpectUserWorkTimeService.getExpectUserWorkTime(id);
        SysExpectUserWorkTimeDto dto = config.toDto(unitConfig);
        translateUtils.baseDataTranslate(dto);
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteExpectUserWorkTimeById(String id) {
        SysExpectUserWorkTime userWorkTime = sysExpectUserWorkTimeService.getById(id);
        sysExpectUserWorkTimeService.removeById(id);
        updateProjectSumWorkTime(userWorkTime.getExpectProjectId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatchExpectUserWorkTime(List<SysExpectUserWorkTimeDto> list) {
        if (list.isEmpty()) {
            return;
        }
        PmsConfig unitConfig = pmsConfigService.getByKey(SysExpectConfigEnum.EXPECT_TIME_UNIT.getValue());
        List<SysExpectUserWorkTime> collect = list.stream().map(i -> i.toEntity(unitConfig)).collect(Collectors.toList());
        Set<String> set = collect.stream().map(SysExpectUserWorkTime::getExpectProjectId).collect(Collectors.toSet());
        if (set.size() > 1) {
            throw new RuntimeException("批量插入用户预估工时配置时，项目id必须相同");
        }
        String expectProjectId = set.stream().toList().getFirst();
        sysExpectUserWorkTimeService.updateBatchById(collect);
        updateProjectSumWorkTime(expectProjectId);
    }

    @Override
    public PmsConfig getConfigByKey(String key) {
        return pmsConfigService.getByKey(key);
    }


    @Override
    public ExpectWorkTimeCompareVo getWorkTimeCompareBar(ExpectWorkTimeCompareDto param) {
        Date year = param.getTime();
        DateTime start = DateUtil.beginOfYear(year);
        DateTime end = DateUtil.endOfYear(year);
        // 获取实际工时
        List<EveryMonthWorkTime> workResult = sysWorkRecordService.getEveryMonthWorkTime(start, end, param);
        List<EveryMonthWorkTime> expectResult = sysExpectProjectWorkTimeService.getEveryMonthWorkTime(start, end, param);
        Map<String, BigDecimal> realWorkMap = workResult.stream().collect(Collectors.toMap(EveryMonthWorkTime::getMonth, i -> i.getWorkTime() == null ? BigDecimal.ZERO : i.getWorkTime()));
        Map<String, BigDecimal> expectWorkMap = expectResult.stream().collect(Collectors.toMap(EveryMonthWorkTime::getMonth, i -> i.getWorkTime() == null ? BigDecimal.ZERO : i.getWorkTime()));
        ExpectWorkTimeCompareVo result = new ExpectWorkTimeCompareVo();
        List<Integer> sortMonth = new ArrayList<>();

        List<BigDecimal> expectWorkTimes = new ArrayList<>();
        List<BigDecimal> actualWorkTimes = new ArrayList<>();
        // 时间单位
        String unit = param.getUnit();
        for (int m = 1; m <= 12; m++) {
            sortMonth.add(m);
            actualWorkTimes.add(covertWorkTimeToWeb(unit, realWorkMap.getOrDefault(Integer.toString(m), BigDecimal.ZERO)));
            expectWorkTimes.add(covertWorkTimeToWeb(unit, expectWorkMap.getOrDefault(Integer.toString(m), BigDecimal.ZERO)));
        }
        result.setMonths(sortMonth);
        result.setExpectWorkTimes(expectWorkTimes);
        result.setActualWorkTimes(actualWorkTimes);
        return result;
    }

    @Override
    public ExpectWorkTimeCompareVo getWorkTimeCompareLine(ExpectWorkTimeCompareDto param) {
        ExpectWorkTimeCompareVo workTimeCompareBar = getWorkTimeCompareBar(param);
        List<BigDecimal> compareWorkTimes = new ArrayList<>();
        for (int i = 0; i < workTimeCompareBar.getMonths().size(); i++) {
            BigDecimal compareWorkTime = getBigDecimal(workTimeCompareBar, i);
            compareWorkTimes.add(compareWorkTime);
        }
        workTimeCompareBar.setCompareWorkTimes(compareWorkTimes);
        return workTimeCompareBar;
    }

    @NotNull
    private static BigDecimal getBigDecimal(ExpectWorkTimeCompareVo workTimeCompareBar, int i) {
        BigDecimal expectWorkTime = workTimeCompareBar.getExpectWorkTimes().get(i);
        BigDecimal actualWorkTime = workTimeCompareBar.getActualWorkTimes().get(i);
        BigDecimal compareWorkTime;
        if (expectWorkTime.compareTo(BigDecimal.ZERO) == 0) {
//            if (actualWorkTime.compareTo(BigDecimal.ZERO) == 0){
//                return BigDecimal.ZERO;
//            }else {
//                return BigDecimal.valueOf(1);
//            }
//            BigDecimal newExpectWorkTime = expectWorkTime.add(BigDecimal.ONE);
//            BigDecimal newActualWorkTime = actualWorkTime.add(BigDecimal.ONE);
//            compareWorkTime = newActualWorkTime.subtract(newExpectWorkTime).divide(newExpectWorkTime, 2, RoundingMode.HALF_DOWN);
            return BigDecimal.ZERO;
        } else {
            compareWorkTime = actualWorkTime.subtract(expectWorkTime).divide(expectWorkTime, 2, RoundingMode.HALF_DOWN);
        }
        return compareWorkTime;
    }

    @Override
    public List<ExpectWorkTimeComparePieVo> getProjectDiffPie(ExpectWorkTimeCompareDto param) {
        PmsConfig rangConfig = pmsConfigService.getByKey(SysExpectConfigEnum.DIFF_RANGE.getValue());
        List<SysExpectConfigEnum.DiffRange> rangeConfig = JSONUtil.parseArray(rangConfig.getConfigValue()).toList(SysExpectConfigEnum.DiffRange.class);
        Date time = param.getTime();
        DateTime start = DateUtil.beginOfMonth(time);
        DateTime end = DateUtil.endOfMonth(time);
        List<ProjectDiffRangeDto> diffRangeList = sysExpectProjectWorkTimeService.getProjectDiffRange(start, end, param)
                .stream()
                .peek(i -> i.setDiffRange(i.getDiffRange() == null ? BigDecimal.ZERO : i.getDiffRange()))
                .sorted(Comparator.comparing(ProjectDiffRangeDto::getDiffRange))
                .toList();
        List<ExpectWorkTimeComparePieVo> result = new ArrayList<>();
        for (SysExpectConfigEnum.DiffRange diffRange : rangeConfig) {
            ExpectWorkTimeComparePieVo vo = new ExpectWorkTimeComparePieVo();
            vo.setName(diffRange.getRangeTitle());
            vo.setMax(diffRange.getMax());
            vo.setMin(diffRange.getMin());
            if (!diffRangeList.isEmpty()) {
                BigDecimal max = diffRange.getMax();
                BigDecimal min = diffRange.getMin();
                if (max == null) {
                    max = diffRangeList.getLast().getDiffRange().add(BigDecimal.ONE);
                } else if (min == null) {
                    min = diffRangeList.getFirst().getDiffRange().subtract(BigDecimal.ONE);
                }
                final BigDecimal finalMax = max;
                final BigDecimal finalMin = min;
                List<ProjectDiffRangeDto> list = diffRangeList.stream().filter(i -> i.getDiffRange().compareTo(finalMin) > 0 && i.getDiffRange().compareTo(finalMax) <= 0).toList();
                vo.setProjectList(list.stream().map(i -> {
                    ExpectWorkTimeComparePieVo.ProjectDiff diff = new ExpectWorkTimeComparePieVo.ProjectDiff();
                    diff.setProjectName(i.getProjectName());
                    diff.setProjectCompareValue(i.getDiffRange());
                    return diff;
                }).toList());
                vo.setValue(list.size());
            } else {
                vo.setValue(0);
                vo.setProjectList(new ArrayList<>());
            }
            result.add(vo);
        }
        return result;
    }

    public BigDecimal covertWorkTimeToWeb(String unit, BigDecimal expectWorkTime) {
        if (SysExpectConfigEnum.SysExpectConfigTimeUnitEnum.HOUR.getValue().equals(unit)) {
            // 小时
            return expectWorkTime.divide(BigDecimal.valueOf(60), 1, RoundingMode.HALF_DOWN);
        } else if (SysExpectConfigEnum.SysExpectConfigTimeUnitEnum.MINUTE.getValue().equals(unit)) {
            // 分钟
            return expectWorkTime;
        } else {
            return expectWorkTime;
        }
    }

    /**
     * 更新用户预估工时要同步更新项目预估工时
     */
    private void updateProjectSumWorkTime(String expectProjectId) {
        List<SysExpectUserWorkTime> userWorkTimeList = sysExpectUserWorkTimeService.getByExpectProject(expectProjectId);
        SysExpectProjectWorkTime expectProjectWorkTime = sysExpectProjectWorkTimeService.getById(expectProjectId);
        Integer sum = userWorkTimeList.stream().map(SysExpectUserWorkTime::getExpectWorkTime).filter(Objects::nonNull).reduce(0, Integer::sum);
        expectProjectWorkTime.setExpectWorkTime(sum);
        sysExpectProjectWorkTimeService.updateExpectProjectWorkTime(expectProjectWorkTime);
    }


}
