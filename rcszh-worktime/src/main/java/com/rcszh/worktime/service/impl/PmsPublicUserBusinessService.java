package com.rcszh.worktime.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rcszh.base.common.core.page.TableDataInfo;
import com.rcszh.common.util.PageUtil;
import com.rcszh.project.domain.PmsProject;
import com.rcszh.project.service.IPmsProjectService;
import com.rcszh.worktime.domain.PmsPublicUserLine;
import com.rcszh.worktime.domain.PmsPublicUserProject;
import com.rcszh.worktime.domain.vo.PmsPublicUserLineVo;
import com.rcszh.worktime.service.IPmsPublicUserBusinessService;
import com.rcszh.worktime.service.IPmsPublicUserLineService;
import com.rcszh.worktime.service.IPmsPublicUserProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class PmsPublicUserBusinessService implements IPmsPublicUserBusinessService {

    @Autowired
    private IPmsPublicUserLineService pmsPublicUserLineService;

    @Autowired
    private IPmsPublicUserProjectService pmsPublicUserProjectService;

    @Autowired
    private IPmsProjectService pmsProjectService;

    @Override
    public TableDataInfo findPmsPublicUserLines(PmsPublicUserLine pmsPublicUserLine) {
        List<PmsPublicUserLine> list = pmsPublicUserLineService.list(new LambdaQueryWrapper<PmsPublicUserLine>()
                .eq(PmsPublicUserLine::getPublicUserId, pmsPublicUserLine.getPublicUserId())
                .orderByDesc(PmsPublicUserLine::getStartTime)
        );
        if (CollUtil.isEmpty(list)) {
            return PageUtil.getDataTable(list);
        }
        List<PmsPublicUserLineVo> result = new ArrayList<>();

        List<PmsPublicUserProject> publicUserProjects = pmsPublicUserProjectService.list(new LambdaQueryWrapper<PmsPublicUserProject>()
                .in(PmsPublicUserProject::getPublicUserLineId, list.stream().map(PmsPublicUserLine::getId).toList())
        );
        Map<Long, PmsProject> projectMap = new HashMap<>();
        if (CollUtil.isNotEmpty(publicUserProjects)) {
            Set<Long> projectIdSet = publicUserProjects.stream().map(PmsPublicUserProject::getProjectId).collect(Collectors.toSet());
            List<PmsProject> pmsProjects = pmsProjectService.listByIds(projectIdSet);
            if (CollUtil.isNotEmpty(pmsProjects)) {
                projectMap = pmsProjects.stream().collect(Collectors.toMap(PmsProject::getId, p -> p, (a, b) -> a));
            }
        }

        Map<Long, List<PmsPublicUserProject>> publicUserProjectGroup = publicUserProjects.stream().collect(Collectors.groupingBy(PmsPublicUserProject::getPublicUserLineId));
        for (PmsPublicUserLine publicUserLine : list) {
            List<PmsPublicUserProject> userProjects = publicUserProjectGroup.get(publicUserLine.getId());
            PmsPublicUserLineVo vo = new PmsPublicUserLineVo();
            BeanUtil.copyProperties(publicUserLine, vo);
            List<PmsProject> pmsProjects = new ArrayList<>();
            if (CollUtil.isNotEmpty(userProjects)) {
                for (PmsPublicUserProject userProject : userProjects) {
                    PmsProject pmsProject = projectMap.get(userProject.getProjectId());
                    if (Objects.nonNull(pmsProject)) {
                        pmsProjects.add(pmsProject);
                    }
                }
            }
            vo.setProjects(pmsProjects);
            result.add(vo);
        }

        return PageUtil.getDataTable(list, result);
    }
}
