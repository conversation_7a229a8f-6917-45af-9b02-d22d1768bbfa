package com.rcszh.worktime.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rcszh.worktime.domain.SysWorkHourIgnoreUsers;
import com.rcszh.worktime.mapper.SysWorkHourIgnoreUsersMapper;
import com.rcszh.worktime.service.ISysWorkHourIgnoreUsersService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class SysWorkHourIgnoreUsersServiceImpl extends ServiceImpl<SysWorkHourIgnoreUsersMapper, SysWorkHourIgnoreUsers> implements ISysWorkHourIgnoreUsersService {

    @Autowired
    private SysWorkHourIgnoreUsersMapper sysWorkHourIgnoreUsersMapper;

    @Override
    public List<SysWorkHourIgnoreUsers> queryPage(SysWorkHourIgnoreUsers sysWorkHourIgnoreUsers) {
        return sysWorkHourIgnoreUsersMapper.queryPage(sysWorkHourIgnoreUsers);
    }

    @Override
    public void batchSave(List<Long> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return;
        }
        LambdaQueryWrapper<SysWorkHourIgnoreUsers> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SysWorkHourIgnoreUsers::getUserId, userIds);
        List<SysWorkHourIgnoreUsers> list = this.list(queryWrapper);
        Set<Long> collect = list.stream().map(SysWorkHourIgnoreUsers::getUserId).collect(Collectors.toSet());
        //补充数据
        for (Long userId : userIds) {
            if (!collect.contains(userId)) {
                SysWorkHourIgnoreUsers ignoreUsers = new SysWorkHourIgnoreUsers();
                ignoreUsers.setUserId(userId);
                ignoreUsers.setCreateBy(userId);
                ignoreUsers.setCreateTime(new Date());
                this.save(ignoreUsers);
            }
        }
    }

    @Override
    public Set<Long> findAllUserIds() {
        List<SysWorkHourIgnoreUsers> list = this.list();
        return list.stream().map(SysWorkHourIgnoreUsers::getUserId).collect(Collectors.toSet());
    }
}
