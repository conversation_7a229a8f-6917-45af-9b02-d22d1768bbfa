package com.rcszh.worktime.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rcszh.base.common.exception.ServiceException;
import com.rcszh.basic.enums.SysExpectConfigEnum;
import com.rcszh.worktime.domain.dto.expect_work_time.EveryMonthWorkTime;
import com.rcszh.worktime.domain.dto.expect_work_time.ExpectWorkTimeCompareDto;
import com.rcszh.worktime.domain.dto.expect_work_time.SysExpectUserWorkTimeDto;
import com.rcszh.worktime.domain.expect_work.SysExpectUserWorkTime;
import com.rcszh.worktime.domain.vo.ExpectWorkTimeComparePieVo;
import com.rcszh.worktime.mapper.SysExpectUserWorkTimeMapper;
import com.rcszh.worktime.service.ISysExpectUserWorkTimeService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SysExpectUserWorkTimeServiceImpl extends ServiceImpl<SysExpectUserWorkTimeMapper, SysExpectUserWorkTime> implements ISysExpectUserWorkTimeService {
    /**
     * 根据项目预估工时查询用户预估工时
     */
    @Override
    public List<SysExpectUserWorkTime> getByExpectProject(String expectProjectId) {
        return list(new LambdaQueryWrapper<SysExpectUserWorkTime>().eq(SysExpectUserWorkTime::getExpectProjectId, expectProjectId));
    }

    /**
     * 保存用户预估工时
     */
    @Override
    public void saveExpectUserWorkTime(SysExpectUserWorkTime sysExpectUserWorkTime) {
        SysExpectUserWorkTime oldInfo = getOne(new LambdaQueryWrapper<SysExpectUserWorkTime>()
                .eq(SysExpectUserWorkTime::getExpectProjectId, sysExpectUserWorkTime.getExpectProjectId())
                .eq(SysExpectUserWorkTime::getUserId, sysExpectUserWorkTime.getUserId())
        );
        if (oldInfo != null) {
            throw new ServiceException("该项目已存在该用户的预估工时配置");
        }
        sysExpectUserWorkTime.fillBaseFields();
        save(sysExpectUserWorkTime);
    }

    /**
     * 查询用户预估工时
     */
    @Override
    public List<SysExpectUserWorkTime> getExpectUserWorkTime(SysExpectUserWorkTimeDto sysExpectUserWorkTime) {
        return list(new LambdaQueryWrapper<SysExpectUserWorkTime>()
                .eq(StrUtil.isNotBlank(sysExpectUserWorkTime.getExpectProjectId()), SysExpectUserWorkTime::getExpectProjectId, sysExpectUserWorkTime.getExpectProjectId())
                .eq(sysExpectUserWorkTime.getUserId() != null, SysExpectUserWorkTime::getUserId, sysExpectUserWorkTime.getUserId())
        );
    }

    @Override
    public SysExpectUserWorkTime getExpectUserWorkTime(String id) {
        return getById(id);
    }

    @Override
    public List<EveryMonthWorkTime> getEveryMonthWorkTime(DateTime start, DateTime end, ExpectWorkTimeCompareDto param) {
        return getBaseMapper().findEveryMonthWorkTime(start, end, param);
    }

    @Override
    public List<ExpectWorkTimeComparePieVo> getUserDiffRange(List<SysExpectConfigEnum.DiffRange> rangeConfig, ExpectWorkTimeCompareDto param) {
        return getBaseMapper().findUserDiffRange(rangeConfig, param);
    }
}
