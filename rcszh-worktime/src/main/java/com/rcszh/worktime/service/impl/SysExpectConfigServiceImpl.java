package com.rcszh.worktime.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.rcszh.base.common.exception.ServiceException;
import com.rcszh.worktime.domain.expect_work.SysExpectConfig;
import com.rcszh.worktime.mapper.SysExpectConfigMapper;
import com.rcszh.worktime.service.ISysExpectConfigService;
import org.springframework.stereotype.Service;

@Service
public class SysExpectConfigServiceImpl extends ServiceImpl<SysExpectConfigMapper, SysExpectConfig> implements ISysExpectConfigService {
    /**
     * 根据key查询配置
     */
    @Override
    public SysExpectConfig getByKey(String key) {
        SysExpectConfig config = getOne(new LambdaQueryWrapper<SysExpectConfig>().eq(SysExpectConfig::getConfigKey, key));
        if (config == null) {
            throw new ServiceException(key + " 配置不存在");
        }
        return config;
    }
}
