package com.rcszh.worktime.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rcszh.base.common.core.domain.entity.SysUser;
import com.rcszh.base.common.core.page.TableDataInfo;
import com.rcszh.basic.service.ISysCommonService;
import com.rcszh.common.util.PageUtil;
import com.rcszh.worktime.domain.PmsWorkRecordReset;
import com.rcszh.worktime.domain.SysWorkEntry;
import com.rcszh.worktime.domain.SysWorkRecord;
import com.rcszh.worktime.enums.WorkRecordStatus;
import com.rcszh.worktime.mapper.PmsWorkRecordResetMapper;
import com.rcszh.worktime.service.IPmsWorkRecordResetService;
import com.rcszh.worktime.service.ISysWorkEntryService;
import com.rcszh.worktime.service.ISysWorkRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class PmsWorkRecordResetServiceImpl extends ServiceImpl<PmsWorkRecordResetMapper, PmsWorkRecordReset> implements IPmsWorkRecordResetService {

    @Autowired
    private PmsWorkRecordResetMapper pmsWorkRecordResetMapper;

    @Autowired
    private ISysCommonService sysCommonService;

    @Autowired
    private ISysWorkRecordService sysWorkRecordService;

    @Autowired
    private ISysWorkEntryService sysWorkEntryService;

    @Override
    public TableDataInfo findPmsWorkRecordResets(PmsWorkRecordReset pmsWorkRecordReset) {
        List<PmsWorkRecordReset> pmsWorkRecordResets = pmsWorkRecordResetMapper.selectList(new LambdaQueryWrapper<PmsWorkRecordReset>()
                .eq(Objects.nonNull(pmsWorkRecordReset.getWorkTime()), PmsWorkRecordReset::getWorkTime, pmsWorkRecordReset.getWorkTime())
                .eq(Objects.nonNull(pmsWorkRecordReset.getUserId()), PmsWorkRecordReset::getUserId, pmsWorkRecordReset.getUserId())
                .orderByDesc(PmsWorkRecordReset::getId)
        );
        if (CollUtil.isEmpty(pmsWorkRecordResets)) {
            return PageUtil.getDataTable(pmsWorkRecordResets);
        }
        Set<Long> userIdSet = new HashSet<>();
        for (PmsWorkRecordReset workRecordReset : pmsWorkRecordResets) {
            userIdSet.add(workRecordReset.getUserId());
            userIdSet.add(workRecordReset.getOperatorId());
        }
        List<SysUser> sysUsers = sysCommonService.findUserByIds(new ArrayList<>(userIdSet));
        Map<Long, SysUser> userMap = sysUsers.stream().collect(Collectors.toMap(SysUser::getUserId, t -> t, (k1, k2) -> k1));

        for (PmsWorkRecordReset workRecordReset : pmsWorkRecordResets) {
            SysUser operator = userMap.get(workRecordReset.getOperatorId());
            if (Objects.nonNull(operator)) {
                workRecordReset.setOperatorName(operator.getNickName());
            }
            SysUser user = userMap.get(workRecordReset.getUserId());
            if (Objects.nonNull(user)) {
                workRecordReset.setUserName(user.getNickName());
            }
        }

        return PageUtil.getDataTable(pmsWorkRecordResets);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int addPmsWorkRecordReset(PmsWorkRecordReset pmsWorkRecordReset) {
        LambdaQueryWrapper<SysWorkRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysWorkRecord::getUserId, pmsWorkRecordReset.getUserId());
        queryWrapper.eq(SysWorkRecord::getWorkTime, pmsWorkRecordReset.getWorkTime());
        List<SysWorkRecord> sysWorkRecords = sysWorkRecordService.list(queryWrapper);
        boolean flag = false;
        for (SysWorkRecord sysWorkRecord : sysWorkRecords) {
            if (WorkRecordStatus.pending.getCode().equals(sysWorkRecord.getStatus())) {
                throw new RuntimeException("存在待审核的工时记录，请联系审批人驳回处理");
            }
            if (WorkRecordStatus.audited.getCode().equals(sysWorkRecord.getStatus())) {
                flag = true;
            }
        }
        if (!flag) {
            throw new RuntimeException("不存在已审核的工时记录，无需重置");
        }
        SysWorkRecord updateData = new SysWorkRecord();
        updateData.setStatus(WorkRecordStatus.confirming.getCode());
        updateData.setUpdateTime(new Date());
        updateData.setUpdateBy(String.valueOf(pmsWorkRecordReset.getOperatorId()));
        sysWorkRecordService.update(updateData, queryWrapper);

        SysWorkEntry sysWorkEntry = new SysWorkEntry();
        sysWorkEntry.setUserId(pmsWorkRecordReset.getUserId());
        sysWorkEntry.setWorkTime(pmsWorkRecordReset.getWorkTime());
        sysWorkEntry.fillBaseFields();
        sysWorkEntryService.save(sysWorkEntry);

        return pmsWorkRecordResetMapper.insert(pmsWorkRecordReset);
    }
}
