package com.rcszh.worktime.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.rcszh.base.common.core.domain.entity.SysUser;
import com.rcszh.base.common.core.page.TableDataInfo;
import com.rcszh.worktime.domain.PmsPublicUser;
import com.rcszh.worktime.domain.PmsPublicUserLine;
import com.rcszh.worktime.domain.dto.PmsPublicUserDto;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

public interface IPmsPublicUserService extends IService<PmsPublicUser> {
    TableDataInfo find(PmsPublicUserDto publicUserDto);

    List<SysUser> selectUnallocatedList(SysUser sysUser);

    @Transactional(rollbackFor = Exception.class)
    boolean addPublicUser(PmsPublicUser publicUserDto);

    List<PmsPublicUserLine> findEffectiveProjects(Date startTime, Date endTime);

    List<PmsPublicUserLine> findEffectiveProjects(Long userId, Date startTime, Date endTime);

    PmsPublicUserLine getEffectiveProject(Long userId, Date workTime);

}
