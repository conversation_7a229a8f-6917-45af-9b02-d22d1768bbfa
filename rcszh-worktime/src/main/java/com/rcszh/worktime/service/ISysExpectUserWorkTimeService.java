package com.rcszh.worktime.service;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.extension.service.IService;
import com.rcszh.basic.enums.SysExpectConfigEnum;
import com.rcszh.worktime.domain.dto.expect_work_time.EveryMonthWorkTime;
import com.rcszh.worktime.domain.dto.expect_work_time.ExpectWorkTimeCompareDto;
import com.rcszh.worktime.domain.dto.expect_work_time.SysExpectUserWorkTimeDto;
import com.rcszh.worktime.domain.expect_work.SysExpectUserWorkTime;
import com.rcszh.worktime.domain.vo.ExpectWorkTimeComparePieVo;

import java.util.List;

public interface ISysExpectUserWorkTimeService extends IService<SysExpectUserWorkTime> {
    // 根据项目预估工时查询用户预估工时
    List<SysExpectUserWorkTime> getByExpectProject(String expectProjectId);

    // 创建用户预估工时
    void saveExpectUserWorkTime(SysExpectUserWorkTime sysExpectUserWorkTime);

    // 查询用户预估工时
    List<SysExpectUserWorkTime> getExpectUserWorkTime(SysExpectUserWorkTimeDto sysExpectUserWorkTime);

    // 根据id查询用户预估工时
    SysExpectUserWorkTime getExpectUserWorkTime(String id);

    // 获取每个月的用户预估工时
    List<EveryMonthWorkTime> getEveryMonthWorkTime(DateTime start, DateTime end, ExpectWorkTimeCompareDto param);

    // 获取指定差异范围内的用户预估工时
    List<ExpectWorkTimeComparePieVo> getUserDiffRange(List<SysExpectConfigEnum.DiffRange> rangeConfig, ExpectWorkTimeCompareDto param);
}
