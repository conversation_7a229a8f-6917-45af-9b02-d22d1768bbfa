package com.rcszh.worktime.service;

import cn.hutool.core.lang.tree.Tree;
import com.rcszh.worktime.domain.SysWorkLog;

import java.util.List;

/**
 * 工作日志Service接口
 * 
 * <AUTHOR>
 * @date 2024-09-11
 */
public interface ISysWorkLogService 
{
    /**
     * 查询工作日志
     * 
     * @param id 工作日志主键
     * @return 工作日志
     */
    public SysWorkLog getInfo(Long id);

    /**
     * 获取tree
     *
     * @return
     */
    List<Tree<Long>> treeNodes(String isFullTime);

    /**
     * 新增工作日志
     * 
     * @param sysWorkLog 工作日志
     * @return 结果
     */
    public void saveSysWorkLog(SysWorkLog sysWorkLog);

    /**
     * 修改工作日志
     * 
     * @param sysWorkLog 工作日志
     * @return 结果
     */
    public void updateSysWorkLog(SysWorkLog sysWorkLog);

    /**
     * 删除工作日志信息
     * 
     * @param id 工作日志主键
     * @return 结果
     */
    public void deleteSysWorkLogById(Long id);
}
