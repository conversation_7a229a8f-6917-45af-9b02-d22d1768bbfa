package com.rcszh.worktime.domain.expect_work;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.rcszh.common.domain.BaseDomain;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SysExpectConfig extends BaseDomain {
    @TableId(type = IdType.AUTO)
    private Integer id;
    // 配置键
    private String configKey;
    // 配置值
    private String configValue;
    // 配置名
    private String configName;
    // 分组
    private String groupName;
    // 是否显示在页面
    private String isShow;
    // 组件类型
    private String component;
    // 备注
    private String remark;
}
