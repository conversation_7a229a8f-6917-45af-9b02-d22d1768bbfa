package com.rcszh.worktime.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class SysWorkRecordItemVo {

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "项目Id")
    private Long projectId;

    @ApiModelProperty(value = "填报工时（分钟）")
    private Integer workDuration;

    @ApiModelProperty(value = "填报工时（分钟）")
    private BigDecimal workDurationPercent;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "工作日志id")
    private Long workLogId;
}
