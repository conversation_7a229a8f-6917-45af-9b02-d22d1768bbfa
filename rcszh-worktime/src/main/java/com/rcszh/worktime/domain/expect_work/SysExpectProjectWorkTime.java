package com.rcszh.worktime.domain.expect_work;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.rcszh.basic.domain.PmsConfig;
import com.rcszh.worktime.domain.dto.expect_work_time.SysExpectProjectWorkTimeDto;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.Date;

// 预估项目工时
@Getter
@Setter
public class SysExpectProjectWorkTime extends BaseExpectWork {
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    // 项目Id
    @NotNull(message = "项目[projectId]不能为空")
    private Long projectId;
    // 项目名称
    @TableField(exist = false)
    private String projectName;
    // 项目编码
    @TableField(exist = false)
    private String projectCode;
    // 预估工时月份
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "预估工时月份[expectDate]不能为空")
    private Date expectDate;

    public SysExpectProjectWorkTimeDto toDto(PmsConfig config) {
        SysExpectProjectWorkTimeDto dto = new SysExpectProjectWorkTimeDto();
        BeanUtils.copyProperties(this, dto);
        dto.setExpectWorkTime(covertWorkTime(config));
        return dto;
    }
}
