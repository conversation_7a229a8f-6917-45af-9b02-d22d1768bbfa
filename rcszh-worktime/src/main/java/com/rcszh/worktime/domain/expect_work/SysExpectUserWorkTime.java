package com.rcszh.worktime.domain.expect_work;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.rcszh.basic.domain.PmsConfig;
import com.rcszh.worktime.domain.dto.expect_work_time.SysExpectUserWorkTimeDto;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

@Getter
@Setter
public class SysExpectUserWorkTime extends BaseExpectWork {
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    // 项目预估工时配置id
    private String expectProjectId;
    // 用户id
    private Long userId;

    public SysExpectUserWorkTimeDto toDto(PmsConfig unitConfig) {
        SysExpectUserWorkTimeDto dto = new SysExpectUserWorkTimeDto();
        BeanUtils.copyProperties(this, dto);
        dto.setExpectWorkTime(covertWorkTime(unitConfig));
        return dto;
    }
}
