package com.rcszh.worktime.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.rcszh.common.domain.BaseDomain;
import com.rcszh.worktime.enums.PmsAllocationType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;


@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("pms_public_user_line")
@ApiModel(value = "公共人员明细")
public class PmsPublicUserLine extends BaseDomain {
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(name = "公共人员ID")
    private Long publicUserId;

    @ApiModelProperty(name = "用户ID")
    private Long userId;

    @ApiModelProperty(name = "分摊类型：全部、部分")
    private String allocationType;

    public String getAllocationTypeName() {
        return PmsAllocationType.getNameByCode(this.allocationType);
    }

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(name = "开始日期")
    private Date startTime;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(name = "结束日期")
    private Date endTime;
}
