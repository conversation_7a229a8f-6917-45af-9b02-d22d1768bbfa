package com.rcszh.worktime.domain.dto;

import com.rcszh.worktime.domain.PmsPublicUserLine;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;


@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class PmsPublicUserLineDto extends PmsPublicUserLine {

    @ApiModelProperty(name = "项目ids")
    List<Long> projectIds;
}
