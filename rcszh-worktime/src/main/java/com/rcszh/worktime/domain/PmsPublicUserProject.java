package com.rcszh.worktime.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rcszh.common.domain.BaseDomain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("pms_public_user_project")
@ApiModel(value = "公共人员项目")
public class PmsPublicUserProject extends BaseDomain {
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(name = "公共人员明细ID")
    private Long publicUserLineId;

    @ApiModelProperty(name = "项目ID")
    private Long projectId;
}
