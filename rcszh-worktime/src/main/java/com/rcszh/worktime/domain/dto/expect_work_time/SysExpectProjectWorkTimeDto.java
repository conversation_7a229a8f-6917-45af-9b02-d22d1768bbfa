package com.rcszh.worktime.domain.dto.expect_work_time;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.rcszh.basic.domain.PmsConfig;
import com.rcszh.worktime.domain.expect_work.SysExpectProjectWorkTime;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class SysExpectProjectWorkTimeDto extends BaseExpectWorkDto {
    private String id;
    // 项目Id
    @NotNull(message = "项目[projectId]不能为空")
    private Long projectId;
    // 项目名称
    @TableField(exist = false)
    private String projectName;
    // 项目编码
    @TableField(exist = false)
    private String projectCode;
    // 预估工时月份
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "预估工时月份[expectDate]不能为空")
    private Date expectDate;
    // 预估工时
    private BigDecimal expectWorkTime;

    public SysExpectProjectWorkTime toEntity(PmsConfig config) {
        SysExpectProjectWorkTime expectProjectWorkTime = new SysExpectProjectWorkTime();
        BeanUtils.copyProperties(this, expectProjectWorkTime);
        expectProjectWorkTime.setExpectWorkTime(covertWorkTime(config));
        return expectProjectWorkTime;
    }
}
