package com.rcszh.worktime.domain.dto.expect_work_time;

import com.rcszh.base.common.exception.ServiceException;
import com.rcszh.basic.domain.PmsConfig;
import com.rcszh.basic.enums.SysExpectConfigEnum;
import com.rcszh.common.domain.BaseDomain;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
public class BaseExpectWorkDto extends BaseDomain {
    // 预估工时
    private BigDecimal expectWorkTime;

    public Integer covertWorkTime(PmsConfig config) {
        String unit = config.getConfigValue();
        if (getExpectWorkTime() != null) {
            if (SysExpectConfigEnum.SysExpectConfigTimeUnitEnum.HOUR.getValue().equals(unit)) {
                // 小时
                int scale = getExpectWorkTime().scale();
                if (scale > 1) {
                    throw new ServiceException("预估工时以小时为单位只能精确到小数点后一位");
                }
                return getExpectWorkTime().multiply(BigDecimal.valueOf(60)).intValue();
            } else if (SysExpectConfigEnum.SysExpectConfigTimeUnitEnum.MINUTE.getValue().equals(unit)) {
                // 分钟
                int scale = getExpectWorkTime().scale();
                if (scale > 0) {
                    throw new ServiceException("预估工时以分钟为单位只能精确到整数位");
                }
                return getExpectWorkTime().intValue();
            } else {
                throw new RuntimeException("时间单位配置错误");
            }
        }
        return null;
    }
}
