package com.rcszh.worktime.domain.expect_work;

import com.rcszh.basic.domain.PmsConfig;
import com.rcszh.basic.enums.SysExpectConfigEnum;
import com.rcszh.common.domain.BaseDomain;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Getter
@Setter
public class BaseExpectWork extends BaseDomain {
    // 预计工时
    private Integer expectWorkTime;

    public BigDecimal covertWorkTime(PmsConfig unitConfig) {
        String unit = unitConfig.getConfigValue();
        if (getExpectWorkTime() != null) {
            if (SysExpectConfigEnum.SysExpectConfigTimeUnitEnum.HOUR.getValue().equals(unit)) {
                // 小时
                return BigDecimal.valueOf(getExpectWorkTime()).divide(BigDecimal.valueOf(60), 1, RoundingMode.HALF_DOWN);
            } else if (SysExpectConfigEnum.SysExpectConfigTimeUnitEnum.MINUTE.getValue().equals(unit)) {
                return BigDecimal.valueOf(getExpectWorkTime());
            } else {
                throw new RuntimeException("时间单位配置错误");
            }
        }
        return null;
    }
}
