package com.rcszh.worktime.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rcszh.annotation.BaseDataTranslate;
import com.rcszh.common.domain.BaseDomain;
import com.rcszh.enums.CacheDataType;
import com.rcszh.enums.CacheMethod;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;


@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("pms_public_user")
@ApiModel(value = "公共人员")
public class PmsPublicUser extends BaseDomain {
    @TableId(type = IdType.AUTO)
    private Long id;

    @BaseDataTranslate(dataType = CacheDataType.USER, method = CacheMethod.findById, params = "userId", showCol = "userName=userName,nickName=nickName")
    @ApiModelProperty(name = "用户ID")
    private Long userId;

    @ApiModelProperty(name = "登录名称")
    @TableField(exist = false)
    private String userName;

    @ApiModelProperty(name = "用户名称")
    @TableField(exist = false)
    private String nickName;

    /**
     * 是否删除
     */
    private String deleted;
}
