package com.rcszh.worktime.domain.vo;

import com.rcszh.basic.domain.echarts.BasePie;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

// 预计工时和实际工时饼状图
@Getter
@Setter
public class ExpectWorkTimeComparePieVo extends BasePie<Integer> {
//    private String name;
    private BigDecimal max;
    private BigDecimal min;
//    private Integer value;
    private List<ProjectDiff> projectList;

    @Data
    public static class ProjectDiff {
        private String projectName;
        private BigDecimal projectCompareValue;
    }
}
