package com.rcszh.worktime.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rcszh.annotation.BaseDataTranslate;
import com.rcszh.base.common.annotation.Excel;
import com.rcszh.common.domain.BaseDomain;
import com.rcszh.common.util.WorkTimeUtil;
import com.rcszh.enums.CacheDataType;
import com.rcszh.enums.CacheMethod;
import com.rcszh.worktime.enums.WorkRecordType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;


@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("sys_work_record")
@ApiModel(value = "工时填报记录")
public class SysWorkRecord extends BaseDomain {
    @TableId(type = IdType.AUTO)
    private Long id;

    @Excel(name = "项目名称", sort = 2)
    @TableField(exist = false)
    private String projectName;

    @Excel(name = "项目编码", sort = 1)
    @TableField(exist = false)
    private String projectCode;

    @ApiModelProperty(value = "项目Id")
    @BaseDataTranslate(dataType = CacheDataType.PROJECT,method = CacheMethod.findById,params = "projectId",showCol = "projectCode=code,projectName=name")
    private Long projectId;

    @ApiModelProperty(value = "人员")
    private Long userId;

    @Excel(name = "工时日期", sort = 3, dateFormat = "yyyy-MM-dd")
    private Date workTime;

    /**
     * 填报工时（分钟）
     */
    @Excel(name = "工时", sort = 6)
    private Integer workDuration;

    /**
     * 填报工时（百分比）
     */
    private BigDecimal workDurationPercent;

    /**
     * 工作日志id
     */
    private Long workLogId;

    public void setWorkMinute4Sec(Integer workSec) {
        this.workDuration = WorkTimeUtil.transformMinute(workSec);
    }

    /**
     * 最大工时（分钟）
     */
    private Integer maxWorkDuration;

    /**
     * 实际工时（分钟）
     */
    private Integer regularWorkDuration;

    public void setRegularWorkMinute4Sec(Integer regularWorkSec) {
        this.regularWorkDuration = WorkTimeUtil.transformMinute(regularWorkSec);
    }

    /**
     * 当日标准工作时长
     */
    private Integer standardWorkDuration;

    public void setStandardWorkDuration4Sec(Integer standardWorkSec) {
        this.standardWorkDuration = WorkTimeUtil.transformMinute(standardWorkSec);
    }

    /**
     * 状态：WorkRecordStatus
     */
    private String status;

    /**
     * 类型：WorkRecordType
     */
    private String type;

    @Excel(name = "工时类型", sort = 5)
    @TableField(exist = false)
    private String typeName;

    public String getTypeName() {
        return WorkRecordType.getNameByCode(type);
    }

    /**
     * 备注
     */
    @Excel(name = "任务", sort = 7)
    private String remark;

    @ApiModelProperty(value = "是否初始化数据")
    private String isInitData;

}
