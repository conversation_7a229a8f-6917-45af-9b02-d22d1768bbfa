package com.rcszh.worktime.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rcszh.base.common.annotation.Excel;
import com.rcszh.common.domain.BaseDomain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;


@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("pms_work_record_reset")
@ApiModel(value = "工时填报重置记录")
public class PmsWorkRecordReset extends BaseDomain {
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "人员")
    private Long userId;

    @ApiModelProperty(value = "人员名称")
    @TableField(exist = false)
    private String userName;

    @ApiModelProperty(value = "工时日期")
    private Date workTime;

    @ApiModelProperty(value = "操作人")
    private Long operatorId;

    @ApiModelProperty(value = "操作人名称")
    @TableField(exist = false)
    private String operatorName;

    @Excel(name = "任务", sort = 7)
    private String remark;
}
