package com.rcszh.worktime.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.rcszh.base.common.annotation.Excel;
import com.rcszh.common.domain.BaseDomain;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 工作日志对象 sys_work_log
 * 
 * <AUTHOR>
 * @date 2024-09-11
 */
@Data
public class SysWorkLog extends BaseDomain
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 父id */
    @Excel(name = "父id")
    private Long parentId;

    @TableField(exist = false)
    private String parentName;

    /** 编码 */
    @Excel(name = "编码")
    private String code;

    /** 名称 */
    @Excel(name = "名称")
    private String name;

    /** 祖级列表 */
    @Excel(name = "祖级列表")
    private String ancestors;

    /** 层级 */
    @Excel(name = "层级")
    private Integer level;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 删除标志（0代表存在 1代表删除） */
    private String delFlag;

    /**
     * 是否支持编写日志
     */
    private String writeLogFlag;

    /**
     * 适用类型
     */
    private String applicableTypes;

    /**
     * 适用类型
     */
    @TableField(exist = false)
    private String[] applicableTypesArray;

    /**
     * 适用类型
     */
    @TableField(exist = false)
    private String applicableTypesName;

    @TableField(exist = false)
    private List<SysWorkLog> children = new ArrayList<>();

}
