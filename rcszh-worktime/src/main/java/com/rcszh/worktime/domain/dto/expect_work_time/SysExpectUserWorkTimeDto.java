package com.rcszh.worktime.domain.dto.expect_work_time;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.rcszh.annotation.BaseDataTranslate;
import com.rcszh.basic.domain.PmsConfig;
import com.rcszh.enums.CacheDataType;
import com.rcszh.enums.CacheMethod;
import com.rcszh.worktime.domain.expect_work.SysExpectUserWorkTime;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.beans.BeanUtils;

@EqualsAndHashCode(callSuper = true)
@Data
public class SysExpectUserWorkTimeDto extends BaseExpectWorkDto {
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    // 项目预估工时配置id
    @NotNull(message = "项目预估工时配置[expectProjectId]不能为空")
    private String expectProjectId;
    // 用户id
    @BaseDataTranslate(dataType = CacheDataType.USER, method = CacheMethod.findById, params = "userId", showCol = "nickName=name")
    private Long userId;
    // 用户名称
    private String nickName;

    public SysExpectUserWorkTime toEntity(PmsConfig config) {
        SysExpectUserWorkTime expectUserWorkTime = new SysExpectUserWorkTime();
        BeanUtils.copyProperties(this, expectUserWorkTime);
        expectUserWorkTime.setExpectWorkTime(covertWorkTime(config));
        return expectUserWorkTime;
    }
}
