package com.rcszh.worktime.domain.dto.expect_work_time;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;

// 预计工时和实际工时比较参数
@Data
public class ExpectWorkTimeCompareDto {
    // 年份
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @NotNull(message = "时间参数[time]不能为空")
    private Date time;
    // 项目名称
    private Long projectId;
    // 时间单位
    private String unit;

}
