package com.rcszh.worktime.domain.vo;

import com.rcszh.base.common.core.text.Convert;
import com.rcszh.common.util.WorkTimeUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class SysWorkRecordVo {

    @ApiModelProperty(value = "填报时间")
    private Date workTime;

    @ApiModelProperty(value = "最大工时（分钟）")
    private Integer maxWorkDuration;

    @ApiModelProperty(value = "实际工时（分钟）")
    private BigDecimal regularWorkDuration;

    @ApiModelProperty(value = "标准工作时长（分钟）")
    private Integer standardWorkDuration;

    private boolean hasCheckin;

    @ApiModelProperty(value = "考勤工时发生变动")
    private boolean regularChanged;

    /**
     * 状态标签
     */
    private String statusTag;

    /**
     * 状态类型
     */
    private String statusType;

    private String isPublicUser;

    public void setRegularWorkDuration4Sec(Integer regularWorkSec) {
        this.regularWorkDuration = Convert.toBigDecimal(WorkTimeUtil.transformMinuteBig(regularWorkSec));
    }

    public void setStandardWorkDuration4Sec(Integer standardWorkSec) {
        this.standardWorkDuration = WorkTimeUtil.transformMinute(standardWorkSec);
    }

    List<SysWorkRecordItemVo> items;

}
