package com.rcszh.worktime.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rcszh.base.common.annotation.Excel;
import com.rcszh.common.domain.BaseDomain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;


@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("sys_work_entry")
@ApiModel(value = "工时填报逾期可填记录")
public class SysWorkEntry extends BaseDomain {
    @TableId(type = IdType.AUTO)
    private Long id;

//    @ApiModelProperty(value = "项目Id")
//    private Long projectId;

    @ApiModelProperty(value = "人员")
    private Long userId;

    @Excel(name = "工时日期", sort = 3, dateFormat = "yyyy-MM-dd")
    private Date workTime;
}
