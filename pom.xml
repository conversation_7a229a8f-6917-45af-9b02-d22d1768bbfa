<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.rcszh</groupId>
	<artifactId>pms</artifactId>
	<version>0.0.1</version>
	<name>项目管理系统</name>
	<description>项目管理系统-工时</description>
	<packaging>pom</packaging>
	<properties>
		<ruoyi.version>3.8.7</ruoyi.version>
		<pms.version>0.0.1</pms.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<java.version>21</java.version>
		<maven-jar-plugin.version>3.1.1</maven-jar-plugin.version>
		<druid.version>1.2.23</druid.version>
		<bitwalker.version>1.21</bitwalker.version>
		<swagger.version>3.0.0</swagger.version>
		<kaptcha.version>2.3.3</kaptcha.version>
		<pagehelper.boot.version>1.4.6</pagehelper.boot.version>
		<fastjson.version>2.0.25</fastjson.version>
		<oshi.version>6.4.0</oshi.version>
		<commons.io.version>2.11.0</commons.io.version>
		<commons.collections.version>3.2.2</commons.collections.version>
		<poi.version>4.1.2</poi.version>
		<easy.poi.version>4.2.0</easy.poi.version>
		<velocity.version>2.3</velocity.version>
		<jwt.version>0.9.1</jwt.version>
		<mariadb.version>3.3.3</mariadb.version>
		<dingtalk-version>2.1.14</dingtalk-version>
		<springboot.version>3.2.5</springboot.version>
		<pdfbox.version>3.0.1</pdfbox.version>
	</properties>

	<modules>
		<module>rcszh-base-common</module>
		<module>ruoyi-framework</module>
        <module>rcszh-system</module>
		<module>rcszh-common</module>
		<module>rcszh-admin</module>
		<module>rcszh-external</module>
		<module>rcszh-basic-data</module>
		<module>rcszh-generator</module>
		<module>rcszh-quartz</module>
		<module>rcszh-pms</module>
		<module>rcszh-pms-wechat</module>
		<module>rcszh-pms-dingtalk</module>
		<module>rcszh-activiti</module>
        <module>rcszh-message</module>
		<module>rcszh-common-baseDataTranslate</module>
        <module>rcszh-framework-redis</module>
        <module>rcszh-cache</module>
        <module>rcszh-lowcode</module>
        <module>rcszh-orm</module>
		<module>rcszh-pms-weChatMiniApp</module>
        <module>rcszh-form</module>
		<module>rcszh-project</module>
		<module>rcszh-attendance</module>
		<module>rcszh-worktime</module>
		<module>rcszh-report</module>
        <module>rcszh-api</module>
        <module>rcszh-auth</module>
        <module>rcszh-equipment</module>
    </modules>

	<!-- 依赖声明 -->
	<dependencyManagement>
		<dependencies>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>4.0.3</version>
            </dependency>

			<!-- SpringBoot的依赖配置-->
			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-dependencies</artifactId>
				<version>${springboot.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>

			<!--web框架-->
			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-starter-web</artifactId>
				<version>${springboot.version}</version>
			</dependency>

			<!-- 阿里数据库连接池 -->
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>druid-spring-boot-starter</artifactId>
				<version>${druid.version}</version>
			</dependency>
			<!-- 解析客户端操作系统、浏览器等 -->
			<dependency>
				<groupId>eu.bitwalker</groupId>
				<artifactId>UserAgentUtils</artifactId>
				<version>${bitwalker.version}</version>
			</dependency>

			<!-- pagehelper 分页插件 -->
			<dependency>
				<groupId>com.github.pagehelper</groupId>
				<artifactId>pagehelper-spring-boot-starter</artifactId>
				<version>${pagehelper.boot.version}</version>
				<exclusions>
					<exclusion>
						<!--                    去除mybatis依赖-->
						<groupId>org.mybatis</groupId>
						<artifactId>mybatis</artifactId>
					</exclusion>
				</exclusions>
			</dependency>

			<!-- 获取系统信息 -->
			<dependency>
				<groupId>com.github.oshi</groupId>
				<artifactId>oshi-core</artifactId>
				<version>${oshi.version}</version>
			</dependency>

			<!-- Swagger3依赖 -->
			<dependency>
				<groupId>io.springfox</groupId>
				<artifactId>springfox-boot-starter</artifactId>
				<version>${swagger.version}</version>
				<exclusions>
					<exclusion>
						<groupId>io.swagger</groupId>
						<artifactId>swagger-models</artifactId>
					</exclusion>
				</exclusions>
			</dependency>

			<!-- io常用工具类 -->
			<dependency>
				<groupId>commons-io</groupId>
				<artifactId>commons-io</artifactId>
				<version>${commons.io.version}</version>
			</dependency>

			<!-- excel工具 -->
			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi</artifactId>
				<version>${poi.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi-ooxml</artifactId>
				<version>${poi.version}</version>
			</dependency>
			<dependency>
				<groupId>cn.afterturn</groupId>
				<artifactId>easypoi-base</artifactId>
				<version>${easy.poi.version}</version>
			</dependency>

			<!-- velocity代码生成使用模板 -->
			<dependency>
				<groupId>org.apache.velocity</groupId>
				<artifactId>velocity-engine-core</artifactId>
				<version>${velocity.version}</version>
			</dependency>

			<!-- collections工具类 -->
			<dependency>
				<groupId>commons-collections</groupId>
				<artifactId>commons-collections</artifactId>
				<version>${commons.collections.version}</version>
			</dependency>

			<!-- 阿里JSON解析器 -->
			<dependency>
				<groupId>com.alibaba.fastjson2</groupId>
				<artifactId>fastjson2</artifactId>
				<version>${fastjson.version}</version>
			</dependency>

			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>fastjson</artifactId>
				<version>1.2.45</version>
				<scope>compile</scope>
			</dependency>

			<!-- Token生成与解析-->
			<dependency>
				<groupId>io.jsonwebtoken</groupId>
				<artifactId>jjwt</artifactId>
				<version>${jwt.version}</version>
			</dependency>

			<!-- 验证码 -->
			<dependency>
				<groupId>pro.fessional</groupId>
				<artifactId>kaptcha</artifactId>
				<version>${kaptcha.version}</version>
			</dependency>

			<!-- 定时任务-->
			<dependency>
				<groupId>com.rcszh</groupId>
				<artifactId>rcszh-quartz</artifactId>
				<version>${pms.version}</version>
			</dependency>

			<!-- 代码生成-->
			<dependency>
				<groupId>com.rcszh</groupId>
				<artifactId>rcszh-generator</artifactId>
				<version>${pms.version}</version>
			</dependency>

			<!-- 核心模块-->
			<dependency>
				<groupId>com.ruoyi</groupId>
				<artifactId>ruoyi-framework</artifactId>
				<version>${pms.version}</version>
			</dependency>

			<!-- 系统模块-->
			<dependency>
                <groupId>com.rcszh</groupId>
                <artifactId>rcszh-system</artifactId>
				<version>${pms.version}</version>
			</dependency>

			<!-- 通用工具-->
			<dependency>
				<groupId>com.rcszh</groupId>
				<artifactId>rcszh-base-common</artifactId>
				<version>${pms.version}</version>
			</dependency>
			<!-- https://mvnrepository.com/artifact/org.apache.pdfbox/pdfbox -->
			<dependency>
				<groupId>org.apache.pdfbox</groupId>
				<artifactId>pdfbox</artifactId>
				<version>${pdfbox.version}</version>
			</dependency>
			<dependency>
				<groupId>org.mariadb.jdbc</groupId>
				<artifactId>mariadb-java-client</artifactId>
				<version>${mariadb.version}</version>
			</dependency>
			<dependency>
				<groupId>com.github.binarywang</groupId>
				<artifactId>weixin-java-cp</artifactId>
				<version>4.7.0</version>
			</dependency>

			<dependency>
				<groupId>cn.hutool</groupId>
				<artifactId>hutool-all</artifactId>
				<version>5.8.29</version>
			</dependency>
			<dependency>
				<groupId>cn.hchub</groupId>
				<artifactId>redisson-spring-boot-starter</artifactId>
				<version>3.2.0</version>
			</dependency>

			<dependency>
				<groupId>com.rcszh</groupId>
				<artifactId>rcszh-common</artifactId>
				<version>${pms.version}</version>
			</dependency>

			<dependency>
				<groupId>com.rcszh</groupId>
				<artifactId>rcszh-auth</artifactId>
				<version>${pms.version}</version>
			</dependency>
			<dependency>
				<groupId>com.rcszh</groupId>
				<artifactId>rcszh-basic-data</artifactId>
				<version>${pms.version}</version>
			</dependency>
			<dependency>
				<groupId>com.rcszh</groupId>
				<artifactId>rcszh-external</artifactId>
				<version>${pms.version}</version>
			</dependency>
			<dependency>
				<groupId>com.rcszh</groupId>
				<artifactId>rcszh-project</artifactId>
				<version>${pms.version}</version>
			</dependency>
			<dependency>
				<groupId>com.rcszh</groupId>
				<artifactId>rcszh-attendance</artifactId>
				<version>${pms.version}</version>
			</dependency>
			<dependency>
				<groupId>com.rcszh</groupId>
				<artifactId>rcszh-worktime</artifactId>
                <version>${pms.version}</version>
            </dependency>
            <dependency>
                <groupId>com.rcszh</groupId>
                <artifactId>rcszh-equipment</artifactId>
				<version>${pms.version}</version>
			</dependency>
			<dependency>
				<groupId>com.rcszh</groupId>
				<artifactId>rcszh-report</artifactId>
                <version>${pms.version}</version>
            </dependency>
            <dependency>
                <groupId>com.rcszh</groupId>
                <artifactId>rcszh-api</artifactId>
				<version>${pms.version}</version>
			</dependency>
			<dependency>
				<groupId>com.rcszh</groupId>
				<artifactId>rcszh-message</artifactId>
				<version>${pms.version}</version>
			</dependency>
			<dependency>
				<groupId>com.rcszh</groupId>
				<artifactId>rcszh-pms</artifactId>
				<version>${pms.version}</version>
			</dependency>
			<dependency>
				<groupId>com.rcszh</groupId>
				<artifactId>rcszh-pms-wechat</artifactId>
				<version>${pms.version}</version>
			</dependency>
			<dependency>
				<groupId>com.rcszh</groupId>
				<artifactId>rcszh-pms-weChatMiniApp</artifactId>
				<version>${pms.version}</version>
			</dependency>
			<dependency>
				<groupId>com.rcszh</groupId>
				<artifactId>rcszh-pms-dingtalk</artifactId>
				<version>${pms.version}</version>
			</dependency>
			<dependency>
				<groupId>com.rcszh</groupId>
                <artifactId>rcszh-cache</artifactId>
                <version>${pms.version}</version>
            </dependency>
            <dependency>
                <groupId>com.rcszh</groupId>
				<artifactId>rcszh-common-baseDataTranslate</artifactId>
				<version>${pms.version}</version>
			</dependency>
			<dependency>
				<groupId>com.rcszh</groupId>
				<artifactId>rcszh-activiti</artifactId>
				<version>${pms.version}</version>
			</dependency>
			<dependency>
				<groupId>com.rcszh</groupId>
				<artifactId>rcszh-lowcode</artifactId>
                <version>${pms.version}</version>
            </dependency>
            <dependency>
                <groupId>com.rcszh</groupId>
                <artifactId>rcszh-framework-redis</artifactId>
				<version>${pms.version}</version>
			</dependency>
			<dependency>
				<groupId>com.baomidou</groupId>
				<artifactId>mybatis-plus-spring-boot3-starter</artifactId>
				<version>3.5.5</version>
			</dependency>
			<dependency>
				<groupId>com.aliyun</groupId>
				<artifactId>dingtalk</artifactId>
				<version>${dingtalk-version}</version>
			</dependency>
			<dependency>
				<groupId>javax.xml.bind</groupId>
				<artifactId>jaxb-api</artifactId>
				<version>2.3.1</version>
			</dependency>
			<dependency>
				<groupId>jakarta.servlet</groupId>
				<artifactId>jakarta.servlet-api</artifactId>
				<version>6.0.0</version>
			</dependency>
            <!--			<dependency>-->
            <!--				<groupId>javax.servlet</groupId>-->
            <!--				<artifactId>javax.servlet-api</artifactId>-->
            <!--				<version>3.0.1</version>-->
            <!--			</dependency>-->
			<dependency>
				<groupId>io.swagger</groupId>
				<artifactId>swagger-models</artifactId>
				<version>1.6.2</version>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.6.1</version>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
					<encoding>${project.build.sourceEncoding}</encoding>
					<compilerArgs>
						<arg>-parameters</arg>
					</compilerArgs>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<version>3.3.1</version>
				<configuration>
					<delimiters>
						<delimiter>@</delimiter>
					</delimiters>
					<useDefaultDelimiters>false</useDefaultDelimiters>
					<!-- 过滤后缀不需要转码的文件后缀名-->
					<nonFilteredFileExtensions>
						<nonFilteredFileExtension>ttf</nonFilteredFileExtension>
					</nonFilteredFileExtensions>
				</configuration>
			</plugin>
		</plugins>
		<resources>
			<resource>
                <!--				<filtering>true</filtering>-->
				<directory>src/main/resources</directory>
				<excludes>
					<exclude>application*.yml</exclude>
				</excludes>
			</resource>
			<resource>
				<directory>src/main/resources</directory>
                <filtering>false</filtering>
                <includes>
                    <include>*/*.xlsx</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
				<!-- 是否替换@xx@表示的maven properties属性值 -->
				<filtering>true</filtering>
                <excludes>
                    <exclude>*/*.xlsx</exclude>
                </excludes>
			</resource>
		</resources>
	</build>
	<profiles>
		<profile>
			<!-- 本地环境 -->
			<id>local</id>
			<properties>
				<profileActive>local</profileActive>
			</properties>
			<!-- 默认环境 -->
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
		</profile>
		<profile>
			<!-- 开发环境 -->
			<id>dev</id>
			<properties>
				<profileActive>dev</profileActive>
			</properties>
		</profile>
		<profile>
			<id>prod</id>
			<properties>
				<profileActive>prod</profileActive>
			</properties>
		</profile>
	</profiles>
</project>
