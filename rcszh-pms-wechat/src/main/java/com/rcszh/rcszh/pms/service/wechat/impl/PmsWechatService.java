package com.rcszh.rcszh.pms.service.wechat.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rcszh.base.common.core.domain.entity.SysUser;
import com.rcszh.base.common.core.text.Convert;
import com.rcszh.base.common.exception.ServiceException;
import com.rcszh.basic.domain.SysApplication;
import com.rcszh.basic.domain.SysDeptExt;
import com.rcszh.basic.domain.SysExternalUser;
import com.rcszh.basic.enums.PlatformType;
import com.rcszh.basic.service.ISysApplicationService;
import com.rcszh.basic.service.ISysCommonService;
import com.rcszh.basic.service.ISysExternalService;
import com.rcszh.basic.service.impl.SysDeptExtServiceImpl;
import com.rcszh.external.server.weixin.WeiXinCpService;
import com.rcszh.rcszh.pms.service.wechat.IPmsWechatService;
import com.rcszh.system.service.ISysUserService;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.bean.WxCpDepart;
import me.chanjar.weixin.cp.bean.WxCpUser;
import me.chanjar.weixin.cp.bean.WxCpUserDetail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class PmsWechatService implements IPmsWechatService {

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysCommonService sysCommonService;

    @Autowired
    private WeiXinCpService weiXinCpService;

    @Autowired
    private ISysExternalService externalUserService;

    @Autowired
    private ISysApplicationService applicationService;

    @Autowired
    private SysDeptExtServiceImpl iSysDeptExtService;

    @Override
    public SysUser saveOrUpdate(String appId, String corpId, String appKey, String corpsecret, String wxUserid, String userTicket) {
        SysUser threeUser = new SysUser();
        WxCpUser wxUser = weiXinCpService.getUser(corpId, Convert.toInt(appKey), corpsecret, wxUserid);
        WxCpUserDetail userDetail = weiXinCpService.getUserDetail(corpId, Convert.toInt(appKey), corpsecret, userTicket);
        String userName = userDetail.getUserId();
//        for (WxCpUser.Attr extAttr : wxUser.getExtAttrs()) {
//            if ("工号".equals(extAttr.getName()) && StrUtil.isNotBlank(extAttr.getTextValue())) {
//                userName = extAttr.getTextValue();
//            }
//        }
        threeUser.setUserName(userName);
        threeUser.setNickName(wxUser.getName());
        threeUser.setEmail(userDetail.getEmail());
        threeUser.setPhonenumber(userDetail.getMobile());
        // 0=男,1=女,2=未知
        // 微信：0表示未定义，1表示男性，2表示女性
        if ("0".equals(userDetail.getGender())) {
            threeUser.setSex("2");
        } else if ("1".equals(userDetail.getGender())) {
            threeUser.setSex("0");
        } else if ("2".equals(userDetail.getGender())) {
            threeUser.setSex("1");
        }
        threeUser.setAvatar(userDetail.getAvatar());

        SysUser sysUser = null;
        SysExternalUser externalUser = externalUserService.getByExternalUserIdAndAppId(appId, wxUserid);
        if (Objects.nonNull(externalUser)) {
            sysUser = userService.selectUserById(externalUser.getUserId());
            sysUser = sysCommonService.saveSysUser(sysUser, threeUser);
        } else {
            sysUser = userService.selectUserByUserName(threeUser.getUserName());
            sysUser = sysCommonService.saveSysUser(sysUser, threeUser);

            externalUser = externalUserService.getByUserIdAndAppId(appId, sysUser.getUserId());
            if (Objects.nonNull(externalUser)) {
                externalUser.setExternalUserId(wxUserid);
                externalUser.setAppId(appId);
                externalUser.setUpdateBy("1");
                externalUser.setUpdateTime(new Date());
                externalUserService.updateById(externalUser);
            } else {
                externalUser = new SysExternalUser();
                externalUser.setPlatform(PlatformType.wechat.getCode());
                externalUser.setExternalUserId(wxUserid);
                externalUser.setUserId(sysUser.getUserId());
                externalUser.setAppId(appId);
                externalUser.setCreateBy("1");
                externalUser.setCreateTime(new Date());
                externalUserService.save(externalUser);
            }
        }
        return sysUser;
    }

    @Transactional(rollbackFor = Exception.class)
    public void syncWechatDeptUser() throws WxErrorException {
        List<SysApplication> applications = applicationService.findByPlatform(PlatformType.wechat);
        Set<String> externalUserIds = new HashSet<>();
        for (SysApplication wechatApplication : applications) {
            // 拿到企业微信的所有部门
            String appId = wechatApplication.getId();
            Integer agentId = Convert.toInt(wechatApplication.getAppKey());
            Long companyId = wechatApplication.getCompanyId();
            List<WxCpDepart> wechatDept = weiXinCpService.getDepartList(wechatApplication.getCorpId(), Convert.toInt(wechatApplication.getAppKey()), wechatApplication.getAppSecret());
            Map<String, SysDeptExt> sysDeptExtMap = iSysDeptExtService.findListByCompanyId(companyId)
                    .stream()
                    .filter(item -> item.getDeptCode() != null)
                    .collect(Collectors.toMap(SysDeptExt::getDeptCode, item -> item));
            WxCpDepart wxCpDepart = wechatDept.stream().filter(item -> item.getParentId().equals(0L)).findFirst().orElseThrow();
            // 获取部门下的所有用户
            Long wxCpDepartId = wxCpDepart.getId();
            // 获取所有的微信用户
            List<WxCpUser> wxUsers = weiXinCpService.getUserListByDeptId(wechatApplication.getCorpId(), Convert.toInt(wechatApplication.getAppKey()), wechatApplication.getAppSecret(), wxCpDepartId);
            if (CollUtil.isEmpty(wxUsers)) {
                continue;
            }
            Map<String, WxCpUser> wxCpUserMap = wxUsers.stream().collect(Collectors.toMap(WxCpUser::getUserId, item -> item));
            // 通过externalUserId查询出所有的外部用户
            List<SysExternalUser> externalUserList = externalUserService.getByExternalUserIdListAndAppId(appId, wxCpUserMap.keySet().stream().toList());
            Map<String, SysExternalUser> externalExternalUserMap = externalUserList.stream().collect(Collectors.toMap(SysExternalUser::getExternalUserId, item -> item));
            String deptCode = companyId + "_" + wxCpDepartId.toString();
            SysDeptExt sysDeptExt = sysDeptExtMap.get(deptCode);
            if (sysDeptExt == null) {
                throw new ServiceException("对应企业微信部门不存在：" + deptCode);
            }

            for (WxCpUser user : wxUsers) {
                SysUser threeUser = new SysUser();
                String userName = user.getUserId();
//        for (WxCpUser.Attr extAttr : wxUser.getExtAttrs()) {
//            if ("工号".equals(extAttr.getName()) && StrUtil.isNotBlank(extAttr.getTextValue())) {
//                userName = extAttr.getTextValue();
//            }
//        }
                threeUser.setUserName(userName);
                threeUser.setNickName(user.getName());
                threeUser.setEmail(user.getEmail());
                threeUser.setPhonenumber(user.getMobile());
                threeUser.setAvatar(user.getAvatar());
                if (StrUtil.isNotBlank(user.getMainDepartment())) {
                    SysDeptExt deptExt = sysDeptExtMap.get(companyId + "_" + user.getMainDepartment());
                    if (Objects.nonNull(deptExt)) {
                        threeUser.setDeptId(deptExt.getDeptId());
                    }
                }
                SysExternalUser externalUser = externalExternalUserMap.get(user.getUserId());
                SysUser sysUser = null;
                if (Objects.nonNull(externalUser)) {
                    sysUser = userService.selectUserById(externalUser.getUserId());
                    sysUser = sysCommonService.saveSysUser(sysUser, threeUser);
                } else {
                    sysUser = userService.selectUserByUserName(threeUser.getUserName());
                    sysUser = sysCommonService.saveSysUser(sysUser, threeUser);

                    externalUser = externalUserService.getByUserIdAndAppId(appId, sysUser.getUserId());
                    if (Objects.nonNull(externalUser)) {
                        externalUser.setExternalUserId(user.getUserId());
                        externalUser.setAppId(appId);
                        externalUser.setUpdateBy("1");
                        externalUser.setUpdateTime(new Date());
                        externalUserService.updateById(externalUser);
                    } else {
                        externalUser = new SysExternalUser();
                        externalUser.setPlatform(PlatformType.wechat.getCode());
                        externalUser.setExternalUserId(user.getUserId());
                        externalUser.setUserId(sysUser.getUserId());
                        externalUser.setAppId(appId);
                        externalUser.setCreateBy("1");
                        externalUser.setCreateTime(new Date());
                        externalUserService.save(externalUser);
                    }
                }

                externalUserIds.add(user.getUserId());
            }
        }

        if (CollUtil.isNotEmpty(applications)) {
            // 禁用用户
            List<SysExternalUser> externalUsers = externalUserService.list(new LambdaQueryWrapper<SysExternalUser>()
                    .in(SysExternalUser::getAppId, applications.stream().map(SysApplication::getId).toList())
                    .notIn(SysExternalUser::getExternalUserId, externalUserIds));
            if (CollUtil.isNotEmpty(externalUsers)) {
                sysCommonService.disableUserByUserIds(externalUsers.stream().map(SysExternalUser::getUserId).toList());
            }
        }
    }


    /**
     * 同步企业微信部门
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncWechatDept() throws WxErrorException {
        // 同步多个企业微信部门
        List<SysApplication> applications = applicationService.findByPlatform(PlatformType.wechat);
        for (SysApplication wechatApplication : applications) {
            // 拿到企业微信对应的系统中的根部门
            Long companyId = wechatApplication.getCompanyId();
            if (companyId == null) {
                // 没指定公司
                throw new ServiceException("应用没配置公司: ");
            }
            // 查询系统中的所有部门
            List<SysDeptExt> sysDeptList = iSysDeptExtService.findListByCompanyId(companyId);
            // 拿到企业微信的所有部门
            String appId = wechatApplication.getId();
            Integer agentId = Convert.toInt(wechatApplication.getAppKey());
            List<WxCpDepart> wechatDepts = weiXinCpService.getDepartList(wechatApplication.getCorpId(), Convert.toInt(wechatApplication.getAppKey()), wechatApplication.getAppSecret());
            Tree<String> rootTree = buildDeptTree(wechatDepts).getFirst();

            SysDeptExt company = sysDeptList.stream()
                    .filter(item -> item.getDeptId().equals(companyId))
                    .findFirst()
                    .orElseThrow(() -> new ServiceException("企业微信根部门不存在: " + companyId));
            company.setDeptName((String) rootTree.getName());
            company.setDeptCode(companyId + "_" + rootTree.getId());
            company.fillBaseFields();
            iSysDeptExtService.updateById(company);
            // 移除不存在于企业微信的部门
            Map<String, WxCpDepart> wechatDeptMap = wechatDepts
                    .stream()
                    .collect(Collectors.toMap(item -> companyId + "_" + item.getId().toString(), item -> item));
            sysDeptList.stream()
                    .filter(item -> wechatDeptMap.get(item.getDeptCode()) == null)
                    .toList()
                    .forEach(dept -> {
                        dept.setDelFlag("2");
                        dept.fillBaseFields();
                        iSysDeptExtService.saveDeptExt(dept, companyId);
                    });

            Map<String, SysDeptExt> sysDeptExtMap = sysDeptList.stream().collect(Collectors.toMap(SysDeptExt::getDeptCode, item -> item));
            searchDept(rootTree, null, sysDeptExtMap, companyId, wechatApplication);
        }
    }

    public void searchDept(Tree<String> node, SysDeptExt parent, Map<String, SysDeptExt> sysDeptExtMap, Long rootDeptId, SysApplication wechatApplication) {
        if (node == null) {
            return;
        }
        sysDeptExtMap = sysDeptExtMap == null ? new HashMap<>() : sysDeptExtMap;
        // 遍历顶层部门
        String deptCode = rootDeptId + "_" + node.getId();
        SysDeptExt oldSysDept = sysDeptExtMap.get(deptCode);
        WxCpDepart wxCpDepart = JSONUtil.parseObj(node.get("obj")).toBean(WxCpDepart.class);
        if (oldSysDept == null) {
            // 不存在则新增
            oldSysDept = new SysDeptExt();
            oldSysDept.setDeptCode(deptCode);
            oldSysDept.setAncestors("0");
            oldSysDept.setParentId(0L);
            oldSysDept.setOrderNum(0);
            oldSysDept.fillBaseFields();
        }
        oldSysDept.setParentId(parent == null ? 0L : parent.getDeptId());
        oldSysDept.setAncestors(parent == null ? "0" : parent.getAncestors() + "," + parent.getDeptId());
        oldSysDept.setDeptName((String) node.getName());
        String wxLeader = ArrayUtil.isNotEmpty(wxCpDepart.getDepartmentLeader()) ? wxCpDepart.getDepartmentLeader()[0] : null;
        if (wxLeader != null) {
            WxCpUser user = weiXinCpService.getUser(wechatApplication.getCorpId(), Convert.toInt(wechatApplication.getAppKey()), wechatApplication.getAppSecret(), wxLeader);
            SysExternalUser sysExternalUser = externalUserService.getByExternalUserIdAndAppId(wechatApplication.getId(), user.getUserId());
            oldSysDept.setLeader(sysExternalUser.getUserId().toString());
        }
        SysDeptExt sysDeptExt = iSysDeptExtService.saveDeptExt(oldSysDept, rootDeptId);
        Long companyId = rootDeptId;
        if (rootDeptId == null) {
            sysDeptExt.setCompanyId(oldSysDept.getDeptId());
            iSysDeptExtService.saveDeptExt(oldSysDept, sysDeptExt.getCompanyId());
            companyId = sysDeptExt.getCompanyId();
        }
        // 遍历子部门
        List<Tree<String>> children = node.getChildren();
        if (CollUtil.isNotEmpty(children)) {
            for (Tree<String> child : children) {
                searchDept(child, oldSysDept, sysDeptExtMap, companyId, wechatApplication);
            }
        }
    }


    /**
     * 构建部门树
     *
     * @param deptList 企业微信部门列表
     * @return 部门树
     */
    public List<Tree<String>> buildDeptTree(List<WxCpDepart> deptList) {
        List<TreeNode<String>> nodeList = CollUtil.newArrayList();
        for (WxCpDepart wxCpDepart : deptList) {
            TreeNode<String> node = new TreeNode<>();
            node.setId(String.valueOf(wxCpDepart.getId()));
            node.setName(wxCpDepart.getName());
            node.setParentId(String.valueOf(wxCpDepart.getParentId()));
//            node.setWeight(wxCpDepart.getOrder());
            Map<String, Object> extra = new HashMap<>(2);
            extra.put("obj", wxCpDepart);
            node.setExtra(extra);
            nodeList.add(node);
        }
        return TreeUtil.build(nodeList, "0");
    }


}
