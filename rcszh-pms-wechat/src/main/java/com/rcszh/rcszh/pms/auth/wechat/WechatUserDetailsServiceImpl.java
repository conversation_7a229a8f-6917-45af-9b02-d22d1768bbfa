//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fern<PERSON>lower decompiler)
//

package com.rcszh.rcszh.pms.auth.wechat;

import com.rcszh.base.common.core.domain.entity.SysUser;
import com.rcszh.base.common.core.domain.model.LoginUser;
import com.rcszh.base.common.enums.UserStatus;
import com.rcszh.base.common.exception.ServiceException;
import com.rcszh.base.common.utils.MessageUtils;
import com.rcszh.base.common.utils.StringUtils;
import com.ruoyi.framework.web.service.SysPermissionService;
import com.rcszh.system.service.ISysUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

@Service
public class WechatUserDetailsServiceImpl implements UserDetailsService {
    private static final Logger log = LoggerFactory.getLogger(WechatUserDetailsServiceImpl.class);

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private ISysUserService userService;

    public WechatUserDetailsServiceImpl() {
    }

    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        SysUser user = userService.selectUserByUserName(username);
        if (StringUtils.isNull(user)) {
            log.info("登录用户：{} 不存在.", username);
            throw new ServiceException(MessageUtils.message("user.not.exists"));
        } else if (UserStatus.DELETED.getCode().equals(user.getDelFlag())) {
            log.info("登录用户：{} 已被删除.", username);
            throw new ServiceException(MessageUtils.message("user.password.delete"));
        } else if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
            log.info("登录用户：{} 已被停用.", username);
            throw new ServiceException(MessageUtils.message("user.blocked"));
        }
        return this.createLoginUser(user);
    }

    public UserDetails createLoginUser(SysUser user) {
        return new LoginUser(user.getUserId(), user.getDeptId(), user, this.permissionService.getMenuPermission(user));
    }
}
