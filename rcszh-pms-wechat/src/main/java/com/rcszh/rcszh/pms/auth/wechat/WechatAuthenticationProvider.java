package com.rcszh.rcszh.pms.auth.wechat;

import com.rcszh.base.common.core.domain.entity.SysUser;
import com.rcszh.base.common.core.text.Convert;
import com.rcszh.base.common.exception.ServiceException;
import com.rcszh.basic.domain.SysApplication;
import com.rcszh.basic.service.ISysApplicationService;
import com.rcszh.external.server.weixin.WeiXinCpService;
import com.rcszh.rcszh.pms.service.wechat.IPmsWechatService;
import me.chanjar.weixin.cp.bean.WxCpOauth2UserInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class WechatAuthenticationProvider implements AuthenticationProvider {

    @Qualifier("wechatUserDetailsServiceImpl")
    @Autowired
    private UserDetailsService wechatUserDetailsService;

    @Autowired
    private ISysApplicationService applicationService;

    @Autowired
    private WeiXinCpService weiXinCpService;

    @Autowired
    private IPmsWechatService pmsWechatService;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        WechatAuthenticationToken authenticationToken = (WechatAuthenticationToken) authentication;

        String code = (String) authenticationToken.getPrincipal();

        SysApplication application = applicationService.getById(authenticationToken.getAppId());
        if (Objects.isNull(application)) {
            throw new ServiceException("应用不存在");
        }
        WxCpOauth2UserInfo userInfo = weiXinCpService.loginByCpCode(application.getCorpId(), Convert.toInt(application.getAppKey()), application.getAppSecret(), code);
        SysUser user = pmsWechatService.saveOrUpdate(application.getId(), application.getCorpId(), application.getAppKey(), application.getAppSecret(), userInfo.getUserId(), userInfo.getUserTicket());

        UserDetails userDetails = wechatUserDetailsService.loadUserByUsername(user.getUserName());

        // 此时鉴权成功后，应当重新 new 一个拥有鉴权的 authenticationResult 返回
        WechatAuthenticationToken authenticationResult = new WechatAuthenticationToken(userDetails, userDetails.getAuthorities());

        authenticationResult.setDetails(authenticationToken.getDetails());

        return authenticationResult;
    }


    @Override
    public boolean supports(Class<?> authentication) {
        // 判断 authentication 是不是 WxCodeAuthenticationToken 的子类或子接口
        return WechatAuthenticationToken.class.isAssignableFrom(authentication);
    }
}