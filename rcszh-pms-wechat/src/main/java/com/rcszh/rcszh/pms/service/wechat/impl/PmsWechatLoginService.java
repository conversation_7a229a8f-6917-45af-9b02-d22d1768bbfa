package com.rcszh.rcszh.pms.service.wechat.impl;

import com.rcszh.pms.service.IPmsLoginService;
import com.rcszh.rcszh.pms.auth.wechat.WechatAuthenticationToken;
import com.rcszh.basic.domain.dto.SsoLoginBodyDTO;
import com.rcszh.basic.service.ISysCommonService;
import com.rcszh.base.common.constant.Constants;
import com.rcszh.base.common.core.domain.model.LoginUser;
import com.rcszh.base.common.exception.ServiceException;
import com.rcszh.base.common.utils.MessageUtils;
import com.ruoyi.framework.manager.AsyncManager;
import com.ruoyi.framework.manager.factory.AsyncFactory;
import com.ruoyi.framework.security.context.AuthenticationContextHolder;
import com.ruoyi.framework.web.service.TokenService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

@Service
public class PmsWechatLoginService implements IPmsLoginService {

    @Resource
    private AuthenticationManager authenticationManager;

    @Autowired
    private ISysCommonService sysCommonService;

    @Autowired
    private TokenService tokenService;

    @Override
    public String login(SsoLoginBodyDTO body) {
        String code = body.getCode();
        WechatAuthenticationToken authentication = null;
        try {
            WechatAuthenticationToken authenticationToken = new WechatAuthenticationToken(code, body.getAppId());
            AuthenticationContextHolder.setContext(authenticationToken);
            authentication = (WechatAuthenticationToken) authenticationManager.authenticate(authenticationToken);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        } finally {
            AuthenticationContextHolder.clearContext();
        }
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        loginUser.setTerminal(body.getTerminal());
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(loginUser.getUsername(), Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        sysCommonService.recordLoginInfo(loginUser.getUserId());

        // 生成token
        return tokenService.createToken(loginUser);
    }
}
