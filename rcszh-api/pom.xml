<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.rcszh</groupId>
        <artifactId>pms</artifactId>
        <version>0.0.1</version>
    </parent>

    <artifactId>rcszh-api</artifactId>
    <name>API模块</name>
    <version>0.0.1</version>
    <description>
        API模块
    </description>
    <dependencies>
        <dependency>
            <groupId>com.rcszh</groupId>
            <artifactId>rcszh-base-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.rcszh</groupId>
            <artifactId>rcszh-common</artifactId>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-models</artifactId>
        </dependency>
    </dependencies>
</project>
