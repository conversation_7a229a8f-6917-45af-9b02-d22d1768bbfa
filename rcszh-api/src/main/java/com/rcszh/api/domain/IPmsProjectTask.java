package com.rcszh.api.domain;

import com.rcszh.common.domain.IBaseDomain;

import java.math.BigDecimal;
import java.util.Date;

public interface IPmsProjectTask extends IBaseDomain {
    Long getId();

    void setId(Long id);

    String getIsUsed();

    void setIsUsed(String isUsed);

    String getCode();

    void setCode(String code);

    String getName();

    void setName(String name);

    String getTaskAbbr();

    void setTaskAbbr(String taskAbbr);

    String getStatus();

    void setStatus(String status);

    String getStatusName();

    void setStatusName(String statusName);

    Integer getStatusFlag();

    void setStatusFlag(Integer statusFlag);

    BigDecimal getProgress();

    void setProgress(BigDecimal progress);

    BigDecimal getPersonalProgress();

    void setPersonalProgress(BigDecimal personalProgress);

    Date getPlannedStartDate();

    void setPlannedStartDate(Date plannedStartDate);

    String[] getPlannedStartDateArray();

    void setPlannedStartDateArray(String[] plannedStartDateArray);

    Date getPlannedEndDate();

    void setPlannedEndDate(Date plannedEndDate);

    Date getActualStartDate();

    void setActualStartDate(Date actualStartDate);

    Date getActualEndDate();

    void setActualEndDate(Date actualEndDate);

    Integer getWorkDays();

    void setWorkDays(Integer workDays);

    Integer getActualWorkDays();

    void setActualWorkDays(Integer actualWorkDays);

    Long getOwner();

    void setOwner(Long owner);

    String getOwnerName();

    void setOwnerName(String ownerName);

    String getTaskType();

    void setTaskType(String taskType);

    BigDecimal getPlanWeight();

    void setPlanWeight(BigDecimal planWeight);

    String getMilestone();

    void setMilestone(String milestone);

    String getMilestoneName();

    void setMilestoneName(String milestoneName);

    String getMilestoneStatus();

    void setMilestoneStatus(String milestoneStatus);

    String getMilestoneStatusName();

    void setMilestoneStatusName(String milestoneStatusName);

    BigDecimal getEstimatedHours();

    void setEstimatedHours(BigDecimal estimatedHours);

    BigDecimal getActualHours();

    void setActualHours(BigDecimal actualHours);

    String getTaskDescription();

    void setTaskDescription(String taskDescription);

    Long getProjectId();

    void setProjectId(Long projectId);

    String getProjectCode();

    void setProjectCode(String projectCode);

    String getProjectName();

    void setProjectName(String projectName);

    Long getParentId();

    void setParentId(Long parentId);

    String getAncestors();

    void setAncestors(String ancestors);

    String getAncestorsName();

    void setAncestorsName(String ancestorsName);

    boolean isLeaf();

    void setLeaf(boolean isLeaf);

    Long getSerialNumber();

    void setSerialNumber(Long serialNumber);

//    List<IPmsProjectTaskUser> getUserList();
//
//    void setUserList(List<IPmsProjectTaskUser> userList);

    Long getDaysSince();

    void setDaysSince(Long daysSince);

    String getDaysSinceFlag();

    void setDaysSinceFlag(String daysSinceFlag);

    String getDaysSinceMessage();

    void setDaysSinceMessage(String daysSinceMessage);

    String getUserStr();

    void setUserStr(String userStr);

    Long getActualDaysSince();

    void setActualDaysSince(Long actualDaysSince);

    String getActualDaysSinceFlag();

    void setActualDaysSinceFlag(String actualDaysSinceFlag);

    Long getProjectManagerId();

    void setProjectManagerId(Long projectManagerId);

    String getProjectManagerName();

    void setProjectManagerName(String projectManagerName);
}
