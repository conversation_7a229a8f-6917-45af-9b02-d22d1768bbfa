package com.rcszh.api.domain;

import com.rcszh.common.domain.IBaseDomain;

import java.math.BigDecimal;
import java.util.Date;

public interface IPmsProjectTaskUserRecord extends IBaseDomain {
    String getId();

    void setId(String id);

    String getTaskUserId();

    void setTaskUserId(String taskUserId);

    String getApprovalStatus();

    void setApprovalStatus(String approvalStatus);

    String getTaskUserStatus();

    void setTaskUserStatus(String taskUserStatus);

    BigDecimal getProgress();

    void setProgress(BigDecimal progress);

    BigDecimal getProgressDiff();

    void setProgressDiff(BigDecimal progressDiff);

    Date getActualStartDate();

    void setActualStartDate(Date actualStartDate);

    Date getActualEndDate();

    void setActualEndDate(Date actualEndDate);

    BigDecimal getUsedHours();

    void setUsedHours(BigDecimal usedHours);

    BigDecimal getRemainHours();

    void setRemainHours(BigDecimal remainHours);

    Date getApplyDate();

    void setApplyDate(Date applyDate);

    String getRemark();

    void setRemark(String remark);
}