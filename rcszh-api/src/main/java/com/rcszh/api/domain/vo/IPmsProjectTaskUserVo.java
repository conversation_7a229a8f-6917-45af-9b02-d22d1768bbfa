package com.rcszh.api.domain.vo;

import cn.hutool.json.JSONObject;
import com.rcszh.api.domain.IPmsProject;
import com.rcszh.api.domain.IPmsProjectTask;
import com.rcszh.api.domain.IPmsProjectTaskUser;

import java.io.Serializable;

/**
 * 项目任务成员对象展示接口 pms_project_task_user_vo
 *
 * <AUTHOR>
 * @date 2024-12-04
 */
public interface IPmsProjectTaskUserVo extends Serializable {

    /**
     * 获取项目
     *
     * @return 项目
     */
    IPmsProject getProject();

    /**
     * 设置项目
     *
     * @param project 项目
     */
    void setProject(IPmsProject project);

//    /**
//     * 获取里程碑
//     *
//     * @return 里程碑
//     */
//    List<IPmsProjectTask> getTaskMilestones();
//
//    /**
//     * 设置里程碑
//     *
//     * @param taskMilestones 里程碑
//     */
//    void setTaskMilestones(List<IPmsProjectTask> taskMilestones);

    /**
     * 获取工时信息
     *
     * @return 工时信息
     */
    JSONObject getTask();

    /**
     * 设置工时信息
     *
     * @param task 工时信息
     */
    void setTask(JSONObject task);

    /**
     * 获取任务信息
     *
     * @return 任务信息
     */
    IPmsProjectTask getTaskInfo();

    /**
     * 设置任务信息
     *
     * @param taskInfo 任务信息
     */
    void setTaskInfo(IPmsProjectTask taskInfo);

//    /**
//     * 获取任务资源
//     *
//     * @return 任务资源
//     */
//    List<IPmsProjectTaskUser> getTaskResources();
//
//    /**
//     * 设置任务资源
//     *
//     * @param taskResources 任务资源
//     */
//    void setTaskResources(List<IPmsProjectTaskUser> taskResources);

    /**
     * 获取个人任务
     *
     * @return 个人任务
     */
    IPmsProjectTaskUser getMyTask();

    /**
     * 设置个人任务
     *
     * @param myTask 个人任务
     */
    void setMyTask(IPmsProjectTaskUser myTask);

//    /**
//     * 获取更新记录
//     *
//     * @return 更新记录
//     */
//    List<IPmsProjectTaskUserRecord> getUpdateRecords();
//
//    /**
//     * 设置更新记录
//     *
//     * @param updateRecords 更新记录
//     */
//    void setUpdateRecords(List<IPmsProjectTaskUserRecord> updateRecords);
}