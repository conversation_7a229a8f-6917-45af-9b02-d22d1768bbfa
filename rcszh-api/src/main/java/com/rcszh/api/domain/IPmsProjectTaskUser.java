package com.rcszh.api.domain;

import com.rcszh.common.domain.IBaseDomain;

import java.math.BigDecimal;
import java.util.Date;

public interface IPmsProjectTaskUser extends IBaseDomain {
    String getId();

    void setId(String id);

    Long getTaskId();

    void setTaskId(Long taskId);

    Long getUserId();

    void setUserId(Long userId);

    String getUserName();

    void setUserName(String userName);

    String getNickName();

    void setNickName(String nickName);

    BigDecimal getWeight();

    void setWeight(BigDecimal weight);

    BigDecimal getEstimatedHours();

    void setEstimatedHours(BigDecimal estimatedHours);

    BigDecimal getProgress();

    void setProgress(BigDecimal progress);

    String getTaskUserStatus();

    void setTaskUserStatus(String taskUserStatus);

    Date getPlannedStartDate();

    void setPlannedStartDate(Date plannedStartDate);

    Date getPlannedEndDate();

    void setPlannedEndDate(Date plannedEndDate);

    Date getActualStartDate();

    void setActualStartDate(Date actualStartDate);

    Date getActualEndDate();

    void setActualEndDate(Date actualEndDate);

    String getRemark();

    void setRemark(String remark);
}