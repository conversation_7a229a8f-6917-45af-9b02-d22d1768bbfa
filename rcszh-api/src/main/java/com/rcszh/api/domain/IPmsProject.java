package com.rcszh.api.domain;

import com.rcszh.common.domain.IBaseDomain;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public interface IPmsProject extends IBaseDomain {

    // 主键字段
    Long getId();

    void setId(Long id);

    // 项目基本信息
    String getCode();

    void setCode(String code);

    String getName();

    void setName(String name);

    Long getAssociatedProjectId();

    void setAssociatedProjectId(Long associatedProjectId);

    String getAssociatedProjectName();

    void setAssociatedProjectName(String associatedProjectName);

    String getProjectAbbr();

    void setProjectAbbr(String projectAbbr);

    String getCategoryCode();

    void setCategoryCode(String categoryCode);

    String getCategoryName();

    void setCategoryName(String categoryName);

    String getProjectStatus();

    void setProjectStatus(String projectStatus);

    String getProjectStatusName();

    void setProjectStatusName(String projectStatusName);

    String getProjectMode();

    void setProjectMode(String projectMode);

    String getProjectModeName();

    void setProjectModeName(String projectModeName);

    // 项目归属人员
    Long getProjectManagerId();

    void setProjectManagerId(Long projectManagerId);

    String getProjectManagerName();

    void setProjectManagerName(String projectManagerName);

    Long getProjectSupervisorId();

    void setProjectSupervisorId(Long projectSupervisorId);

    String getProjectSupervisorName();

    void setProjectSupervisorName(String projectSupervisorName);

    Long getProjectAssistantId();

    void setProjectAssistantId(Long projectAssistantId);

    String getProjectAssistantName();

    void setProjectAssistantName(String projectAssistantName);

    Long getProjectAssistantId2();

    void setProjectAssistantId2(Long projectAssistantId2);

    String getProjectAssistantId2Name();

    void setProjectAssistantId2Name(String projectAssistantId2Name);

    Long getProjectAssistantId3();

    void setProjectAssistantId3(Long projectAssistantId3);

    String getProjectAssistantId3Name();

    void setProjectAssistantId3Name(String projectAssistantId3Name);

    Long getProjectAssistantId4();

    void setProjectAssistantId4(Long projectAssistantId4);

    String getProjectAssistantId4Name();

    void setProjectAssistantId4Name(String projectAssistantId4Name);

    Long getProjectAssistantId5();

    void setProjectAssistantId5(Long projectAssistantId5);

    String getProjectAssistantId5Name();

    void setProjectAssistantId5Name(String projectAssistantId5Name);

    Long getSalespersonId();

    void setSalespersonId(Long salespersonId);

    String getSalespersonName();

    void setSalespersonName(String salespersonName);

    // 时间计划
    Date getPlannedStartAt();

    void setPlannedStartAt(Date plannedStartAt);

    Date getPlannedFinishBy();

    void setPlannedFinishBy(Date plannedFinishBy);

    Date getActualStartDate();

    void setActualStartDate(Date actualStartDate);

    Date getActualEndDate();

    void setActualEndDate(Date actualEndDate);

    // 地理位置
    String getProvince();

    void setProvince(String province);

    String getProvinceName();

    void setProvinceName(String provinceName);

    String getCity();

    void setCity(String city);

    String getCityName();

    void setCityName(String cityName);

    String getArea();

    void setArea(String area);

    String getAreaName();

    void setAreaName(String areaName);

    String getAddress();

    void setAddress(String address);

    // 项目金额
    String getCurrencyCode();

    void setCurrencyCode(String currencyCode);

    BigDecimal getRate();

    void setRate(BigDecimal rate);

    BigDecimal getContractAmount();

    void setContractAmount(BigDecimal contractAmount);

    BigDecimal getTaxRate();

    void setTaxRate(BigDecimal taxRate);

    BigDecimal getAmountExcludingTax();

    void setAmountExcludingTax(BigDecimal amountExcludingTax);

    // 描述
    String getDescription();

    void setDescription(String description);

    // 关联实体
    String getCustomerId();

    void setCustomerId(String customerId);

    String getCustomerName();

    void setCustomerName(String customerName);

    String getSupplierId();

    void setSupplierId(String supplierId);

    String getSupplierName();

    void setSupplierName(String supplierName);

    Long getCompanyId();

    void setCompanyId(Long companyId);

    String getCompanyName();

    void setCompanyName(String companyName);

    // 业务标识字段
    Integer getStatusFlag();

    void setStatusFlag(Integer statusFlag);

    Integer getDimensionFlag();

    void setDimensionFlag(Integer dimensionFlag);

    BigDecimal getWorkHoursRate();

    void setWorkHoursRate(BigDecimal workHoursRate);

    Long getDaysSince();

    void setDaysSince(Long daysSince);

    String getDaysSinceFlag();

    void setDaysSinceFlag(String daysSinceFlag);

    String getDaysSinceMessage();

    void setDaysSinceMessage(String daysSinceMessage);

    BigDecimal getProgress();

    void setProgress(BigDecimal progress);

    Integer getWorkDuration();

    void setWorkDuration(Integer workDuration);

    String getUserStatus();

    void setUserStatus(String userStatus);

    List<Date> getEntryDates();

    void setEntryDates(List<Date> entryDates);

    Date getStartTime();

    void setStartTime(Date startTime);

    Date getEndTime();

    void setEndTime(Date endTime);

    Long getUserId();

    void setUserId(Long userId);

    String getCreateByName();

    void setCreateByName(String createByName);

    List<Long> getProjectIds();

    void setProjectIds(List<Long> projectIds);

    Long getTaskId();

    void setTaskId(Long taskId);

    BigDecimal getActualWorkHours();

    void setActualWorkHours(BigDecimal actualWorkHours);

//    List<IPmsProjectUser> getUsers();
//    void setUsers(List<IPmsProjectUser> users);

    List<Object> getProjectFiles();

    void setProjectFiles(List<Object> projectFiles);
}