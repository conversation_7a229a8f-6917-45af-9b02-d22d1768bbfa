package com.rcszh.api.domain;

import com.rcszh.common.domain.IBaseDomain;

import java.util.List;

public interface IPmsProjectUser extends IBaseDomain {
    Long getId();

    void setId(Long id);

    Long getProjectId();

    void setProjectId(Long projectId);

    Long getGroupLeaderId();

    void setGroupLeaderId(Long groupLeaderId);

    String getGroupLeaderName();

    void setGroupLeaderName(String groupLeaderName);

    String getProjectRole();

    void setProjectRole(String projectRole);

    Long getUserId();

    void setUserId(Long userId);

    String getUserName();

    void setUserName(String userName);

    String getNickName();

    void setNickName(String nickName);

    String getDeptName();

    void setDeptName(String deptName);

    String getStatus();

    void setStatus(String status);

    String getProjectRoleName();

    void setProjectRoleName(String projectRoleName);

    boolean isDisabled();

    void setDisabled(boolean disabled);

    Long getDeptId();

    void setDeptId(Long deptId);

    List<Long> getFilteredUserIdList();

    void setFilteredUserIdList(List<Long> filteredUserIdList);

    List<Long> getFilterRoleList();

    void setFilterRoleList(List<Long> filterRoleList);

    Boolean getIsLeader();

    void setIsLeader(Boolean isLeader);
}