package com.rcszh.api.service;

import cn.hutool.json.JSONObject;
import com.rcszh.api.domain.EqptProjectListDto;
import com.rcszh.api.domain.IPmsProject;

import java.util.List;

public interface IPmsProjectBusinessService {

    JSONObject getInfo(Long id);

    /**
     * 删除项目
     *
     * @param ids
     */
    void delProject(Long[] ids);

    /**
     * 我的项目--工时填报
     *
     * @param myProject
     * @return
     */
    List<IPmsProject> myProject(IPmsProject myProject);

    /**
     * 管理的项目
     *
     * @param pmsProject
     * @return
     */
    List<Object> getManagedProjects(IPmsProject pmsProject);


    List<IPmsProject> allEqptProject(EqptProjectListDto eqptProjectListDto);
}
