package com.rcszh.base.common.utils.excel;

import lombok.Data;

import java.io.Serializable;

@Data
public class ExcelModelDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 排序
     */
    private int order;
    /**
     * 是否为null
     */
    private boolean isNotNull;

    /**
     * 字段
     */
    private String field;

    /**
     * 字段名
     */
    private String name;

    /**
     * 字段排序
     */
    private String description;

    /**
     * 是否为null
     */
    private boolean required;

    /**
     * 后缀名
     */
    private String suffix;

    /**
     * 日期格式（如果字段是日期类型）
     */
    private String dateFormat;

    /**
     * 百分比格式
     */
    private String percentageFormat;

    /**
     * 增的对齐方式配置，默认是 CENTER
     */
    private String alignment;

    /**
     * 字典
     */
    private String dictType;

    /**
     * 下拉框字典
     */
    private String selectDictType;


    /**
     * 字段名
     */
    private String fieldName;

    /**
     * 字段排序
     */
    private String fieldDesp;


}
