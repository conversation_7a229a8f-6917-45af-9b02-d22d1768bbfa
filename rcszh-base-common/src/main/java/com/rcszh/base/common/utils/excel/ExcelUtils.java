package com.rcszh.base.common.utils.excel;

import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
public class ExcelUtils<T> {

    /**
     * 实体对象
     */
    public Class<T> clazz;

    public ExcelUtils(Class<T> clazz) {
        this.clazz = clazz;
    }

    /**
     * 从 Excel 导入数据
     *
     * @param clazz       目标数据类型
     * @param inputStream Excel 文件的输入流
     * @return 导入的对象列表
     * @throws Exception 如果发生异常
     */
    public static <T> List<T> importExcel(InputStream inputStream, Class<T> clazz) throws Exception {
        return ExcelToolClass.importExcel(clazz, inputStream);
    }

    /**
     * 从 Excel 导入数据
     *
     * @param clazz         目标数据类型
     * @param multipartFile Excel 文件对象
     * @return 导入的对象列表
     * @throws Exception 如果发生异常
     */
    public static <T> List<T> importExcel(MultipartFile multipartFile, Class<T> clazz) throws Exception {
        return ExcelToolClass.importExcel(clazz, multipartFile.getInputStream());
    }

    /**
     * 导出 Excel 文件
     *
     * @param fileName Excel文件名
     * @param data     导出数据列表
     * @param response HttpServletResponse，响应输出流
     * @throws Exception 如果发生异常
     */
    public void exportExcel(HttpServletResponse response, List<T> data, String fileName) throws Exception {
        ExcelToolClass.exportExcel(fileName, data, response, clazz);
    }

    /**
     * 模板下载
     *
     * @param response
     * @param tClass
     * @param <T>
     */
    public static <T> void downloadModelExcel(HttpServletResponse response, Class<T> tClass) {
        ExcelToolClass.downloadModelExcel(response, tClass);
    }

    /**
     * 将错误信息写入Excel文件并通过流返回。
     *
     * @param file     上传的Excel文件
     * @param excelErr 错误信息，Key为行号，Value为错误信息
     * @return InputStream 返回修改后的Excel文件流
     * @throws IOException
     */
    public static InputStream writeErrMessageToExcel(MultipartFile file, Map<Integer, String> excelErr) throws IOException {
        return ExcelToolClass.writeErrMessageToExcel(file, excelErr);
    }

    public static InputStream writeErrMessageToExcel(MultipartFile file, Map<Integer, String> excelErr, int titleRowNum) throws IOException {
        return ExcelToolClass.writeErrMessageToExcel(file, excelErr, titleRowNum);
    }


}
