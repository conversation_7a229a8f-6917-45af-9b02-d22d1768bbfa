package com.rcszh.base.common.utils.excel;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelWriter;
import com.rcszh.base.common.core.domain.entity.SysDictData;
import com.rcszh.base.common.exception.ServiceException;
import com.rcszh.base.common.utils.DictUtils;
import com.rcszh.base.common.utils.MessageUtils;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class ExcelToolClass {

    private static Workbook workbook = null;

    /**
     * 从 Excel 导入数据
     *
     * @param clazz 目标数据类型
     * @param inputStream Excel 文件的输入流
     * @param <T> 目标类型
     * @return 导入的对象列表
     * @throws Exception 如果发生异常
     */
    public static <T> List<T> importExcel(Class<T> clazz, InputStream inputStream) throws Exception {
        // 读取 Excel 文件
        ExcelReader reader = new ExcelReader(inputStream, 0);
        List<List<Object>> rows = reader.read();
        if (CollUtil.isEmpty(rows)) {
            throw new ServiceException("文件无数据,导入失败");
        }

        // 获取字段注解配置
        List<ExcelModelDTO> fieldConfigs = getFieldConfigs(clazz);

        // 按照字段配置顺序处理数据
        List<Object> headList = rows.get(0);
        Map<String, ExcelModelDTO> modelMap = fieldConfigs.stream().collect(Collectors.toMap(ExcelModelDTO::getName, t -> t, (key1, key2) -> key1));
        List<T> dataList = new ArrayList<>();
        for (int i = 1; i < rows.size(); i++) { // 从第二行开始读取数据
            List<Object> row = rows.get(i);
            T obj = clazz.getDeclaredConstructor().newInstance();

            for (int j = 0; j < row.size(); j++) {
                Object value = row.get(j);
                Object header = headList.get(j);

                // 获取字段配置并映射到对象
                ExcelModelDTO fieldConfig = modelMap.get(Convert.toStr(header));
                if (Objects.isNull(fieldConfig)) {
                    continue;
                }
//                ExcelModelDTO fieldConfig = fieldConfigs.get(j);
                Field field = clazz.getDeclaredField(fieldConfig.getField());
                field.setAccessible(true);
                // 获取字段类型
                Class<?> fieldType = field.getType();

                // 判断是否能转换为目标字段类型，如果不能，则默认为 null
                Object convertedValue = convertValue(value, fieldType);
                // 设置值
                field.set(obj, convertedValue);
            }
            dataList.add(obj);
        }
        return dataList;
    }

    /**
     * 类型转换方法，如果 value 与目标类型不匹配则返回 null。
     */
    private static Object convertValue(Object value, Class<?> targetType) {
        if (value == null) {
            return null;
        }

        // 判断目标类型
        if (targetType.isAssignableFrom(value.getClass())) {
            if (targetType == String.class){
                if (StrUtil.isBlank(Convert.toStr(value))){
                    return null;
                }
            }
            return value;
        }

        try {
            if (targetType == Integer.class || targetType == int.class) {
                return Convert.toInt(value);
            } else if (targetType == Long.class || targetType == long.class) {
                return Convert.toLong(value);
            } else if (targetType == Double.class || targetType == double.class) {
                return Convert.toDouble(value);
            } else if (targetType == Float.class || targetType == float.class) {
                return Convert.toFloat(value);
            } else if (targetType == Boolean.class || targetType == boolean.class) {
                return Convert.toBool(value);
            } else if (targetType == String.class) {
                return Convert.toStr(value);
            } else if (targetType == Date.class) {
                // 假设值是字符串，需要转为日期
                return DateUtil.parse(Convert.toStr(value), DatePattern.NORM_DATETIME_PATTERN);
            } else if(targetType == BigDecimal.class){
                return Convert.toBigDecimal(value);
            }else {
                return null;  // 对于不支持的类型返回 null
            }
        } catch (Exception e) {
            // 如果转换失败，返回 null
            return null;
        }
    }

    /**
     * 导出 Excel 文件
     *
     * @param fileName Excel文件名
     * @param data     导出数据列表
     * @param response HttpServletResponse，响应输出流
     * @throws Exception 如果发生异常
     */
    public static <T> void exportExcel(String fileName, List<T> data, HttpServletResponse response, Class<T> tClass) throws Exception {
        // 获取字段注解配置
        List<ExcelModelDTO> fieldConfigs = getFieldConfigs(tClass);

        // 创建ExcelWriter实例
        ExcelWriter writer = new ExcelWriter(true);
        Workbook workbook = writer.getWorkbook();
        workbook.removeSheetAt(workbook.getSheetIndex("sheet1"));

        // 创建Sheet1 (数据)
        Sheet dataSheet = workbook.createSheet("数据");
        createHeader(dataSheet, fieldConfigs, workbook);
        fillData(dataSheet, data, fieldConfigs, workbook);

        // 设置响应头以下载文件
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename*=utf-8'zh_cn'" + URLEncoder.encode(fileName + ".xlsx", StandardCharsets.UTF_8));

        // 将Excel写入输出流
        writer.flush(response.getOutputStream(), true);
        writer.close();
    }

    /**
     * 填充数据（Sheet1）
     *
     * @param sheet       Excel Sheet
     * @param data        数据列表
     * @param fieldConfigs 字段配置
     */
    private static <T> void fillData(Sheet sheet, List<T> data, List<ExcelModelDTO> fieldConfigs, Workbook workbook) throws Exception {
        int rowIndex = 1;

        // 记录每列的最大宽度（包括表头和数据）
        int[] columnWidths = new int[fieldConfigs.size()];

        for (Object item : data) {
            Row row = sheet.createRow(rowIndex++);
            int colIndex = 0;

            for (ExcelModelDTO config : fieldConfigs) {
                Field field = item.getClass().getDeclaredField(config.getField());
                field.setAccessible(true);

                Object fieldValue = field.get(item);
                CellStyle style = createCellStyle(config.getAlignment(), workbook);

                Font font = workbook.createFont();
                font.setFontName("Arial");
                font.setFontHeightInPoints((short) 10);
                style.setFont(font);
                Cell cell = row.createCell(colIndex++);

                if (fieldValue != null) {
                    String formattedValue = formatFieldValue(fieldValue, config);
                    cell.setCellValue(formattedValue);

                    // 更新列宽
//                    columnWidths[colIndex - 1] = Math.max(columnWidths[colIndex - 1], formattedValue.getBytes().length > config.getName().getBytes().length ? formattedValue.getBytes().length : config.getName().getBytes().length);
                } else {
                    cell.setCellValue("");  // 如果值为null，设置为空
                    // 更新列宽
//                    columnWidths[colIndex - 1] = Math.max(columnWidths[colIndex - 1], config.getName().getBytes().length);
                }

                cell.setCellStyle(style);
            }
        }

//        // 调整列宽（表头与数据同时考虑）
//        for (int i = 0; i < fieldConfigs.size(); i++) {
//            sheet.setColumnWidth(i, (columnWidths[i] + 2) * 256);  // 加2是为了增加一些空间
//        }
    }

    /**
     * 格式化字段值（处理日期、百分比、后缀名等）
     *
     * @param fieldValue 字段值
     * @param config     字段配置
     * @return 格式化后的字段值
     */
    private static String formatFieldValue(Object fieldValue, ExcelModelDTO config) {
        // 如果是日期类型，格式化为指定的日期格式
        if (fieldValue instanceof Date) {
            if (StrUtil.isNotBlank(config.getDateFormat())) {
                return DateUtil.format(Convert.toDate(fieldValue), config.getDateFormat());
            }
        }

        // 如果是数字类型，检查是否需要按百分比格式化
        if (fieldValue instanceof Number) {
            // 如果配置了百分比格式
            if (StrUtil.isNotBlank(config.getPercentageFormat())) {
                DecimalFormat percentageFormat = new DecimalFormat(config.getPercentageFormat());
                return percentageFormat.format(fieldValue);
            }
        }

        // 添加后缀名（例如：文件大小，金额单位等）
        String valueString = fieldValue.toString();
        if (StrUtil.isNotBlank(config.getSuffix())) {
            valueString += config.getSuffix();
        }

        if (StrUtil.isNotBlank(config.getDictType())){
            String dictLabel = DictUtils.getDictLabel(config.getDictType(), valueString);
            valueString = dictLabel;
        }

        return valueString;
    }

    /**
     * 创建表头（Sheet1）
     *
     * @param sheet       Excel Sheet
     * @param fieldConfigs 字段配置
     * @param workbook    Workbook对象
     */
    private static void createHeader(Sheet sheet, List<ExcelModelDTO> fieldConfigs, Workbook workbook) {
        Row headerRow = sheet.createRow(0);
        int colIndex = 0;

        // 记录每列的最大宽度（仅根据表头）
        int[] columnWidths = new int[fieldConfigs.size()];

        // 创建表头列
        for (ExcelModelDTO config : fieldConfigs) {
            CellStyle style = createCellStyle(config.getAlignment(), workbook);
            style.setFont(createBoldFont(workbook));

            Cell cell = headerRow.createCell(colIndex++);
            cell.setCellValue(config.getName());
            cell.setCellStyle(style);

            // 仅根据表头内容计算列宽
//            columnWidths[colIndex - 1] = Math.max(columnWidths[colIndex - 1], config.getName().getBytes().length);
        }

//        // 根据最大内容长度调整列宽，确保表头和数据都考虑在内
//        for (int i = 0; i < fieldConfigs.size(); i++) {
//            sheet.setColumnWidth(i, (columnWidths[i] + 2) * 256);  // 加2是为了增加一些空间
//        }
    }

    /**
     * 获取字段注解配置
     *
     * @param clazz 数据对象的类
     * @return 按顺序排列的字段配置列表
     */
    public static <T> List<ExcelModelDTO> getFieldConfigs(Class<T> clazz) {
        List<ExcelModelDTO> fieldConfigs = new ArrayList<>();

        // 获取类中的所有字段
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            if (field.isAnnotationPresent(ExcelModelHelper.class)) {
                ExcelModelHelper annotation = field.getAnnotation(ExcelModelHelper.class);

                // 创建字段配置对象，并填充注解信息
                ExcelModelDTO config = new ExcelModelDTO();
                config.setOrder(annotation.order());
                config.setName(MessageUtils.messageKey(annotation.name()));
                config.setField(field.getName());
                config.setDescription(annotation.description());
                config.setRequired(annotation.required());
                config.setSuffix(annotation.suffix());
                config.setPercentageFormat(annotation.percentageFormat());
                config.setDateFormat(annotation.dateFormat());
                config.setAlignment(annotation.alignment());
                config.setSelectDictType(annotation.selectDictType());
                fieldConfigs.add(config);
            }
        }

        // 按照order排序字段配置
        fieldConfigs.sort(Comparator.comparingInt(ExcelModelDTO::getOrder));
        return fieldConfigs;
    }

    /**
     * 模板下载
     * @param response
     * @param tClass
     * @param <T>
     */
    public static <T> void downloadModelExcel(HttpServletResponse response, Class<T> tClass) {
        ExcelModelName excelModelName = tClass.getAnnotation(ExcelModelName.class);
        String modelName = excelModelName.name();
        List<ExcelModelDTO> excelModelDTOS = ExcelToolClass.getFieldConfigs(tClass);
        try {
            createExcelModel(modelName, excelModelDTOS, response);
        } catch (Exception e) {
            log.error("模板创建出错,错误消息:{}", e.getMessage());
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 创建单元格样式
     *
     * @param alignment 对齐方式
     * @param workbook  Workbook对象
     * @return CellStyle
     */
    private static CellStyle createCellStyle(String alignment, Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setDataFormat(workbook.createDataFormat().getFormat("@"));
        style.setAlignment(getAlignment(alignment));
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        return style;
    }

    /**
     * 获取对齐方式
     *
     * @param alignment 对齐方式字符串
     * @return HorizontalAlignment
     */
    private static HorizontalAlignment getAlignment(String alignment) {
        if ("LEFT".equalsIgnoreCase(alignment)) {
            return HorizontalAlignment.LEFT;
        } else if ("RIGHT".equalsIgnoreCase(alignment)) {
            return HorizontalAlignment.RIGHT;
        }
        return HorizontalAlignment.CENTER;  // 默认居中
    }

    /**
     * 创建加粗字体
     *
     * @param workbook Workbook对象
     * @return Font
     */
    private static Font createBoldFont(Workbook workbook) {
        Font font = workbook.createFont();
        font.setFontName("Arial");
        font.setFontHeightInPoints((short) 11);
        font.setBold(true);
        return font;
    }

    /**
     * 创建excel文件
     *
     * @param fileName
     * @param dtos
     * @param response
     * @throws Exception
     */
    public static void createExcelModel(String fileName, List<ExcelModelDTO> dtos, HttpServletResponse response) throws Exception {
        ExcelWriter ew = new ExcelWriter(true);
        workbook = ew.getWorkbook();
        workbook.removeSheetAt(workbook.getSheetIndex("sheet1"));
        Sheet dataSheet = workbook.createSheet("数据");
        Row row = dataSheet.createRow(0);
        int i = 0;
        // 记录每列的最大宽度（仅根据表头）
        int[] columnWidths = new int[dtos.size()];
        for (ExcelModelDTO modelDTO : dtos) {
            CellStyle style = createCellStyle(modelDTO.getAlignment(), workbook);
            if (modelDTO.isRequired()) {
                style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                style.setFillForegroundColor(IndexedColors.RED.getIndex());
            }

            Cell cell = row.createCell(i);
            cell.setCellValue(modelDTO.getName());
            cell.setCellStyle(style);
            i++;

            // 仅根据表头内容计算列宽
            columnWidths[i - 1] = Math.max(columnWidths[i - 1], modelDTO.getName().getBytes().length);

            if (StrUtil.isNotBlank(modelDTO.getSelectDictType())){
                // 创建下拉框数据验证规则
                List<SysDictData> dictCache = DictUtils.getDictCache(modelDTO.getSelectDictType());
                List<String> collect = dictCache.stream().map(SysDictData::getDictLabel).collect(Collectors.toList());
                String[] options = collect.toArray(String[]::new);

                // 创建下拉框的区域
                DataValidationHelper validationHelper = dataSheet.getDataValidationHelper();
                DataValidationConstraint constraint = validationHelper.createExplicitListConstraint(options);

                CellRangeAddressList addressList = new CellRangeAddressList(1, 65535, i - 1, i - 1);  // 1,65535 表示所有行，第2列是列索引1

                // 创建数据验证
                DataValidation dataValidation = validationHelper.createValidation(constraint, addressList);
                dataSheet.addValidationData(dataValidation);
            }
        }
        // 根据最大内容长度调整列宽，确保表头和数据都考虑在内
        for (int j = 0; j < dtos.size(); j++) {
            dataSheet.setColumnWidth(j, (columnWidths[j] + 2) * 256);  // 加2是为了增加一些空间
        }

        Sheet dataSheet2 = workbook.createSheet("注意事项");
        int j = 3;
        for (ExcelModelDTO modelDTO : dtos) {
            DataFormat format = workbook.createDataFormat();
            CellStyle style = workbook.createCellStyle();
            style.setDataFormat(format.getFormat("@"));
            Row totleRow = dataSheet2.createRow(1);
            Cell cellTotal = totleRow.createCell(1);
            cellTotal.setCellValue("导入模板说明");
            cellTotal.setCellStyle(style);
            Cell cellTotal1 = totleRow.createCell(2);
            cellTotal1.setCellValue("红色字段为必填字段");
            cellTotal1.setCellStyle(style);
            if (StrUtil.isNotBlank(modelDTO.getDescription())) {
                Row dataRow = dataSheet2.createRow(j);
                Cell cell = dataRow.createCell(1);
                cell.setCellValue(modelDTO.getName());
                cell.setCellStyle(style);
                Cell cell2 = dataRow.createCell(2);
                cell2.setCellValue(modelDTO.getDescription());
                cell2.setCellStyle(style);
                j++;
            }
        }
        response.addHeader("Content-Type", "application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename*=utf-8'zh_cn'" + URLEncoder.encode(fileName + ".xlsx", StandardCharsets.UTF_8));
        workbook.write(response.getOutputStream());
    }

    /**
     * 将错误信息写入Excel文件并通过流返回。
     *
     * @param file      上传的Excel文件
     * @param excelErr  错误信息，Key为行号，Value为错误信息
     * @return InputStream 返回修改后的Excel文件流
     * @throws IOException
     */
    public static InputStream writeErrMessageToExcel(MultipartFile file, Map<Integer, String> excelErr) throws IOException {
        return ExcelToolClass.writeErrMessageToExcel(file, excelErr, 0);
    }

    public static InputStream writeErrMessageToExcel(MultipartFile file, Map<Integer, String> excelErr, int titleRowNum) throws IOException {
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.isEmpty()) {
            throw new IOException("文件名不能为空");
        }

        // 创建适当的 Workbook 实例，取决于文件的扩展名
        Workbook workbook = null;
        if (originalFilename.endsWith(".xls")) {
            workbook = new HSSFWorkbook(file.getInputStream()); // 处理 .xls 文件
        } else if (originalFilename.endsWith(".xlsx")) {
            workbook = new XSSFWorkbook(file.getInputStream()); // 处理 .xlsx 文件
        } else {
            throw new IOException("文件类型不支持，必须是 .xls 或 .xlsx 文件");
        }

        // 获取第一个 sheet
        Sheet sheet = workbook.getSheetAt(0);

        // 添加错误信息标题
        Row titleRow = sheet.getRow(titleRowNum); // 默认第一行是标题行
        short titleLastCellNum = titleRow.getLastCellNum();
        Cell titleCell = titleRow.createCell(titleLastCellNum); // 获取最后一个单元格
        titleCell.setCellValue("错误信息");


        // 遍历行，检查是否有错误信息需要添加
        for (int rowNum = titleRowNum + 1; rowNum <= sheet.getLastRowNum(); rowNum++) {
            Row row = sheet.getRow(rowNum);
            if (row == null) {
                continue;
            }

            // 如果该行有错误信息，添加到该行的最后一个单元格
            String errorMessage = excelErr.get(rowNum - titleRowNum);
            if (errorMessage != null) {
                Cell cell = row.createCell(titleLastCellNum); // 创建新的单元格
                cell.setCellValue(errorMessage); // 设置错误信息
            }
        }

        // 使用 ByteArrayOutputStream 将修改后的 Excel 文件写入内存
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        workbook.write(byteArrayOutputStream); // 将数据写入输出流

        // 返回文件流
        byte[] modifiedExcelBytes = byteArrayOutputStream.toByteArray();
        return new ByteArrayInputStream(modifiedExcelBytes); // 返回修改后的 Excel 文件流
    }

}
