package com.rcszh.base.common.utils.excel;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD})
public @interface ExcelModelHelper {
    /**
     * 排序
     * @return
     */
    int order() default Integer.MAX_VALUE;

    /**
     * 字段名
     * @return
     */
    String name();

    /**
     * 字段排序
     * @return
     */
    String description() default "";

    /**
     * 是否必填
     * @return
     */
    boolean required() default false;

    /**
     * 后缀名
     * @return
     */
    String suffix() default "";

    /**
     * 日期格式（如果字段是日期类型）
     * @return 日期格式
     */
    String dateFormat() default "yyyy-MM-dd";

    /**
     * 百分比格式
     * @return 百分比格式
     */
    String percentageFormat() default "";

    /**
     * 新增的对齐方式配置，默认是 CENTER
     * @return
     */
    String alignment() default "CENTER";  // "LEFT", "CENTER", "RIGHT"

    /**
     * 字典
     * @return
     */
    String dictType() default "";

    /**
     * 下拉框
     * @return
     */
    String selectDictType() default "";

    /**
     * 字段名
     *
     * @return
     */
    String fieldName() default "";

    /**
     * 字段排序
     *
     * @return
     */
    String fieldDesp() default "";

    /**
     * 是否为null
     *
     * @return
     */
    boolean isNotNull() default false;
}
