package com.rcszh.base.common.utils.excel;


import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.afterturn.easypoi.excel.entity.vo.BaseEntityTypeConstants;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.rcszh.base.common.exception.ServiceException;
import com.rcszh.base.common.utils.ServiceUtils;
import com.rcszh.base.common.utils.bean.BeanUtils;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;

import java.io.IOException;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * excel工具类
 */
public class DyExcelUtils {
    private static final Logger logger = LoggerFactory.getLogger(ExcelUtils.class);

    /**
     * 导出文件
     *
     * @param response
     * @param fileName
     * @param list
     * @param pojoClass
     * @throws IOException
     */
    public static void exportExcel(HttpServletResponse response, String fileName, Collection<?> list, Class<?> pojoClass) throws IOException {
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(), pojoClass, list);
        Sheet sheet1 = workbook.getSheetAt(0);
        sheet1.setDefaultColumnWidth(12800);
        sheet1.setDefaultRowHeight((short) 512);
        response.setCharacterEncoding("UTF-8");
        response.setHeader("content-Type", "application/vnd.ms-excel");
        response.setHeader("Content-Disposition", "attachment;filename*=utf-8'zh_cn'" + URLEncoder.encode(fileName, StandardCharsets.UTF_8) + ".xlsx");
        ServletOutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
    }

    public static void exportExcelToTarget(HttpServletResponse response, String fileName, Collection<?> sourceList, Class<?> targetClass) throws Exception {
        List<Object> targetList = new ArrayList<>(sourceList.size());
        for (Object source : sourceList) {
            Object target = targetClass.newInstance();
            BeanUtils.copyProperties(source, target);
            targetList.add(target);
        }
        //生成excel
        exportExcel(response, fileName, targetList, targetClass);
    }

    /**
     * 通用导出工具
     *
     * @param response
     * @param fileName
     * @param data
     * @param columnTitles
     * @param columnFields
     * @throws IOException
     */
    public static void commonForExportExcel(HttpServletResponse response, String fileName, Collection data, String[] columnTitles, String[] columnFields) throws IOException {
        List<ExcelExportEntity> colList = new ArrayList<>();
//    ExcelExportEntity colNumEntity = new ExcelExportEntity("序号", "nu");
//    colNumEntity.setFormat("isAddIndex");
//    colList.add(colNumEntity);
        Map<String, Object> fieldValue = new HashMap<>(columnFields.length);
        for (Object datum : data) {
            JSONObject jsonObject = JSONUtil.parseObj(datum);
            for (String columnField : columnFields) {
                if (!fieldValue.containsKey(columnField)) {
                    Object o = jsonObject.get(columnField);
                    if (o != null && !JSONUtil.isNull(o)) {
                        fieldValue.put(columnField, o);
                        if (fieldValue.size() == columnFields.length) {
                            break;
                        }
                    }
                }
            }
        }
        for (int i = 0; i < columnFields.length; i++) {
            ExcelExportEntity colEntity = new ExcelExportEntity(columnTitles[i], columnFields[i]);
            Object o = fieldValue.get(columnFields[i]);
            if (o instanceof java.math.BigDecimal || o instanceof Integer || o instanceof Double || o instanceof Float) {
                colEntity.setType(BaseEntityTypeConstants.DOUBLE_TYPE.intValue());
            }
            colList.add(colEntity);
        }
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(null, "数据"), colList, data);
        response.setCharacterEncoding("UTF-8");
        response.setHeader("content-Type", "application/vnd.ms-excel");
        response.setHeader("Content-Disposition", "attachment;filename*=utf-8'zh_cn'" + URLEncoder.encode(fileName, StandardCharsets.UTF_8) + ".xlsx");
        ServletOutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
    }

    /**
     * 下载excel
     *
     * @param response
     * @param context
     * @param dataTypeCode
     * @param modelName
     */
    public static void downloadModelExcel(HttpServletResponse response, ApplicationContext context, String dataTypeCode, String modelName) {
        Class className = ServiceUtils.getAssociateEntityClass(context, dataTypeCode);
        if (StrUtil.isBlank(modelName)) {
            ExcelModelName excelModelName = (ExcelModelName) className.getAnnotation(ExcelModelName.class);
            modelName = excelModelName.name();
        }
        Class superclass = className.getSuperclass();
        Class superSuperClass = superclass.getSuperclass();
        List<Field> superDeclaredFields = new ArrayList<>(Arrays.asList(superclass.getDeclaredFields()));
        List<Field> superSuperDeclaredFields = new ArrayList<>(Arrays.asList(superSuperClass.getDeclaredFields()));
        List<Field> fields = new ArrayList<>(Arrays.asList(className.getDeclaredFields()));
        if (CollUtil.isNotEmpty(superDeclaredFields)) {
            if (CollUtil.isNotEmpty(fields)) {
                superDeclaredFields.addAll(fields);
            }
            if (CollUtil.isNotEmpty(superSuperDeclaredFields)) {
                superDeclaredFields.addAll(superSuperDeclaredFields);
            }
            List<ExcelModelDTO> excelModelDTOS = new ArrayList<>();
            for (Field field : superDeclaredFields) {
                if (!field.isAccessible()) {
                    field.setAccessible(true);
                }
                ExcelModelHelper apiModelProperty = field.<ExcelModelHelper>getAnnotation(ExcelModelHelper.class);
                if (apiModelProperty != null) {
                    ExcelModelDTO dto = new ExcelModelDTO();
                    dto.setField(field.getName());
                    dto.setNotNull(apiModelProperty.isNotNull());
                    dto.setFieldName(apiModelProperty.fieldName());
                    dto.setOrder(apiModelProperty.order());
                    dto.setFieldDesp(apiModelProperty.fieldDesp());
                    excelModelDTOS.add(dto);
                }
            }
            try {
                ExcelToolClass.createExcelModel(modelName, excelModelDTOS, response);
            } catch (Exception e) {
                logger.error("模板{}创建出错", dataTypeCode, e);
                throw new ServiceException(e.getMessage());
            }
        }
    }
}
