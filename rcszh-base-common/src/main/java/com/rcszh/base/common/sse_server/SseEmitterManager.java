package com.rcszh.base.common.sse_server;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

public class SseEmitterManager {
    private static final Map<String, SSESession> emitters = new ConcurrentHashMap<>();
    private static final Logger logger = LoggerFactory.getLogger(SseEmitterManager.class);

    /**
     * 添加 SseEmitter 到管理器
     *
     * @param token   用户
     * @param emitter SseEmitter 实例
     */
    public static void addEmitter(String token, Long userId, SseEmitter emitter) {
        SSESession sseSession = SSESession.builder().token(token).emitter(emitter).userId(userId).build();
        emitters.put(token, sseSession);
    }

    /**
     * 获取在线用户
     */
    public static List<Long> getOnlineUsers() {
        return emitters.values().stream().map(SSESession::getUserId).distinct().collect(Collectors.toList());
    }

    /**
     * 移除 SseEmitter
     */
    public static void removeEmitter(String token) {
        emitters.remove(token);
    }

    /**
     * 获取 SseEmitter
     */
    public static SSESession getEmitter(String token) {
        return emitters.get(token);
    }

    /**
     * 获取所有 SseEmitter
     *
     * @param userId
     * @return
     */
    public static List<SSESession> getEmittersByUserId(Long userId) {
        return emitters.values().stream().filter(sseSession -> sseSession.getUserId().equals(userId)).toList();
    }

    /**
     * 获取所有 SseEmitter
     *
     * @param userId
     * @return
     */
    public static List<SSESession> getEmittersByUserId(List<Long> userId) {
        List<SSESession> sseEmitters = new ArrayList<>();
        for (Long id : userId) {
            sseEmitters.addAll(getEmittersByUserId(id));
        }
        return sseEmitters;
    }

    /**
     * 发送消息给指定用户
     *
     * @param userId  用户 ID
     * @param message 消息内容
     */
    public static void sendMessage(Long userId, String name, String message) {
        List<SSESession> emitterList = getEmittersByUserId(userId);
        for (SSESession emitter : emitterList) {
            try {
                logger.error("给用户：{}发送消息：{}", userId, message);
                emitter.getEmitter().send(SseEmitter.event().name(name).data(message));
            } catch (IOException e) {
                logger.error(e.getMessage(), e);
            }
        }
    }

    /**
     * 发送消息给指定用户
     *
     * @param userIdList 用户 ID
     * @param message    消息内容
     */
    public static void sendMessage(List<Long> userIdList, String name, String message) {
        List<SSESession> emitterList = getEmittersByUserId(userIdList);
        for (SSESession emitter : emitterList) {
            try {
                logger.error("给用户：{}发送消息：{}", emitter.getUserId(), message);
                emitter.getEmitter().send(SseEmitter.event().name(name).data(message));
//                logger.error("当前SseEmitter：{}", emitter.getEmitter());
            } catch (IOException e) {
                logger.error(e.getMessage(), e);
            }
        }
    }

}
