package com.rcszh.base.common.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONObject;
import com.rcszh.base.common.exception.ServiceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class ServiceUtils<T> {

    private static Logger logger = LoggerFactory.getLogger(ServiceUtils.class);

    /**
     * 有返回值
     * @param serviceName
     * @param methodName
     * @param args
     * @return
     */
    public static <T> T executeAction(String serviceName, String methodName,Object... args) {
        return invokeMethod(serviceName, methodName,args);
    }

    /**
     * 无返回值
     * @param serviceName
     * @param methodName
     * @param args
     * @return
     */
    public static void execute(String serviceName, String methodName,Object... args) {
        invokeMethod(serviceName, methodName,args);
    }

    /**
     * 方法调用
     * @param serviceName
     * @param methodName
     * @param args
     * @return
     */
    public static <T> T invokeMethod(String serviceName, String methodName,Object... args) {
        if (StrUtil.isBlank(serviceName)){
            throw new ServiceException("调用服务名不能为空");
        }
        if (StrUtil.isBlank(methodName)){
            throw new ServiceException("调用方法名不能为空");
        }
        Object result = null;
        Class<? extends Object> class1 = SpringUtil.getBean(serviceName).getClass();
        Method[] ms = class1.getDeclaredMethods();
        Class<?>[] parameterTypes = null;
        Method[] var8 = ms;
        int var9 = ms.length;

        for (int var10 = 0; var10 < var9; ++var10) {
            Method m = var8[var10];
            if (methodName.equals(m.getName())) {
                parameterTypes = m.getParameterTypes();
                break;
            }
        }
        Method mh = ReflectionUtils.findMethod(SpringUtil.getBean(serviceName).getClass(), methodName, parameterTypes);
        if (null == parameterTypes || parameterTypes.length == 0) {
            args = null;
        }
        if (Objects.nonNull(args)){
            result = ReflectionUtils.invokeMethod(mh, SpringUtil.getBean(serviceName), args);
        }else {
            result = ReflectionUtils.invokeMethod(mh, SpringUtil.getBean(serviceName));
        }
        if (Objects.isNull(result)){
            return null;
        }
        return (T)result;
    }


    public static Class getAssociateEntityClass(ApplicationContext context, String serviceName) {
        Class<? extends Object> class1 = context.getBean(serviceName).getClass();
        Class entity = null;
        Class<?> superclass = class1.getSuperclass();

        Type[] associateClassByClass;
        try {
            Type genericSuperclass = superclass.getGenericSuperclass();
            associateClassByClass = ((ParameterizedType) genericSuperclass).getActualTypeArguments();
            if (null != associateClassByClass) {
                entity = (Class) associateClassByClass[0];
            }
        } catch (Exception var7) {
            associateClassByClass = getAssociateClassByClass(superclass);
            if (null != associateClassByClass && associateClassByClass.length > 0) {
                entity = (Class) associateClassByClass[0];
            }
        }

        return entity;
    }

    public static Type[] getAssociateClassByClass(Class<? extends Object> class1) {
        Class<?> superclass = class1.getSuperclass();
        Type genericSuperclass = superclass.getGenericSuperclass();
        Type[] actualTypeArguments = ((ParameterizedType) genericSuperclass).getActualTypeArguments();
        if (null == actualTypeArguments || actualTypeArguments.length == 0) {
            actualTypeArguments = getAssociateClassByClass(superclass.getSuperclass());
        }

        return actualTypeArguments;
    }

    public static Class[] getAssociateClassArr(ApplicationContext context, String serviceName) {
        Class<? extends Object> class1 = context.getBean(serviceName).getClass();
        Type genericSuperclass = null;
        Type[] actualTypeArguments = null;

        try {
            genericSuperclass = class1.getSuperclass().getGenericSuperclass();
            actualTypeArguments = ((ParameterizedType) genericSuperclass).getActualTypeArguments();
        } catch (Exception var11) {
            genericSuperclass = class1.getSuperclass().getSuperclass().getGenericSuperclass();
            actualTypeArguments = ((ParameterizedType) genericSuperclass).getActualTypeArguments();
        }

        if (null != actualTypeArguments && actualTypeArguments.length > 0) {
            List<Class> classes = new ArrayList();
            Type[] var6 = actualTypeArguments;
            int var7 = actualTypeArguments.length;

            for (int var8 = 0; var8 < var7; ++var8) {
                Type actualTypeArgument = var6[var8];
                Class aClass = (Class) actualTypeArgument;
                classes.add(aClass);
            }

            return (Class[]) ((Class[]) classes.toArray());
        } else {
            return null;
        }
    }

    public static Object setEntityVal(Class entity, JSONObject json) {
        Object instance = null;

        try {
            if (json != null) {
                instance = json.toBean(entity);
            } else {
                instance = entity.newInstance();
            }

            return instance;
        } catch (Exception var4) {
            logger.error(var4.getMessage(), var4);
            throw new ServiceException("发生错误");
        }
    }

    public static Object getEntity(ApplicationContext context, String serviceName, JSONObject json) {
        Class className = getAssociateEntityClass(context, serviceName);
        Object entity = setEntityVal(className, json);
        return entity;
    }
}
