package com.rcszh.base.common.exception;

import com.rcszh.base.common.core.domain.AjaxResult;
import lombok.Getter;

import java.text.MessageFormat;

/**
 * 警告提示
 */
public class ServiceWarnException extends RuntimeException {

    private static final long serialVersionUID = 1L;
    /**
     * 错误码
     */
    private Integer code;

    /**
     * 错误提示
     */
    private String message;

    /**
     * 错误明细，内部调试错误
     * <p>
     * 和 {@link CommonResult#getDetailMessage()} 一致的设计
     */
    private String detailMessage;

    /**
     * 返回的数据
     */
    @Getter
    private Object data;

    @Getter
    private String funKey;

    @Getter
    private AjaxResult.FUN_TYPE_ENUM funType;

    /**
     * 空构造方法，避免反序列化问题
     */
    public ServiceWarnException() {
    }

    public ServiceWarnException(String message, AjaxResult.FUN_TYPE_ENUM typeEnum) {
        super(message);
        this.message = message;
        this.funType = typeEnum;
    }

    public ServiceWarnException(String message, AjaxResult.FUN_TYPE_ENUM typeEnum, Object data, String funKey) {
        super(message);
        this.message = message;
        this.funType = typeEnum;
        this.data = data;
        this.funKey = funKey;
    }

    public ServiceWarnException(String message, AjaxResult.FUN_TYPE_ENUM typeEnum, String funKey) {
        super(message);
        this.message = message;
        this.funType = typeEnum;
        this.funKey = funKey;
    }

    public ServiceWarnException(String message, Integer code, AjaxResult.FUN_TYPE_ENUM typeEnum) {
        super(message);
        this.message = message;
        this.code = code;
        this.funType = typeEnum;
    }

    public ServiceWarnException(String message, AjaxResult.FUN_TYPE_ENUM typeEnum, Object... params) {
        super(message);
        this.message = MessageFormat.format(message, params);
        this.funType = typeEnum;
    }

    public String getDetailMessage() {
        return detailMessage;
    }

    public ServiceWarnException setDetailMessage(String detailMessage) {
        this.detailMessage = detailMessage;
        return this;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public ServiceWarnException setMessage(String message) {
        this.message = message;
        return this;
    }

    public Integer getCode() {
        return code;
    }

    public void setFunKey(String funKey) {
        this.funKey = funKey;
    }

    public void setFunType(AjaxResult.FUN_TYPE_ENUM funType) {
        this.funType = funType;
    }

    public void setData(Object data) {
        this.data = data;
    }
}
