2025-07-14 17:48:45.134 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-14 17:48:45.147 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 45113 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-14 17:48:45.148 [main] DEBUG c.r.a.Application - [logStarting,51] - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-14 17:48:45.148 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-14 17:48:46.076 [main] WARN  o.m.s.m.ClassPathMapperScanner - [warn,44] - No MyBatis mapper was found in '[com.ruoyi.**.mapper]' package. Please check your configuration.
2025-07-14 17:48:46.327 [main] WARN  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,437] - Bean 'redisConfig' of type [com.rcszh.config.RedisConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [meterRegistryPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-14 17:48:46.666 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-14 17:48:46.667 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-14 17:48:46.667 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-14 17:48:46.698 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-14 17:48:46.856 [main] WARN  i.n.r.d.DnsServerAddressStreamProviders - [<clinit>,70] - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-07-14 17:48:47.396 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-14 17:48:47.693 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,376] - Can not find table primary key in Class: "com.rcszh.base.common.core.domain.entity.SysUser".
2025-07-14 17:48:47.694 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,52] - class com.rcszh.base.common.core.domain.entity.SysUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-14 17:48:47.722 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,52] - class com.rcszh.base.common.core.domain.entity.SysUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-14 17:48:48.066 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-14 17:48:48.173 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for 192.168.61.10/192.168.61.10:7379
2025-07-14 17:48:48.210 [redisson-netty-4-18] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for 192.168.61.10/192.168.61.10:7379
2025-07-14 17:48:48.282 [main] DEBUG c.r.f.s.f.JwtAuthenticationTokenFilter - [init,240] - Filter 'jwtAuthenticationTokenFilter' configured for use
2025-07-14 17:48:48.335 [main] DEBUG c.r.s.m.S.selectConfigList - [debug,137] - ==>  Preparing: select config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark from sys_config
2025-07-14 17:48:48.342 [main] DEBUG c.r.s.m.S.selectConfigList - [debug,137] - ==> Parameters: 
2025-07-14 17:48:48.350 [main] DEBUG c.r.s.m.S.selectConfigList - [debug,137] - <==      Total: 2
2025-07-14 17:48:51.222 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-14 17:48:52.247 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-14 17:48:52.304 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-14 17:48:52.304 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-14 17:48:52.304 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-14 17:48:52.416 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-14 17:48:52.436 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-14 17:48:52.458 [main] WARN  o.a.s.a.AbstractAutoDeploymentStrategy - [loadEnforcedAppVersion,157] - Enforced application version not set.
2025-07-14 17:48:53.876 [main] DEBUG c.r.s.m.S.selectDictDataList - [debug,137] - ==>  Preparing: select dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark from sys_dict_data WHERE status = ? order by dict_sort asc
2025-07-14 17:48:53.877 [main] DEBUG c.r.s.m.S.selectDictDataList - [debug,137] - ==> Parameters: 0(String)
2025-07-14 17:48:53.891 [main] DEBUG c.r.s.m.S.selectDictDataList - [debug,137] - <==      Total: 164
2025-07-14 17:48:54.421 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-14 17:48:54.426 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-14 17:48:54.426 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-14 17:48:54.426 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-14 17:48:54.426 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-14 17:48:54.426 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-14 17:48:54.426 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-14 17:48:54.426 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@39a54d70
2025-07-14 17:48:54.444 [main] DEBUG c.r.q.m.S.selectJobAll - [debug,137] - ==>  Preparing: select job_id, job_name, job_group, invoke_target, cron_expression, misfire_policy, concurrent, status, create_by, create_time, remark from sys_job
2025-07-14 17:48:54.444 [main] DEBUG c.r.q.m.S.selectJobAll - [debug,137] - ==> Parameters: 
2025-07-14 17:48:54.447 [main] DEBUG c.r.q.m.S.selectJobAll - [debug,137] - <==      Total: 10
2025-07-14 17:48:55.667 [main] WARN  c.b.m.c.m.TableInfoHelper - [initTableFields,376] - Can not find table primary key in Class: "com.rcszh.lowcode.entity.FromDataTable".
2025-07-14 17:48:55.668 [main] WARN  c.b.m.c.i.DefaultSqlInjector - [getMethodList,52] - class com.rcszh.lowcode.entity.FromDataTable ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-14 17:48:55.738 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-14 17:48:55.754 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-14 17:48:55.916 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-14 17:48:55.916 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-14 17:48:55.948 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-14 17:48:55.948 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-14 17:49:00.499 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-14 17:49:00.501 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-14 17:49:00.519 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-14 17:49:00.520 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-14 17:49:00.542 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 15.548 seconds (process running for 16.619)
2025-07-14 17:49:00.614 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 56 ms to scan 26 urls, producing 291 keys and 1781 values
2025-07-14 17:49:00.622 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 1 ms to scan 2 urls, producing 13 keys and 34 values
2025-07-14 17:49:00.690 [main] DEBUG c.r.c.m.P.delete - [debug,137] - ==>  Preparing: DELETE FROM persistent_counter WHERE (expire_time <= ?)
2025-07-14 17:49:00.691 [main] DEBUG c.r.c.m.P.delete - [debug,137] - ==> Parameters: 2025-07-14 17:49:00(String)
2025-07-14 17:49:00.694 [main] DEBUG c.r.c.m.P.delete - [debug,137] - <==    Updates: 0
2025-07-14 17:49:00.696 [main] DEBUG c.r.c.m.P.selectList - [debug,137] - ==>  Preparing: SELECT id,counter_key,counter_value,expire_time,updated_time FROM persistent_counter
2025-07-14 17:49:00.696 [main] DEBUG c.r.c.m.P.selectList - [debug,137] - ==> Parameters: 
2025-07-14 17:49:00.699 [main] DEBUG c.r.c.m.P.selectList - [debug,137] - <==      Total: 2
2025-07-14 17:49:00.706 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-14 17:49:00.861 [RMI TCP Connection(3)-192.168.61.57] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-14 17:49:41.460 [http-nio-8080-exec-1] DEBUG c.r.s.m.S.selectAllMenuPerms - [debug,137] - ==>  Preparing: select distinct m.perms from sys_menu m where m.status = '0'
2025-07-14 17:49:41.461 [http-nio-8080-exec-1] DEBUG c.r.s.m.S.selectAllMenuPerms - [debug,137] - ==> Parameters: 
2025-07-14 17:49:41.465 [http-nio-8080-exec-1] DEBUG c.r.s.m.S.selectAllMenuPerms - [debug,137] - <==      Total: 132
2025-07-14 17:49:41.547 [http-nio-8080-exec-3] DEBUG c.r.s.m.S.selectMenuTreeAll - [debug,137] - ==>  Preparing: select distinct m.menu_id,m.view_list, m.parent_id, m.menu_name, m.path, m.component, m.`query`, m.visible, m.status, ifnull(m.perms,'') as perms, m.is_frame, m.is_cache, m.menu_type, m.icon, m.order_num, m.create_time from sys_menu m where m.menu_type in ('M', 'C') and m.status = 0 order by m.parent_id, m.order_num, m.menu_id
2025-07-14 17:49:41.548 [http-nio-8080-exec-3] DEBUG c.r.s.m.S.selectMenuTreeAll - [debug,137] - ==> Parameters: 
2025-07-14 17:49:41.554 [http-nio-8080-exec-4] DEBUG c.r.b.m.P.selectList - [debug,137] - ==>  Preparing: SELECT id,type,config_key,config_name,config_value,default_value,tip,suffix,required,visible,remark,sort,control_type,control_config,tag,tag_sort,block,create_by,create_time,update_by,update_time FROM pms_config ORDER BY tag ASC,block ASC,sort ASC,id ASC
2025-07-14 17:49:41.554 [http-nio-8080-exec-4] DEBUG c.r.b.m.P.selectList - [debug,137] - ==> Parameters: 
2025-07-14 17:49:41.558 [http-nio-8080-exec-3] DEBUG c.r.s.m.S.selectMenuTreeAll - [debug,137] - <==      Total: 105
2025-07-14 17:49:41.565 [http-nio-8080-exec-4] DEBUG c.r.b.m.P.selectList - [debug,137] - <==      Total: 48
2025-07-14 17:49:41.566 [http-nio-8080-exec-2] DEBUG c.r.l.m.f.F.selectList - [debug,137] - ==>  Preparing: SELECT id,name,code,type,group_name,form_status,form_design_config,is_open_mobile,create_by,create_time,update_by,update_time FROM form ORDER BY update_time DESC
2025-07-14 17:49:41.567 [http-nio-8080-exec-2] DEBUG c.r.l.m.f.F.selectList - [debug,137] - ==> Parameters: 
2025-07-14 17:49:41.573 [http-nio-8080-exec-2] DEBUG c.r.l.m.f.F.selectList - [debug,137] - <==      Total: 16
2025-07-14 17:49:41.831 [http-nio-8080-exec-7] DEBUG c.r.b.m.S.selectList - [debug,137] - ==>  Preparing: SELECT id,setting_key,setting_name,setting_value,remark,create_by,create_time,update_by,update_time FROM sys_setting WHERE (setting_key IN (?,?,?,?,?))
2025-07-14 17:49:41.832 [http-nio-8080-exec-7] DEBUG c.r.b.m.S.selectList - [debug,137] - ==> Parameters: siteName(String), siteTitle(String), logo(String), favicon(String), loginBgImg(String)
2025-07-14 17:49:41.836 [http-nio-8080-exec-7] DEBUG c.r.b.m.S.selectList - [debug,137] - <==      Total: 5
2025-07-14 17:49:41.857 [http-nio-8080-exec-9] ERROR c.r.f.w.e.GlobalExceptionHandler - [handleException,161] - 请求地址'/profile/upload/2025/03/06/20250306190337A001.png',发生系统异常.
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource upload/2025/03/06/20250306190337A001.png.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:206)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at com.rcszh.base.common.filter.RepeatableFilter.doFilter(RepeatableFilter.java:35)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:117)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:83)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at com.ruoyi.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-14 17:49:41.876 [http-nio-8080-exec-8] DEBUG c.r.b.m.P.selectList - [debug,137] - ==>  Preparing: SELECT id,type,config_key,config_name,config_value,default_value,tip,suffix,required,visible,remark,sort,control_type,control_config,tag,tag_sort,block,create_by,create_time,update_by,update_time FROM pms_config WHERE (config_key = ?)
2025-07-14 17:49:41.877 [http-nio-8080-exec-8] DEBUG c.r.b.m.P.selectList - [debug,137] - ==> Parameters: expectTimeActionTye(String)
2025-07-14 17:49:41.880 [http-nio-8080-exec-8] DEBUG c.r.b.m.P.selectList - [debug,137] - <==      Total: 1
2025-07-14 17:49:41.891 [http-nio-8080-exec-10] DEBUG c.r.b.m.P.selectList - [debug,137] - ==>  Preparing: SELECT id,type,config_key,config_name,config_value,default_value,tip,suffix,required,visible,remark,sort,control_type,control_config,tag,tag_sort,block,create_by,create_time,update_by,update_time FROM pms_config WHERE (config_key = ?)
2025-07-14 17:49:41.891 [http-nio-8080-exec-10] DEBUG c.r.b.m.P.selectList - [debug,137] - ==> Parameters: expectTimeUnit(String)
2025-07-14 17:49:41.895 [http-nio-8080-exec-10] DEBUG c.r.b.m.P.selectList - [debug,137] - <==      Total: 1
2025-07-14 17:49:41.923 [http-nio-8080-exec-11] DEBUG c.r.p.m.P.selectList_COUNT - [debug,137] - ==>  Preparing: SELECT count(0) FROM pms_project
2025-07-14 17:49:41.923 [http-nio-8080-exec-12] DEBUG c.r.b.m.P.selectList_COUNT - [debug,137] - ==>  Preparing: SELECT count(0) FROM pms_config WHERE (config_key = ?)
2025-07-14 17:49:41.923 [http-nio-8080-exec-11] DEBUG c.r.p.m.P.selectList_COUNT - [debug,137] - ==> Parameters: 
2025-07-14 17:49:41.923 [http-nio-8080-exec-12] DEBUG c.r.b.m.P.selectList_COUNT - [debug,137] - ==> Parameters: expectTimeUnit(String)
2025-07-14 17:49:41.927 [http-nio-8080-exec-11] DEBUG c.r.p.m.P.selectList_COUNT - [debug,137] - <==      Total: 1
2025-07-14 17:49:41.927 [http-nio-8080-exec-12] DEBUG c.r.b.m.P.selectList_COUNT - [debug,137] - <==      Total: 1
2025-07-14 17:49:41.929 [http-nio-8080-exec-11] DEBUG c.r.p.m.P.selectList - [debug,137] - ==>  Preparing: SELECT id,code,name,associated_project_id,project_abbr,category_code,project_status,project_mode,project_manager_id,project_supervisor_id,project_assistant_id,planned_start_at,planned_finish_by,actual_start_date,actual_end_date,project_assistant_id2,project_assistant_id3,project_assistant_id4,project_assistant_id5,salesperson_id,province,city,area,address,currency_code,rate,contract_amount,tax_rate,amount_excluding_tax,description,customer_id,supplier_id,company_id,create_by,create_time,update_by,update_time FROM pms_project ORDER BY create_time ASC LIMIT ?
2025-07-14 17:49:41.929 [http-nio-8080-exec-12] DEBUG c.r.b.m.P.selectList - [debug,137] - ==>  Preparing: SELECT id,type,config_key,config_name,config_value,default_value,tip,suffix,required,visible,remark,sort,control_type,control_config,tag,tag_sort,block,create_by,create_time,update_by,update_time FROM pms_config WHERE (config_key = ?) LIMIT ?
2025-07-14 17:49:41.929 [http-nio-8080-exec-11] DEBUG c.r.p.m.P.selectList - [debug,137] - ==> Parameters: 10000(Integer)
2025-07-14 17:49:41.929 [http-nio-8080-exec-12] DEBUG c.r.b.m.P.selectList - [debug,137] - ==> Parameters: expectTimeUnit(String), 20(Integer)
2025-07-14 17:49:41.935 [http-nio-8080-exec-12] DEBUG c.r.b.m.P.selectList - [debug,137] - <==      Total: 1
2025-07-14 17:49:41.935 [http-nio-8080-exec-12] DEBUG c.r.w.m.S.findAll - [debug,137] - ==>  Preparing: SELECT w.*,p.name as project_name,p.code as project_code from sys_expect_project_work_time w left join pms_project p on p.id = w.project_id
2025-07-14 17:49:41.936 [http-nio-8080-exec-12] DEBUG c.r.w.m.S.findAll - [debug,137] - ==> Parameters: 
2025-07-14 17:49:41.936 [http-nio-8080-exec-11] DEBUG c.r.p.m.P.selectList - [debug,137] - <==      Total: 34
2025-07-14 17:49:41.939 [http-nio-8080-exec-12] DEBUG c.r.w.m.S.findAll - [debug,137] - <==      Total: 1
2025-07-14 17:49:41.942 [http-nio-8080-exec-12] ERROR c.r.f.w.e.GlobalExceptionHandler - [handleRuntimeException,150] - 请求地址'/system/expect/workTime/project/page',发生未知异常.
java.lang.RuntimeException: 时间单位配置错误
	at com.rcszh.worktime.domain.expect_work.BaseExpectWork.covertWorkTime(BaseExpectWork.java:27)
	at com.rcszh.worktime.domain.expect_work.SysExpectProjectWorkTime.toDto(SysExpectProjectWorkTime.java:39)
	at com.rcszh.worktime.service.impl.SysExpectWorkTimeServiceImpl.lambda$getExpectProjectWorkTime$0(SysExpectWorkTimeServiceImpl.java:68)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at com.rcszh.worktime.service.impl.SysExpectWorkTimeServiceImpl.getExpectProjectWorkTime(SysExpectWorkTimeServiceImpl.java:68)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:354)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:716)
	at com.rcszh.worktime.service.impl.SysExpectWorkTimeServiceImpl$$SpringCGLIB$$0.getExpectProjectWorkTime(<generated>)
	at com.rcszh.admin.web.controller.system.SysExpectWorkTimeController.list(SysExpectWorkTimeController.java:50)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:354)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:174)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:720)
	at com.rcszh.admin.web.controller.system.SysExpectWorkTimeController$$SpringCGLIB$$0.list(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:206)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at com.rcszh.base.common.filter.RepeatableFilter.doFilter(RepeatableFilter.java:35)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:117)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:83)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at com.ruoyi.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-14 17:49:41.982 [http-nio-8080-exec-11] DEBUG c.r.s.m.S.selectUserById - [debug,137] - ==>  Preparing: select u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.avatar, u.phonenumber, u.password, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark, d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.order_num, d.leader, d.status as dept_status, r.role_id, r.role_name, r.role_key, r.role_sort, r.data_scope, r.status as role_status from sys_user u left join sys_dept d on u.dept_id = d.dept_id left join sys_user_role ur on u.user_id = ur.user_id left join sys_role r on r.role_id = ur.role_id where u.user_id = ?
2025-07-14 17:49:41.982 [http-nio-8080-exec-11] DEBUG c.r.s.m.S.selectUserById - [debug,137] - ==> Parameters: 4950(Long)
2025-07-14 17:49:41.985 [http-nio-8080-exec-11] DEBUG c.r.s.m.S.selectUserById - [debug,137] - <==      Total: 0
2025-07-14 17:49:42.001 [http-nio-8080-exec-11] DEBUG c.r.b.m.P.selectById - [debug,137] - ==>  Preparing: SELECT id,code,name,abbreviation,signing_company,group_company,customer_type,customer_level,customer_source,source_remark,phone,email,website,postal_code,fax,province,city,district,address,customer_logo,payment_term,company_name,social_credit_code,registered_address,registered_phone,bank_name,account_number,invoice_code,invoice_description,remark,contact_name,contact_sex,contact_dept,contact_position,contact_fixed_phone,contact_mobile_phone,contact_email,contact_instant_messaging,contact_address,create_time,update_time,is_deleted,create_by,update_by FROM pms_customer WHERE id=? AND is_deleted='N'
2025-07-14 17:49:42.001 [http-nio-8080-exec-11] DEBUG c.r.b.m.P.selectById - [debug,137] - ==> Parameters: 1874700792478556163(Long)
2025-07-14 17:49:42.004 [http-nio-8080-exec-11] DEBUG c.r.b.m.P.selectById - [debug,137] - <==      Total: 0
2025-07-14 17:49:42.034 [http-nio-8080-exec-11] DEBUG c.r.b.m.P.selectById - [debug,137] - ==>  Preparing: SELECT id,code,name,abbreviation,signing_company,group_company,customer_type,customer_level,customer_source,source_remark,phone,email,website,postal_code,fax,province,city,district,address,customer_logo,payment_term,company_name,social_credit_code,registered_address,registered_phone,bank_name,account_number,invoice_code,invoice_description,remark,contact_name,contact_sex,contact_dept,contact_position,contact_fixed_phone,contact_mobile_phone,contact_email,contact_instant_messaging,contact_address,create_time,update_time,is_deleted,create_by,update_by FROM pms_customer WHERE id=? AND is_deleted='N'
2025-07-14 17:49:42.035 [http-nio-8080-exec-11] DEBUG c.r.b.m.P.selectById - [debug,137] - ==> Parameters: 1874700792478556165(Long)
2025-07-14 17:49:42.038 [http-nio-8080-exec-11] DEBUG c.r.b.m.P.selectById - [debug,137] - <==      Total: 0
2025-07-14 17:49:42.055 [http-nio-8080-exec-11] DEBUG c.r.s.m.S.selectDeptById - [debug,137] - ==>  Preparing: select d.dept_id, d.parent_id, d.ancestors, d.dept_name,d.dept_code, d.order_num, d.leader, d.phone, d.email, d.status,d.company_id,d.type, (select dept_name from sys_dept where dept_id = d.parent_id) parent_name from sys_dept d where d.dept_id = ?
2025-07-14 17:49:42.055 [http-nio-8080-exec-11] DEBUG c.r.s.m.S.selectDeptById - [debug,137] - ==> Parameters: 70(Long)
2025-07-14 17:49:42.058 [http-nio-8080-exec-11] DEBUG c.r.s.m.S.selectDeptById - [debug,137] - <==      Total: 0
2025-07-14 17:49:42.080 [http-nio-8080-exec-11] DEBUG c.r.s.m.S.selectUserById - [debug,137] - ==>  Preparing: select u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.avatar, u.phonenumber, u.password, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark, d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.order_num, d.leader, d.status as dept_status, r.role_id, r.role_name, r.role_key, r.role_sort, r.data_scope, r.status as role_status from sys_user u left join sys_dept d on u.dept_id = d.dept_id left join sys_user_role ur on u.user_id = ur.user_id left join sys_role r on r.role_id = ur.role_id where u.user_id = ?
2025-07-14 17:49:42.080 [http-nio-8080-exec-11] DEBUG c.r.s.m.S.selectUserById - [debug,137] - ==> Parameters: 4949(Long)
2025-07-14 17:49:42.083 [http-nio-8080-exec-11] DEBUG c.r.s.m.S.selectUserById - [debug,137] - <==      Total: 0
2025-07-14 17:49:42.121 [http-nio-8080-exec-11] DEBUG c.r.s.m.S.selectUserById - [debug,137] - ==>  Preparing: select u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.avatar, u.phonenumber, u.password, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark, d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.order_num, d.leader, d.status as dept_status, r.role_id, r.role_name, r.role_key, r.role_sort, r.data_scope, r.status as role_status from sys_user u left join sys_dept d on u.dept_id = d.dept_id left join sys_user_role ur on u.user_id = ur.user_id left join sys_role r on r.role_id = ur.role_id where u.user_id = ?
2025-07-14 17:49:42.122 [http-nio-8080-exec-11] DEBUG c.r.s.m.S.selectUserById - [debug,137] - ==> Parameters: 13(Long)
2025-07-14 17:49:42.126 [http-nio-8080-exec-11] DEBUG c.r.s.m.S.selectUserById - [debug,137] - <==      Total: 1
2025-07-14 17:49:42.171 [http-nio-8080-exec-11] DEBUG c.r.b.m.S.selectList - [debug,137] - ==>  Preparing: SELECT id,code,name,p_code,level,chain,create_by,create_time,update_by,update_time FROM sys_city WHERE (code = ?)
2025-07-14 17:49:42.171 [http-nio-8080-exec-11] DEBUG c.r.b.m.S.selectList - [debug,137] - ==> Parameters: 220102(String)
2025-07-14 17:49:42.178 [http-nio-8080-exec-11] DEBUG c.r.b.m.S.selectList - [debug,137] - <==      Total: 1
2025-07-14 17:49:42.217 [http-nio-8080-exec-11] DEBUG c.r.s.m.S.selectUserById - [debug,137] - ==>  Preparing: select u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.avatar, u.phonenumber, u.password, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark, d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.order_num, d.leader, d.status as dept_status, r.role_id, r.role_name, r.role_key, r.role_sort, r.data_scope, r.status as role_status from sys_user u left join sys_dept d on u.dept_id = d.dept_id left join sys_user_role ur on u.user_id = ur.user_id left join sys_role r on r.role_id = ur.role_id where u.user_id = ?
2025-07-14 17:49:42.217 [http-nio-8080-exec-11] DEBUG c.r.s.m.S.selectUserById - [debug,137] - ==> Parameters: 3(Long)
2025-07-14 17:49:42.221 [http-nio-8080-exec-11] DEBUG c.r.s.m.S.selectUserById - [debug,137] - <==      Total: 2
2025-07-14 17:49:42.374 [http-nio-8080-exec-11] DEBUG c.r.p.m.P.selectList - [debug,137] - ==>  Preparing: SELECT id,is_used,code,name,task_abbr,status,progress,planned_start_date,planned_end_date,actual_start_date,actual_end_date,work_days,actual_work_days,owner,task_type,plan_weight,milestone,milestone_status,estimated_hours,actual_hours,task_description,project_id,parent_id,ancestors,is_leaf,serial_number,create_by,create_time,update_by,update_time FROM pms_project_task WHERE (project_id IN (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) AND parent_id = ?)
2025-07-14 17:49:42.376 [http-nio-8080-exec-11] DEBUG c.r.p.m.P.selectList - [debug,137] - ==> Parameters: 1(Long), 2(Long), 3(Long), 221(Long), 222(Long), 223(Long), 224(Long), 225(Long), 226(Long), 233(Long), 234(Long), 235(Long), 236(Long), 237(Long), 238(Long), 239(Long), 240(Long), 241(Long), 242(Long), 243(Long), 244(Long), 245(Long), 246(Long), 249(Long), 250(Long), 251(Long), 252(Long), 254(Long), 255(Long), 256(Long), 257(Long), 258(Long), 259(Long), 260(Long), 0(Long)
2025-07-14 17:49:42.389 [http-nio-8080-exec-11] DEBUG c.r.p.m.P.selectList - [debug,137] - <==      Total: 62
2025-07-14 18:08:35.227 [http-nio-8080-exec-13] DEBUG c.r.s.m.S.selectAllMenuPerms - [debug,137] - ==>  Preparing: select distinct m.perms from sys_menu m where m.status = '0'
2025-07-14 18:08:35.240 [http-nio-8080-exec-13] DEBUG c.r.s.m.S.selectAllMenuPerms - [debug,137] - ==> Parameters: 
2025-07-14 18:08:35.249 [http-nio-8080-exec-13] DEBUG c.r.s.m.S.selectAllMenuPerms - [debug,137] - <==      Total: 132
2025-07-14 18:08:35.347 [http-nio-8080-exec-15] DEBUG c.r.s.m.S.selectMenuTreeAll - [debug,137] - ==>  Preparing: select distinct m.menu_id,m.view_list, m.parent_id, m.menu_name, m.path, m.component, m.`query`, m.visible, m.status, ifnull(m.perms,'') as perms, m.is_frame, m.is_cache, m.menu_type, m.icon, m.order_num, m.create_time from sys_menu m where m.menu_type in ('M', 'C') and m.status = 0 order by m.parent_id, m.order_num, m.menu_id
2025-07-14 18:08:35.348 [http-nio-8080-exec-15] DEBUG c.r.s.m.S.selectMenuTreeAll - [debug,137] - ==> Parameters: 
2025-07-14 18:08:35.374 [http-nio-8080-exec-14] DEBUG c.r.s.m.S.selectAllMenuPerms - [debug,137] - ==>  Preparing: select distinct m.perms from sys_menu m where m.status = '0'
2025-07-14 18:08:35.374 [http-nio-8080-exec-14] DEBUG c.r.s.m.S.selectAllMenuPerms - [debug,137] - ==> Parameters: 
2025-07-14 18:08:35.380 [http-nio-8080-exec-14] DEBUG c.r.s.m.S.selectAllMenuPerms - [debug,137] - <==      Total: 132
2025-07-14 18:08:35.380 [http-nio-8080-exec-16] DEBUG c.r.b.m.P.selectList - [debug,137] - ==>  Preparing: SELECT id,type,config_key,config_name,config_value,default_value,tip,suffix,required,visible,remark,sort,control_type,control_config,tag,tag_sort,block,create_by,create_time,update_by,update_time FROM pms_config ORDER BY tag ASC,block ASC,sort ASC,id ASC
2025-07-14 18:08:35.381 [http-nio-8080-exec-16] DEBUG c.r.b.m.P.selectList - [debug,137] - ==> Parameters: 
2025-07-14 18:08:35.395 [http-nio-8080-exec-15] DEBUG c.r.s.m.S.selectMenuTreeAll - [debug,137] - <==      Total: 105
2025-07-14 18:08:35.395 [http-nio-8080-exec-16] DEBUG c.r.b.m.P.selectList - [debug,137] - <==      Total: 48
2025-07-14 18:08:35.395 [http-nio-8080-exec-17] DEBUG c.r.l.m.f.F.selectList - [debug,137] - ==>  Preparing: SELECT id,name,code,type,group_name,form_status,form_design_config,is_open_mobile,create_by,create_time,update_by,update_time FROM form ORDER BY update_time DESC
2025-07-14 18:08:35.396 [http-nio-8080-exec-17] DEBUG c.r.l.m.f.F.selectList - [debug,137] - ==> Parameters: 
2025-07-14 18:08:35.405 [http-nio-8080-exec-17] DEBUG c.r.l.m.f.F.selectList - [debug,137] - <==      Total: 16
2025-07-14 18:08:35.475 [http-nio-8080-exec-19] DEBUG c.r.b.m.P.selectList - [debug,137] - ==>  Preparing: SELECT id,type,config_key,config_name,config_value,default_value,tip,suffix,required,visible,remark,sort,control_type,control_config,tag,tag_sort,block,create_by,create_time,update_by,update_time FROM pms_config ORDER BY tag ASC,block ASC,sort ASC,id ASC
2025-07-14 18:08:35.475 [http-nio-8080-exec-19] DEBUG c.r.b.m.P.selectList - [debug,137] - ==> Parameters: 
2025-07-14 18:08:35.497 [http-nio-8080-exec-20] DEBUG c.r.l.m.f.F.selectList - [debug,137] - ==>  Preparing: SELECT id,name,code,type,group_name,form_status,form_design_config,is_open_mobile,create_by,create_time,update_by,update_time FROM form ORDER BY update_time DESC
2025-07-14 18:08:35.496 [http-nio-8080-exec-18] DEBUG c.r.s.m.S.selectMenuTreeAll - [debug,137] - ==>  Preparing: select distinct m.menu_id,m.view_list, m.parent_id, m.menu_name, m.path, m.component, m.`query`, m.visible, m.status, ifnull(m.perms,'') as perms, m.is_frame, m.is_cache, m.menu_type, m.icon, m.order_num, m.create_time from sys_menu m where m.menu_type in ('M', 'C') and m.status = 0 order by m.parent_id, m.order_num, m.menu_id
2025-07-14 18:08:35.497 [http-nio-8080-exec-18] DEBUG c.r.s.m.S.selectMenuTreeAll - [debug,137] - ==> Parameters: 
2025-07-14 18:08:35.497 [http-nio-8080-exec-20] DEBUG c.r.l.m.f.F.selectList - [debug,137] - ==> Parameters: 
2025-07-14 18:08:35.503 [http-nio-8080-exec-20] DEBUG c.r.l.m.f.F.selectList - [debug,137] - <==      Total: 16
2025-07-14 18:08:35.505 [http-nio-8080-exec-19] DEBUG c.r.b.m.P.selectList - [debug,137] - <==      Total: 48
2025-07-14 18:08:35.525 [http-nio-8080-exec-18] DEBUG c.r.s.m.S.selectMenuTreeAll - [debug,137] - <==      Total: 105
2025-07-14 18:08:36.275 [http-nio-8080-exec-21] DEBUG c.r.b.m.S.selectList - [debug,137] - ==>  Preparing: SELECT id,setting_key,setting_name,setting_value,remark,create_by,create_time,update_by,update_time FROM sys_setting WHERE (setting_key IN (?,?,?,?,?))
2025-07-14 18:08:36.276 [http-nio-8080-exec-21] DEBUG c.r.b.m.S.selectList - [debug,137] - ==> Parameters: siteName(String), siteTitle(String), logo(String), favicon(String), loginBgImg(String)
2025-07-14 18:08:36.287 [http-nio-8080-exec-21] DEBUG c.r.b.m.S.selectList - [debug,137] - <==      Total: 5
2025-07-14 18:08:36.300 [http-nio-8080-exec-24] DEBUG c.r.b.m.P.selectList - [debug,137] - ==>  Preparing: SELECT id,type,config_key,config_name,config_value,default_value,tip,suffix,required,visible,remark,sort,control_type,control_config,tag,tag_sort,block,create_by,create_time,update_by,update_time FROM pms_config WHERE (config_key = ?)
2025-07-14 18:08:36.303 [http-nio-8080-exec-24] DEBUG c.r.b.m.P.selectList - [debug,137] - ==> Parameters: expectTimeActionTye(String)
2025-07-14 18:08:36.307 [http-nio-8080-exec-24] DEBUG c.r.b.m.P.selectList - [debug,137] - <==      Total: 1
2025-07-14 18:08:36.327 [http-nio-8080-exec-25] ERROR c.r.f.w.e.GlobalExceptionHandler - [handleException,161] - 请求地址'/profile/upload/2025/03/06/20250306190337A001.png',发生系统异常.
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource upload/2025/03/06/20250306190337A001.png.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:206)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at com.rcszh.base.common.filter.RepeatableFilter.doFilter(RepeatableFilter.java:35)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:117)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:83)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at com.ruoyi.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-14 18:08:36.334 [http-nio-8080-exec-26] DEBUG c.r.b.m.P.selectList - [debug,137] - ==>  Preparing: SELECT id,type,config_key,config_name,config_value,default_value,tip,suffix,required,visible,remark,sort,control_type,control_config,tag,tag_sort,block,create_by,create_time,update_by,update_time FROM pms_config WHERE (config_key = ?)
2025-07-14 18:08:36.335 [http-nio-8080-exec-26] DEBUG c.r.b.m.P.selectList - [debug,137] - ==> Parameters: expectTimeUnit(String)
2025-07-14 18:08:36.338 [http-nio-8080-exec-26] DEBUG c.r.b.m.P.selectList - [debug,137] - <==      Total: 1
2025-07-14 18:08:36.383 [http-nio-8080-exec-27] DEBUG c.r.b.m.P.selectList_COUNT - [debug,137] - ==>  Preparing: SELECT count(0) FROM pms_config WHERE (config_key = ?)
2025-07-14 18:08:36.384 [http-nio-8080-exec-27] DEBUG c.r.b.m.P.selectList_COUNT - [debug,137] - ==> Parameters: expectTimeUnit(String)
2025-07-14 18:08:36.387 [http-nio-8080-exec-28] DEBUG c.r.p.m.P.selectList_COUNT - [debug,137] - ==>  Preparing: SELECT count(0) FROM pms_project
2025-07-14 18:08:36.387 [http-nio-8080-exec-27] DEBUG c.r.b.m.P.selectList_COUNT - [debug,137] - <==      Total: 1
2025-07-14 18:08:36.388 [http-nio-8080-exec-28] DEBUG c.r.p.m.P.selectList_COUNT - [debug,137] - ==> Parameters: 
2025-07-14 18:08:36.388 [http-nio-8080-exec-27] DEBUG c.r.b.m.P.selectList - [debug,137] - ==>  Preparing: SELECT id,type,config_key,config_name,config_value,default_value,tip,suffix,required,visible,remark,sort,control_type,control_config,tag,tag_sort,block,create_by,create_time,update_by,update_time FROM pms_config WHERE (config_key = ?) LIMIT ?
2025-07-14 18:08:36.389 [http-nio-8080-exec-27] DEBUG c.r.b.m.P.selectList - [debug,137] - ==> Parameters: expectTimeUnit(String), 20(Integer)
2025-07-14 18:08:36.391 [http-nio-8080-exec-28] DEBUG c.r.p.m.P.selectList_COUNT - [debug,137] - <==      Total: 1
2025-07-14 18:08:36.393 [http-nio-8080-exec-27] DEBUG c.r.b.m.P.selectList - [debug,137] - <==      Total: 1
2025-07-14 18:08:36.394 [http-nio-8080-exec-28] DEBUG c.r.p.m.P.selectList - [debug,137] - ==>  Preparing: SELECT id,code,name,associated_project_id,project_abbr,category_code,project_status,project_mode,project_manager_id,project_supervisor_id,project_assistant_id,planned_start_at,planned_finish_by,actual_start_date,actual_end_date,project_assistant_id2,project_assistant_id3,project_assistant_id4,project_assistant_id5,salesperson_id,province,city,area,address,currency_code,rate,contract_amount,tax_rate,amount_excluding_tax,description,customer_id,supplier_id,company_id,create_by,create_time,update_by,update_time FROM pms_project ORDER BY create_time ASC LIMIT ?
2025-07-14 18:08:36.394 [http-nio-8080-exec-28] DEBUG c.r.p.m.P.selectList - [debug,137] - ==> Parameters: 10000(Integer)
2025-07-14 18:08:36.394 [http-nio-8080-exec-27] DEBUG c.r.w.m.S.findAll - [debug,137] - ==>  Preparing: SELECT w.*,p.name as project_name,p.code as project_code from sys_expect_project_work_time w left join pms_project p on p.id = w.project_id
2025-07-14 18:08:36.395 [http-nio-8080-exec-27] DEBUG c.r.w.m.S.findAll - [debug,137] - ==> Parameters: 
2025-07-14 18:08:36.400 [http-nio-8080-exec-27] DEBUG c.r.w.m.S.findAll - [debug,137] - <==      Total: 1
2025-07-14 18:08:36.401 [http-nio-8080-exec-27] ERROR c.r.f.w.e.GlobalExceptionHandler - [handleRuntimeException,150] - 请求地址'/system/expect/workTime/project/page',发生未知异常.
java.lang.RuntimeException: 时间单位配置错误
	at com.rcszh.worktime.domain.expect_work.BaseExpectWork.covertWorkTime(BaseExpectWork.java:27)
	at com.rcszh.worktime.domain.expect_work.SysExpectProjectWorkTime.toDto(SysExpectProjectWorkTime.java:39)
	at com.rcszh.worktime.service.impl.SysExpectWorkTimeServiceImpl.lambda$getExpectProjectWorkTime$0(SysExpectWorkTimeServiceImpl.java:68)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at com.rcszh.worktime.service.impl.SysExpectWorkTimeServiceImpl.getExpectProjectWorkTime(SysExpectWorkTimeServiceImpl.java:68)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:354)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:716)
	at com.rcszh.worktime.service.impl.SysExpectWorkTimeServiceImpl$$SpringCGLIB$$0.getExpectProjectWorkTime(<generated>)
	at com.rcszh.admin.web.controller.system.SysExpectWorkTimeController.list(SysExpectWorkTimeController.java:50)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:354)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:174)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:720)
	at com.rcszh.admin.web.controller.system.SysExpectWorkTimeController$$SpringCGLIB$$0.list(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:206)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at com.rcszh.base.common.filter.RepeatableFilter.doFilter(RepeatableFilter.java:35)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:117)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:83)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at com.ruoyi.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-14 18:08:36.402 [http-nio-8080-exec-28] DEBUG c.r.p.m.P.selectList - [debug,137] - <==      Total: 34
2025-07-14 18:08:36.438 [http-nio-8080-exec-28] DEBUG c.r.s.m.S.selectUserById - [debug,137] - ==>  Preparing: select u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.avatar, u.phonenumber, u.password, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark, d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.order_num, d.leader, d.status as dept_status, r.role_id, r.role_name, r.role_key, r.role_sort, r.data_scope, r.status as role_status from sys_user u left join sys_dept d on u.dept_id = d.dept_id left join sys_user_role ur on u.user_id = ur.user_id left join sys_role r on r.role_id = ur.role_id where u.user_id = ?
2025-07-14 18:08:36.438 [http-nio-8080-exec-28] DEBUG c.r.s.m.S.selectUserById - [debug,137] - ==> Parameters: 4950(Long)
2025-07-14 18:08:36.441 [http-nio-8080-exec-28] DEBUG c.r.s.m.S.selectUserById - [debug,137] - <==      Total: 0
2025-07-14 18:08:36.459 [http-nio-8080-exec-28] DEBUG c.r.b.m.P.selectById - [debug,137] - ==>  Preparing: SELECT id,code,name,abbreviation,signing_company,group_company,customer_type,customer_level,customer_source,source_remark,phone,email,website,postal_code,fax,province,city,district,address,customer_logo,payment_term,company_name,social_credit_code,registered_address,registered_phone,bank_name,account_number,invoice_code,invoice_description,remark,contact_name,contact_sex,contact_dept,contact_position,contact_fixed_phone,contact_mobile_phone,contact_email,contact_instant_messaging,contact_address,create_time,update_time,is_deleted,create_by,update_by FROM pms_customer WHERE id=? AND is_deleted='N'
2025-07-14 18:08:36.460 [http-nio-8080-exec-28] DEBUG c.r.b.m.P.selectById - [debug,137] - ==> Parameters: 1874700792478556163(Long)
2025-07-14 18:08:36.464 [http-nio-8080-exec-28] DEBUG c.r.b.m.P.selectById - [debug,137] - <==      Total: 0
2025-07-14 18:08:36.484 [http-nio-8080-exec-29] DEBUG c.r.b.m.S.selectList - [debug,137] - ==>  Preparing: SELECT id,setting_key,setting_name,setting_value,remark,create_by,create_time,update_by,update_time FROM sys_setting WHERE (setting_key IN (?,?,?,?,?))
2025-07-14 18:08:36.484 [http-nio-8080-exec-29] DEBUG c.r.b.m.S.selectList - [debug,137] - ==> Parameters: siteName(String), siteTitle(String), logo(String), favicon(String), loginBgImg(String)
2025-07-14 18:08:36.487 [http-nio-8080-exec-29] DEBUG c.r.b.m.S.selectList - [debug,137] - <==      Total: 5
2025-07-14 18:08:36.489 [http-nio-8080-exec-30] DEBUG c.r.l.m.f.V.selectById - [debug,137] - ==>  Preparing: SELECT id,form_id,name,status,type,system_type FROM view_form WHERE id=?
2025-07-14 18:08:36.489 [http-nio-8080-exec-30] DEBUG c.r.l.m.f.V.selectById - [debug,137] - ==> Parameters: 1943512918928240641(String)
2025-07-14 18:08:36.491 [http-nio-8080-exec-31] DEBUG c.r.l.m.F.selectList - [debug,137] - ==>  Preparing: SELECT id,form_id,name,table_name,status,type,create_by,create_time,update_by,update_time FROM form_table WHERE (form_id = ?)
2025-07-14 18:08:36.491 [http-nio-8080-exec-31] DEBUG c.r.l.m.F.selectList - [debug,137] - ==> Parameters: 1901586283559489538(String)
2025-07-14 18:08:36.493 [http-nio-8080-exec-30] DEBUG c.r.l.m.f.V.selectById - [debug,137] - <==      Total: 1
2025-07-14 18:08:36.493 [http-nio-8080-exec-32] ERROR c.r.f.w.e.GlobalExceptionHandler - [handleException,161] - 请求地址'/profile/upload/2025/03/06/20250306190337A001.png',发生系统异常.
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource upload/2025/03/06/20250306190337A001.png.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:206)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at com.rcszh.base.common.filter.RepeatableFilter.doFilter(RepeatableFilter.java:35)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:117)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:83)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at com.ruoyi.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-14 18:08:36.494 [http-nio-8080-exec-31] DEBUG c.r.l.m.F.selectList - [debug,137] - <==      Total: 3
2025-07-14 18:08:36.496 [http-nio-8080-exec-30] DEBUG c.r.l.m.f.V.selectList - [debug,137] - ==>  Preparing: SELECT id,view_form_id,view_form_type,type,name,system_type,options,create_by,create_time,update_by,update_time FROM view_form_config WHERE (view_form_id = ?)
2025-07-14 18:08:36.496 [http-nio-8080-exec-30] DEBUG c.r.l.m.f.V.selectList - [debug,137] - ==> Parameters: 1943512918928240641(String)
2025-07-14 18:08:36.497 [http-nio-8080-exec-28] DEBUG c.r.b.m.P.selectById - [debug,137] - ==>  Preparing: SELECT id,code,name,abbreviation,signing_company,group_company,customer_type,customer_level,customer_source,source_remark,phone,email,website,postal_code,fax,province,city,district,address,customer_logo,payment_term,company_name,social_credit_code,registered_address,registered_phone,bank_name,account_number,invoice_code,invoice_description,remark,contact_name,contact_sex,contact_dept,contact_position,contact_fixed_phone,contact_mobile_phone,contact_email,contact_instant_messaging,contact_address,create_time,update_time,is_deleted,create_by,update_by FROM pms_customer WHERE id=? AND is_deleted='N'
2025-07-14 18:08:36.497 [http-nio-8080-exec-28] DEBUG c.r.b.m.P.selectById - [debug,137] - ==> Parameters: 1874700792478556165(Long)
2025-07-14 18:08:36.497 [http-nio-8080-exec-31] DEBUG c.r.l.m.F.selectList - [debug,137] - ==>  Preparing: SELECT id,props,form_id,form_table_id,is_open_index,parent_id,name,component,component_order,jdbc_type,field_order,length,digit,label,status,ftf_is_system,ftf_display,options,description,create_by,create_time,update_by,update_time FROM form_table_field WHERE (form_id = ?)
2025-07-14 18:08:36.497 [http-nio-8080-exec-31] DEBUG c.r.l.m.F.selectList - [debug,137] - ==> Parameters: 1901586283559489538(String)
2025-07-14 18:08:36.502 [http-nio-8080-exec-28] DEBUG c.r.b.m.P.selectById - [debug,137] - <==      Total: 0
2025-07-14 18:08:36.503 [http-nio-8080-exec-30] DEBUG c.r.l.m.f.V.selectList - [debug,137] - <==      Total: 5
2025-07-14 18:08:36.514 [http-nio-8080-exec-31] DEBUG c.r.l.m.F.selectList - [debug,137] - <==      Total: 46
2025-07-14 18:08:36.523 [http-nio-8080-exec-28] DEBUG c.r.s.m.S.selectDeptById - [debug,137] - ==>  Preparing: select d.dept_id, d.parent_id, d.ancestors, d.dept_name,d.dept_code, d.order_num, d.leader, d.phone, d.email, d.status,d.company_id,d.type, (select dept_name from sys_dept where dept_id = d.parent_id) parent_name from sys_dept d where d.dept_id = ?
2025-07-14 18:08:36.524 [http-nio-8080-exec-28] DEBUG c.r.s.m.S.selectDeptById - [debug,137] - ==> Parameters: 70(Long)
2025-07-14 18:08:36.527 [http-nio-8080-exec-28] DEBUG c.r.s.m.S.selectDeptById - [debug,137] - <==      Total: 0
2025-07-14 18:08:36.549 [http-nio-8080-exec-28] DEBUG c.r.s.m.S.selectUserById - [debug,137] - ==>  Preparing: select u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.avatar, u.phonenumber, u.password, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark, d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.order_num, d.leader, d.status as dept_status, r.role_id, r.role_name, r.role_key, r.role_sort, r.data_scope, r.status as role_status from sys_user u left join sys_dept d on u.dept_id = d.dept_id left join sys_user_role ur on u.user_id = ur.user_id left join sys_role r on r.role_id = ur.role_id where u.user_id = ?
2025-07-14 18:08:36.550 [http-nio-8080-exec-28] DEBUG c.r.s.m.S.selectUserById - [debug,137] - ==> Parameters: 4949(Long)
2025-07-14 18:08:36.554 [http-nio-8080-exec-28] DEBUG c.r.s.m.S.selectUserById - [debug,137] - <==      Total: 0
2025-07-14 18:08:36.653 [http-nio-8080-exec-33] DEBUG c.r.l.m.f.V.selectById - [debug,137] - ==>  Preparing: SELECT id,form_id,name,status,type,system_type FROM view_form WHERE id=?
2025-07-14 18:08:36.654 [http-nio-8080-exec-33] DEBUG c.r.l.m.f.V.selectById - [debug,137] - ==> Parameters: 1943512918928240641(String)
2025-07-14 18:08:36.657 [http-nio-8080-exec-33] DEBUG c.r.l.m.f.V.selectById - [debug,137] - <==      Total: 1
2025-07-14 18:08:36.657 [http-nio-8080-exec-33] DEBUG c.r.l.m.F.selectList - [debug,137] - ==>  Preparing: SELECT id,form_id,name,table_name,status,type,create_by,create_time,update_by,update_time FROM form_table WHERE (form_id = ?)
2025-07-14 18:08:36.658 [http-nio-8080-exec-33] DEBUG c.r.l.m.F.selectList - [debug,137] - ==> Parameters: 1901586283559489538(String)
2025-07-14 18:08:36.660 [http-nio-8080-exec-33] DEBUG c.r.l.m.F.selectList - [debug,137] - <==      Total: 3
2025-07-14 18:08:36.665 [http-nio-8080-exec-33] DEBUG c.r.l.m.f.V.selectList - [debug,137] - ==>  Preparing: SELECT id,view_form_id,view_form_type,type,name,system_type,options,create_by,create_time,update_by,update_time FROM view_form_config WHERE (view_form_id = ? AND view_form_type = ? AND type = ?)
2025-07-14 18:08:36.665 [http-nio-8080-exec-33] DEBUG c.r.l.m.f.V.selectList - [debug,137] - ==> Parameters: 1943512918928240641(String), list(String), setting(String)
2025-07-14 18:08:36.669 [http-nio-8080-exec-33] DEBUG c.r.l.m.f.V.selectList - [debug,137] - <==      Total: 1
2025-07-14 18:08:36.673 [http-nio-8080-exec-33] INFO  c.r.lowcode.orm.ORM - [selectPageToMap,82] - [ORM] 执行sql: SELECT * FROM xedasd order by id asc LIMIT 0,20
2025-07-14 18:08:36.680 [http-nio-8080-exec-33] INFO  c.r.lowcode.orm.ORM - [selectPageToMap,96] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-14 18:08:36.687 [http-nio-8080-exec-33] DEBUG c.r.l.m.F.selectList - [debug,137] - ==>  Preparing: SELECT id,props,form_id,form_table_id,is_open_index,parent_id,name,component,component_order,jdbc_type,field_order,length,digit,label,status,ftf_is_system,ftf_display,options,description,create_by,create_time,update_by,update_time FROM form_table_field WHERE (form_id = ? AND form_table_id = ?)
2025-07-14 18:08:36.687 [http-nio-8080-exec-33] DEBUG c.r.l.m.F.selectList - [debug,137] - ==> Parameters: 1901586283559489538(String), 1901586283567878146(String)
2025-07-14 18:08:36.692 [http-nio-8080-exec-33] DEBUG c.r.l.m.F.selectList - [debug,137] - <==      Total: 17
2025-07-14 18:08:36.736 [http-nio-8080-exec-28] DEBUG c.r.p.m.P.selectList - [debug,137] - ==>  Preparing: SELECT id,is_used,code,name,task_abbr,status,progress,planned_start_date,planned_end_date,actual_start_date,actual_end_date,work_days,actual_work_days,owner,task_type,plan_weight,milestone,milestone_status,estimated_hours,actual_hours,task_description,project_id,parent_id,ancestors,is_leaf,serial_number,create_by,create_time,update_by,update_time FROM pms_project_task WHERE (project_id IN (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) AND parent_id = ?)
2025-07-14 18:08:36.736 [http-nio-8080-exec-28] DEBUG c.r.p.m.P.selectList - [debug,137] - ==> Parameters: 1(Long), 2(Long), 3(Long), 221(Long), 222(Long), 223(Long), 224(Long), 225(Long), 226(Long), 233(Long), 234(Long), 235(Long), 236(Long), 237(Long), 238(Long), 239(Long), 240(Long), 241(Long), 242(Long), 243(Long), 244(Long), 245(Long), 246(Long), 249(Long), 250(Long), 251(Long), 252(Long), 254(Long), 255(Long), 256(Long), 257(Long), 258(Long), 259(Long), 260(Long), 0(Long)
2025-07-14 18:08:36.743 [http-nio-8080-exec-28] DEBUG c.r.p.m.P.selectList - [debug,137] - <==      Total: 62
2025-07-14 18:08:36.993 [http-nio-8080-exec-34] DEBUG c.r.l.m.f.F.selectById - [debug,137] - ==>  Preparing: SELECT id,name,code,type,group_name,form_status,form_design_config,is_open_mobile,create_by,create_time,update_by,update_time FROM form WHERE id=?
2025-07-14 18:08:36.993 [http-nio-8080-exec-34] DEBUG c.r.l.m.f.F.selectById - [debug,137] - ==> Parameters: 1901586283559489538(String)
2025-07-14 18:08:36.996 [http-nio-8080-exec-34] DEBUG c.r.l.m.f.F.selectById - [debug,137] - <==      Total: 1
2025-07-14 18:08:36.997 [http-nio-8080-exec-34] DEBUG c.r.l.m.F.selectList - [debug,137] - ==>  Preparing: SELECT id,form_id,name,table_name,status,type,create_by,create_time,update_by,update_time FROM form_table WHERE (form_id = ?)
2025-07-14 18:08:36.997 [http-nio-8080-exec-34] DEBUG c.r.l.m.F.selectList - [debug,137] - ==> Parameters: 1901586283559489538(String)
2025-07-14 18:08:37.000 [http-nio-8080-exec-34] DEBUG c.r.l.m.F.selectList - [debug,137] - <==      Total: 3
2025-07-14 18:08:37.002 [http-nio-8080-exec-34] DEBUG c.r.l.m.F.selectList - [debug,137] - ==>  Preparing: SELECT id,props,form_id,form_table_id,is_open_index,parent_id,name,component,component_order,jdbc_type,field_order,length,digit,label,status,ftf_is_system,ftf_display,options,description,create_by,create_time,update_by,update_time FROM form_table_field WHERE (form_id = ?) ORDER BY field_order ASC,id ASC
2025-07-14 18:08:37.002 [http-nio-8080-exec-34] DEBUG c.r.l.m.F.selectList - [debug,137] - ==> Parameters: 1901586283559489538(String)
2025-07-14 18:08:37.011 [http-nio-8080-exec-34] DEBUG c.r.l.m.F.selectList - [debug,137] - <==      Total: 46
