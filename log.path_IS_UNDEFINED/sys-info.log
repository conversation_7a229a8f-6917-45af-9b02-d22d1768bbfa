2025-08-04 09:11:51.586 [lettuce-eventExecutorLoop-1-5] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was /*************:7379
2025-08-04 09:11:51.770 [lettuce-nioEventLoop-7-6] INFO  i.l.c.p.ReconnectionHandler - [lambda$null$3,174] - Reconnected to *************/<unresolved>:7379
2025-08-04 09:58:56.535 [schedule-pool-2] INFO  sys-user - [run,57] - [*************]内网IP[admin][Success][登录成功]
2025-08-04 09:58:57.732 [http-nio-8080-exec-83] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countWaitingTasks(..)] countWaitingTasks 执行耗时: 6 ms
2025-08-04 09:58:57.750 [http-nio-8080-exec-83] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countFinishedTasks(..)] countFinishedTasks 执行耗时: 18 ms
2025-08-04 09:58:57.760 [http-nio-8080-exec-83] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countMyApplyTasks(..)] countMyApplyTasks 执行耗时: 10 ms
2025-08-04 09:58:57.766 [http-nio-8080-exec-83] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countCcTasks(..)] countCcTasks 执行耗时: 5 ms
2025-08-04 09:58:57.766 [http-nio-8080-exec-83] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [PmsOrderServiceImpl.getApprovalStat(..)] getApprovalStat 执行耗时: 43 ms
2025-08-04 09:59:05.699 [http-nio-8080-exec-88] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM project_approval WHERE creator  = ?  order by id asc LIMIT 0,20
2025-08-04 09:59:05.717 [http-nio-8080-exec-88] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: select count(1) as count from project_approval WHERE creator  = ? 
2025-08-04 10:24:04.177 [http-nio-8080-exec-22] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM project_approval WHERE creator  = ?  order by id asc LIMIT 0,20
2025-08-04 10:24:04.183 [http-nio-8080-exec-22] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: select count(1) as count from project_approval WHERE creator  = ? 
2025-08-04 10:24:31.280 [http-nio-8080-exec-35] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM project_approval WHERE creator  = ?  order by id asc LIMIT 0,20
2025-08-04 10:24:31.283 [http-nio-8080-exec-35] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: select count(1) as count from project_approval WHERE creator  = ? 
2025-08-04 10:26:51.827 [http-nio-8080-exec-53] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countWaitingTasks(..)] countWaitingTasks 执行耗时: 78 ms
2025-08-04 10:26:51.851 [http-nio-8080-exec-53] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countFinishedTasks(..)] countFinishedTasks 执行耗时: 24 ms
2025-08-04 10:26:51.856 [http-nio-8080-exec-53] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countMyApplyTasks(..)] countMyApplyTasks 执行耗时: 5 ms
2025-08-04 10:26:51.860 [http-nio-8080-exec-53] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countCcTasks(..)] countCcTasks 执行耗时: 4 ms
2025-08-04 10:26:51.860 [http-nio-8080-exec-53] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [PmsOrderServiceImpl.getApprovalStat(..)] getApprovalStat 执行耗时: 111 ms
2025-08-04 10:30:16.967 [http-nio-8080-exec-68] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countWaitingTasks(..)] countWaitingTasks 执行耗时: 6 ms
2025-08-04 10:30:16.973 [http-nio-8080-exec-68] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countFinishedTasks(..)] countFinishedTasks 执行耗时: 6 ms
2025-08-04 10:30:16.980 [http-nio-8080-exec-68] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countMyApplyTasks(..)] countMyApplyTasks 执行耗时: 7 ms
2025-08-04 10:30:16.984 [http-nio-8080-exec-68] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countCcTasks(..)] countCcTasks 执行耗时: 4 ms
2025-08-04 10:30:16.984 [http-nio-8080-exec-68] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [PmsOrderServiceImpl.getApprovalStat(..)] getApprovalStat 执行耗时: 23 ms
2025-08-04 10:30:22.015 [http-nio-8080-exec-72] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM project_approval WHERE creator  = ?  order by id asc LIMIT 0,20
2025-08-04 10:30:22.028 [http-nio-8080-exec-72] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: select count(1) as count from project_approval WHERE creator  = ? 
2025-08-04 10:30:37.443 [http-nio-8080-exec-86] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM project_approval WHERE creator  = ?  order by id asc LIMIT 0,20
2025-08-04 10:30:37.448 [http-nio-8080-exec-86] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: select count(1) as count from project_approval WHERE creator  = ? 
2025-08-04 10:32:16.904 [http-nio-8080-exec-98] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM project_approval WHERE creator  = ?  order by id asc LIMIT 0,20
2025-08-04 10:32:16.910 [http-nio-8080-exec-98] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: select count(1) as count from project_approval WHERE creator  = ? 
2025-08-04 10:35:15.732 [http-nio-8080-exec-16] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countWaitingTasks(..)] countWaitingTasks 执行耗时: 13 ms
2025-08-04 10:35:15.746 [http-nio-8080-exec-16] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countFinishedTasks(..)] countFinishedTasks 执行耗时: 14 ms
2025-08-04 10:35:15.812 [http-nio-8080-exec-16] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countMyApplyTasks(..)] countMyApplyTasks 执行耗时: 66 ms
2025-08-04 10:35:15.817 [http-nio-8080-exec-16] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countCcTasks(..)] countCcTasks 执行耗时: 4 ms
2025-08-04 10:35:15.817 [http-nio-8080-exec-16] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [PmsOrderServiceImpl.getApprovalStat(..)] getApprovalStat 执行耗时: 98 ms
2025-08-04 10:41:12.160 [http-nio-8080-exec-32] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM project_approval WHERE creator  = ?  order by id asc LIMIT 0,20
2025-08-04 10:41:12.174 [http-nio-8080-exec-32] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: select count(1) as count from project_approval WHERE creator  = ? 
2025-08-04 10:58:37.465 [http-nio-8080-exec-79] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM project_approval WHERE creator  = ?  order by id asc LIMIT 0,20
2025-08-04 10:58:37.472 [http-nio-8080-exec-79] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: select count(1) as count from project_approval WHERE creator  = ? 
2025-08-04 11:08:12.179 [http-nio-8080-exec-31] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM project_approval WHERE creator  = ?  order by id asc LIMIT 0,20
2025-08-04 11:08:12.184 [http-nio-8080-exec-31] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: select count(1) as count from project_approval WHERE creator  = ? 
2025-08-04 11:09:30.420 [http-nio-8080-exec-39] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM xedasd order by id asc LIMIT 0,20
2025-08-04 11:09:30.512 [http-nio-8080-exec-39] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: select count(1) as count from xedasd
2025-08-04 11:09:32.585 [http-nio-8080-exec-45] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-08-04 11:09:32.605 [http-nio-8080-exec-45] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-08-04 11:09:36.950 [http-nio-8080-exec-42] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-08-04 11:09:36.962 [http-nio-8080-exec-42] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-08-04 11:09:37.332 [http-nio-8080-exec-41] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-08-04 11:09:37.343 [http-nio-8080-exec-41] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-08-04 11:09:39.229 [http-nio-8080-exec-44] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-08-04 11:09:39.240 [http-nio-8080-exec-44] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-08-04 11:09:41.431 [http-nio-8080-exec-43] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM project_approval WHERE creator  = ?  order by id asc LIMIT 0,20
2025-08-04 11:09:41.435 [http-nio-8080-exec-43] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: select count(1) as count from project_approval WHERE creator  = ? 
2025-08-04 11:09:46.538 [http-nio-8080-exec-61] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM project_approval WHERE creator  = ?  order by id asc LIMIT 0,20
2025-08-04 11:09:46.544 [http-nio-8080-exec-61] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: select count(1) as count from project_approval WHERE creator  = ? 
2025-08-04 11:10:29.125 [http-nio-8080-exec-77] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM project_approval WHERE creator  = ?  order by id asc LIMIT 0,20
2025-08-04 11:10:29.131 [http-nio-8080-exec-77] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: select count(1) as count from project_approval WHERE creator  = ? 
2025-08-04 11:15:14.412 [http-nio-8080-exec-3] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM project_approval WHERE creator  = ?  order by id asc LIMIT 0,20
2025-08-04 11:15:14.417 [http-nio-8080-exec-3] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: select count(1) as count from project_approval WHERE creator  = ? 
2025-08-04 11:44:00.385 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-08-04 11:44:00.387 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-08-04 11:44:00.387 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-08-04 11:44:00.387 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-08-04 11:44:00.387 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-08-04 11:44:00.387 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-08-04 11:44:00.387 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-08-04 11:44:00.387 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-08-04 11:44:00.387 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-08-04 11:44:00.582 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-08-04 11:44:00.582 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-08-04 11:44:00.582 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-08-04 11:44:00.592 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-08-04 11:44:00.626 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-08-04 11:44:00.638 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
