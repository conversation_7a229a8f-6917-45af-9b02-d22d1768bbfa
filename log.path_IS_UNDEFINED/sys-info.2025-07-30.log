2025-07-30 09:35:54.301 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 09:35:54.352 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 09:35:54.353 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 09:35:54.353 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 09:35:54.353 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 09:35:54.353 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 09:35:54.353 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 09:35:54.353 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 09:35:54.354 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 09:35:54.529 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-30 09:35:54.529 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 09:35:54.530 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-30 09:35:54.542 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-30 09:35:54.582 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-30 09:35:54.585 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-30 09:35:59.564 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-30 09:35:59.576 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 42005 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-30 09:35:59.578 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-30 09:36:01.283 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-30 09:36:01.284 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-30 09:36:01.284 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-30 09:36:01.317 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-30 09:36:01.922 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-30 09:36:02.634 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-30 09:36:02.739 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for 192.168.61.10/192.168.61.10:7379
2025-07-30 09:36:02.782 [redisson-netty-4-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for 192.168.61.10/192.168.61.10:7379
2025-07-30 09:36:06.133 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-30 09:36:07.217 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-30 09:36:07.279 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-30 09:36:07.280 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 09:36:07.280 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 09:36:07.415 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 09:36:07.440 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-30 09:36:09.732 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-30 09:36:09.741 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-30 09:36:09.741 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-30 09:36:09.741 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-30 09:36:09.741 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-30 09:36:09.742 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-30 09:36:09.742 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-30 09:36:09.742 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@6e2a6063
2025-07-30 09:36:11.271 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-30 09:36:11.283 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 09:36:11.391 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 09:36:11.391 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 09:36:11.426 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 09:36:11.426 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 09:36:17.532 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 09:36:17.532 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 09:36:17.546 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 09:36:17.546 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-30 09:36:17.554 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 18.137 seconds (process running for 19.439)
2025-07-30 09:36:17.658 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-30 09:36:17.752 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 90 ms to scan 26 urls, producing 298 keys and 1816 values
2025-07-30 09:36:17.767 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 1 ms to scan 2 urls, producing 14 keys and 37 values
2025-07-30 09:36:18.027 [RMI TCP Connection(3)-*************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 09:43:53.676 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 09:43:53.677 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 09:43:53.677 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 09:43:53.677 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 09:43:53.677 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 09:43:53.677 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 09:43:53.677 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 09:43:53.677 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 09:43:53.678 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 09:43:53.818 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-30 09:43:53.818 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 09:43:53.818 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-30 09:43:53.827 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-30 09:43:53.859 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-30 09:43:53.878 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-30 09:43:56.546 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-30 09:43:56.555 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 42492 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-30 09:43:56.556 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-30 09:43:58.160 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-30 09:43:58.161 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-30 09:43:58.161 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-30 09:43:58.194 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-30 09:43:58.777 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-30 09:43:59.443 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-30 09:43:59.558 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for 192.168.61.10/192.168.61.10:7379
2025-07-30 09:43:59.599 [redisson-netty-4-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for 192.168.61.10/192.168.61.10:7379
2025-07-30 09:44:03.141 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-30 09:44:04.398 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-30 09:44:04.514 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-30 09:44:04.515 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 09:44:04.515 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 09:44:04.856 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 09:44:04.884 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-30 09:44:08.532 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-30 09:44:08.541 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-30 09:44:08.541 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-30 09:44:08.541 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-30 09:44:08.542 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-30 09:44:08.542 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-30 09:44:08.542 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-30 09:44:08.542 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@564c0e38
2025-07-30 09:44:10.850 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-30 09:44:10.861 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 09:44:10.957 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 09:44:10.957 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 09:44:10.994 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 09:44:10.995 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 09:44:16.925 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 09:44:16.926 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 09:44:16.939 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 09:44:16.939 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-30 09:44:16.951 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 20.577 seconds (process running for 21.275)
2025-07-30 09:44:17.040 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 75 ms to scan 26 urls, producing 298 keys and 1816 values
2025-07-30 09:44:17.049 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 1 ms to scan 2 urls, producing 14 keys and 37 values
2025-07-30 09:44:17.192 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-30 09:44:17.744 [RMI TCP Connection(3)-*************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 09:47:06.481 [schedule-pool-1] INFO  sys-user - [run,57] - [127.0.0.1]内网IP[admin][Error][密码输入错误1次]
2025-07-30 09:47:06.496 [schedule-pool-2] INFO  sys-user - [run,57] - [127.0.0.1]内网IP[admin][Error][用户不存在/密码错误, username: admin, password: admin1`2]
2025-07-30 09:47:09.991 [schedule-pool-1] INFO  sys-user - [run,57] - [127.0.0.1]内网IP[admin][Success][登录成功]
2025-07-30 09:51:15.884 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 09:51:15.886 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 09:51:15.886 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 09:51:15.886 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 09:51:15.886 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 09:51:15.886 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 09:51:15.886 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 09:51:15.886 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 09:51:15.886 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 09:51:16.054 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-30 09:51:16.054 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 09:51:16.054 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-30 09:51:16.062 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-30 09:51:16.081 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-30 09:51:16.099 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-30 09:51:19.108 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-30 09:51:19.137 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 43072 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-30 09:51:19.139 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-30 09:51:20.831 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-30 09:51:20.832 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-30 09:51:20.832 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-30 09:51:20.864 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-30 09:51:21.451 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-30 09:51:22.175 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-30 09:51:22.279 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for 192.168.61.10/192.168.61.10:7379
2025-07-30 09:51:22.335 [redisson-netty-4-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for 192.168.61.10/192.168.61.10:7379
2025-07-30 09:51:25.780 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-30 09:51:26.914 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-30 09:51:26.976 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-30 09:51:26.976 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 09:51:26.976 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 09:51:27.081 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 09:51:27.103 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-30 09:51:29.308 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-30 09:51:29.313 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-30 09:51:29.313 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-30 09:51:29.314 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-30 09:51:29.314 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-30 09:51:29.314 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-30 09:51:29.314 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-30 09:51:29.314 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@2693d31f
2025-07-30 09:51:30.723 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-30 09:51:30.740 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 09:51:30.832 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 09:51:30.832 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 09:51:30.867 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 09:51:30.867 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 09:51:34.281 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 09:51:37.882 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 09:51:37.882 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 09:51:37.894 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 09:51:37.894 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-30 09:51:37.901 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 18.948 seconds (process running for 19.574)
2025-07-30 09:51:37.976 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-30 09:51:38.043 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 63 ms to scan 26 urls, producing 298 keys and 1816 values
2025-07-30 09:51:38.051 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 1 ms to scan 2 urls, producing 14 keys and 37 values
2025-07-30 09:56:29.947 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 09:56:29.950 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 09:56:29.950 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 09:56:29.950 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 09:56:29.950 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 09:56:29.950 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 09:56:29.950 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 09:56:29.950 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 09:56:29.951 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 09:56:30.118 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-30 09:56:30.118 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 09:56:30.118 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-30 09:56:30.127 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-30 09:56:30.158 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-30 09:56:30.183 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-30 09:56:33.855 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-30 09:56:33.867 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 43417 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-30 09:56:33.867 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-30 09:56:35.508 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-30 09:56:35.509 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-30 09:56:35.509 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-30 09:56:35.541 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-30 09:56:36.142 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-30 09:56:36.820 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-30 09:56:36.932 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for 192.168.61.10/192.168.61.10:7379
2025-07-30 09:56:36.974 [redisson-netty-4-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for 192.168.61.10/192.168.61.10:7379
2025-07-30 09:56:40.918 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-30 09:56:42.033 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-30 09:56:42.093 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-30 09:56:42.094 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 09:56:42.094 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 09:56:42.199 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 09:56:42.217 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-30 09:56:44.397 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-30 09:56:44.403 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-30 09:56:44.403 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-30 09:56:44.404 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-30 09:56:44.404 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-30 09:56:44.404 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-30 09:56:44.404 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-30 09:56:44.404 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@45f854e9
2025-07-30 09:56:45.829 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-30 09:56:45.849 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 09:56:45.961 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 09:56:45.961 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 09:56:46.110 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 09:56:46.110 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 09:56:54.428 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 09:56:54.429 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 09:56:54.442 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 09:56:54.442 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-30 09:56:54.450 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 20.74 seconds (process running for 21.428)
2025-07-30 09:56:54.544 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 85 ms to scan 26 urls, producing 298 keys and 1816 values
2025-07-30 09:56:54.552 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 1 ms to scan 2 urls, producing 14 keys and 37 values
2025-07-30 09:56:54.641 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-30 09:56:55.136 [RMI TCP Connection(1)-*************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 10:04:32.494 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 10:04:32.495 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 10:04:32.495 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 10:04:32.495 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 10:04:32.495 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 10:04:32.495 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 10:04:32.495 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 10:04:32.495 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 10:04:32.495 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 10:04:32.661 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-30 10:04:32.662 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 10:04:32.662 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-30 10:04:32.672 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-30 10:04:32.699 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-30 10:04:32.729 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-30 10:04:36.354 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-30 10:04:36.363 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 43937 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-30 10:04:36.363 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-30 10:04:37.948 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-30 10:04:37.949 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-30 10:04:37.949 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-30 10:04:37.979 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-30 10:04:38.573 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-30 10:04:39.257 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-30 10:04:39.350 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for 192.168.61.10/192.168.61.10:7379
2025-07-30 10:04:39.396 [redisson-netty-4-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for 192.168.61.10/192.168.61.10:7379
2025-07-30 10:04:42.630 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-30 10:04:43.652 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-30 10:04:43.721 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-30 10:04:43.721 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 10:04:43.722 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 10:04:43.836 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 10:04:43.853 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-30 10:04:46.030 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-30 10:04:46.036 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-30 10:04:46.036 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-30 10:04:46.036 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-30 10:04:46.036 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-30 10:04:46.036 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-30 10:04:46.036 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-30 10:04:46.037 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@28ab0789
2025-07-30 10:04:47.417 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-30 10:04:47.428 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 10:04:47.522 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 10:04:47.522 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 10:04:47.557 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 10:04:47.557 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 10:04:54.018 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 10:04:54.019 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 10:04:54.034 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 10:04:54.034 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-30 10:04:54.042 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 17.847 seconds (process running for 18.489)
2025-07-30 10:04:54.108 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 59 ms to scan 26 urls, producing 298 keys and 1816 values
2025-07-30 10:04:54.116 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 1 ms to scan 2 urls, producing 14 keys and 37 values
2025-07-30 10:04:54.201 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-30 10:04:54.596 [RMI TCP Connection(1)-*************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 10:17:52.192 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 10:17:52.193 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 10:17:52.193 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 10:17:52.193 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 10:17:52.193 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 10:17:52.193 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 10:17:52.193 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 10:17:52.193 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 10:17:52.193 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 10:17:52.345 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-30 10:17:52.345 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 10:17:52.345 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-30 10:17:52.357 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-30 10:17:52.378 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-30 10:17:52.397 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-30 10:17:56.797 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-30 10:17:56.810 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 44881 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-30 10:17:56.810 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-30 10:17:58.508 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-30 10:17:58.509 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-30 10:17:58.509 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-30 10:17:58.542 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-30 10:17:59.175 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-30 10:17:59.923 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-30 10:18:00.029 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for 192.168.61.10/192.168.61.10:7379
2025-07-30 10:18:00.069 [redisson-netty-4-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for 192.168.61.10/192.168.61.10:7379
2025-07-30 10:18:03.232 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-30 10:18:04.312 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-30 10:18:04.373 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-30 10:18:04.374 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 10:18:04.374 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 10:18:04.504 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 10:18:04.526 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-30 10:18:06.754 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-30 10:18:06.759 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-30 10:18:06.759 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-30 10:18:06.759 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-30 10:18:06.759 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-30 10:18:06.759 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-30 10:18:06.759 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-30 10:18:06.759 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@bb0ca30
2025-07-30 10:18:08.174 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-30 10:18:08.188 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 10:18:08.281 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 10:18:08.281 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 10:18:08.315 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 10:18:08.316 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 10:18:14.432 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 10:18:14.437 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 10:18:14.439 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 10:18:14.461 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 10:18:14.462 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-30 10:18:14.476 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 17.831 seconds (process running for 19.107)
2025-07-30 10:18:14.630 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-30 10:18:14.765 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 130 ms to scan 26 urls, producing 298 keys and 1816 values
2025-07-30 10:18:14.780 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 2 ms to scan 2 urls, producing 14 keys and 37 values
2025-07-30 10:27:34.930 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 10:27:34.931 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 10:27:34.931 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 10:27:34.931 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 10:27:34.931 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 10:27:34.931 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 10:27:34.931 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 10:27:34.931 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 10:27:34.931 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 10:27:35.096 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-30 10:27:35.096 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 10:27:35.096 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-30 10:27:35.104 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-30 10:27:35.130 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-30 10:27:35.184 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-30 10:27:38.882 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-30 10:27:38.891 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 45608 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-30 10:27:38.892 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-30 10:27:40.523 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-30 10:27:40.524 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-30 10:27:40.524 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-30 10:27:40.556 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-30 10:27:41.160 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-30 10:27:41.863 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-30 10:27:41.968 [redisson-netty-4-19] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for 192.168.61.10/192.168.61.10:7379
2025-07-30 10:27:42.007 [redisson-netty-4-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for 192.168.61.10/192.168.61.10:7379
2025-07-30 10:27:45.100 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-30 10:27:46.088 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-30 10:27:46.150 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-30 10:27:46.150 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 10:27:46.150 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 10:27:46.273 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 10:27:46.290 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-30 10:27:48.426 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-30 10:27:48.432 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-30 10:27:48.432 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-30 10:27:48.432 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-30 10:27:48.432 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-30 10:27:48.432 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-30 10:27:48.432 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-30 10:27:48.432 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@18211894
2025-07-30 10:27:49.863 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-30 10:27:49.881 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 10:27:49.979 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 10:27:49.979 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 10:27:50.016 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 10:27:50.017 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 10:27:57.381 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 10:27:57.382 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 10:27:57.394 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 10:27:57.394 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-30 10:27:57.401 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 18.672 seconds (process running for 19.308)
2025-07-30 10:27:57.487 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-30 10:27:57.575 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 83 ms to scan 26 urls, producing 298 keys and 1816 values
2025-07-30 10:27:57.586 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 1 ms to scan 2 urls, producing 14 keys and 37 values
2025-07-30 10:27:58.140 [RMI TCP Connection(4)-*************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 10:47:18.331 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 10:47:18.332 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 10:47:18.332 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 10:47:18.332 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 10:47:18.332 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 10:47:18.332 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 10:47:18.332 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 10:47:18.332 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 10:47:18.332 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 10:47:18.503 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-30 10:47:18.504 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 10:47:18.504 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-30 10:47:18.512 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-30 10:47:18.560 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-30 10:47:18.561 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-30 10:47:22.677 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-30 10:47:22.694 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 46840 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-30 10:47:22.695 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-30 10:47:24.705 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-30 10:47:24.706 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-30 10:47:24.706 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-30 10:47:24.737 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-30 10:47:25.388 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-30 10:47:26.104 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-30 10:47:26.207 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for 192.168.61.10/192.168.61.10:7379
2025-07-30 10:47:26.249 [redisson-netty-4-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for 192.168.61.10/192.168.61.10:7379
2025-07-30 10:47:29.655 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-30 10:47:30.786 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-30 10:47:30.849 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-30 10:47:30.849 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 10:47:30.849 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 10:47:30.994 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 10:47:31.013 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-30 10:47:33.364 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-30 10:47:33.369 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-30 10:47:33.369 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-30 10:47:33.370 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-30 10:47:33.370 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-30 10:47:33.370 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-30 10:47:33.370 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-30 10:47:33.370 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@a353dff
2025-07-30 10:47:34.786 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-30 10:47:34.804 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 10:47:34.909 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 10:47:34.910 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 10:47:34.948 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 10:47:34.948 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 10:47:42.333 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 10:47:42.334 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 10:47:42.347 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 10:47:42.348 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-30 10:47:42.358 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 19.834 seconds (process running for 21.115)
2025-07-30 10:47:42.512 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 88 ms to scan 26 urls, producing 298 keys and 1816 values
2025-07-30 10:47:42.521 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 1 ms to scan 2 urls, producing 14 keys and 37 values
2025-07-30 10:47:42.721 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-30 10:49:31.406 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 10:52:34.784 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 10:52:34.785 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 10:52:34.785 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 10:52:34.785 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 10:52:34.785 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 10:52:34.786 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 10:52:34.786 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 10:52:34.786 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 10:52:34.786 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 10:52:34.938 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-30 10:52:34.938 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 10:52:34.938 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-30 10:52:34.947 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-30 10:52:34.977 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-30 10:52:34.995 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-30 10:52:38.598 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-30 10:52:38.609 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 47106 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-30 10:52:38.610 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-30 10:52:40.224 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-30 10:52:40.225 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-30 10:52:40.225 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-30 10:52:40.265 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-30 10:52:41.018 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-30 10:52:41.719 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-30 10:52:41.819 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for 192.168.61.10/192.168.61.10:7379
2025-07-30 10:52:41.862 [redisson-netty-4-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for 192.168.61.10/192.168.61.10:7379
2025-07-30 10:52:45.577 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-30 10:52:46.610 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-30 10:52:46.672 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-30 10:52:46.672 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 10:52:46.672 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 10:52:46.816 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 10:52:46.851 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-30 10:52:49.235 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-30 10:52:49.241 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-30 10:52:49.241 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-30 10:52:49.241 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-30 10:52:49.241 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-30 10:52:49.242 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-30 10:52:49.242 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-30 10:52:49.242 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@28ab0789
2025-07-30 10:52:50.872 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-30 10:52:50.891 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 10:52:50.986 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 10:52:50.986 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 10:52:51.023 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 10:52:51.023 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 10:52:57.026 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 10:52:57.026 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 10:52:57.039 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 10:52:57.040 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-30 10:52:57.048 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 18.601 seconds (process running for 19.257)
2025-07-30 10:52:57.134 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 79 ms to scan 26 urls, producing 298 keys and 1816 values
2025-07-30 10:52:57.145 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 1 ms to scan 2 urls, producing 14 keys and 37 values
2025-07-30 10:52:57.253 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-30 10:52:57.781 [RMI TCP Connection(7)-*************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 10:53:04.385 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 10:53:04.386 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 10:53:04.386 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 10:53:04.386 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 10:53:04.386 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 10:53:04.386 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 10:53:04.386 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 10:53:04.386 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 10:53:04.387 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 10:53:04.526 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-30 10:53:04.526 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 10:53:04.527 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-30 10:53:04.534 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-30 10:53:04.557 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-30 10:53:04.577 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-30 10:53:07.605 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-30 10:53:07.615 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 47143 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-30 10:53:07.616 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-30 10:53:09.198 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-30 10:53:09.199 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-30 10:53:09.199 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-30 10:53:09.248 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-30 10:53:09.951 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-30 10:53:10.628 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-30 10:53:10.747 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for 192.168.61.10/192.168.61.10:7379
2025-07-30 10:53:10.795 [redisson-netty-4-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for 192.168.61.10/192.168.61.10:7379
2025-07-30 10:53:13.852 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-30 10:53:14.876 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-30 10:53:14.939 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-30 10:53:14.939 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 10:53:14.939 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 10:53:15.048 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 10:53:15.072 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-30 10:53:17.248 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-30 10:53:17.253 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-30 10:53:17.253 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-30 10:53:17.253 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-30 10:53:17.254 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-30 10:53:17.254 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-30 10:53:17.254 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-30 10:53:17.254 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@706b68e6
2025-07-30 10:53:18.704 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-30 10:53:18.717 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 10:53:18.821 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 10:53:18.821 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 10:53:18.858 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 10:53:18.858 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 10:53:22.517 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 10:53:24.666 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 10:53:24.667 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 10:53:24.680 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 10:53:24.680 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-30 10:53:24.689 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 17.236 seconds (process running for 17.837)
2025-07-30 10:53:24.802 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-30 10:53:24.904 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 95 ms to scan 26 urls, producing 298 keys and 1816 values
2025-07-30 10:53:24.917 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 1 ms to scan 2 urls, producing 14 keys and 37 values
2025-07-30 11:29:41.913 [schedule-pool-1] INFO  sys-user - [run,57] - [*************]内网IP[admin][Success][登录成功]
2025-07-30 11:41:32.160 [http-nio-8080-exec-76] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM xedasd order by id asc LIMIT 0,20
2025-07-30 11:41:32.170 [http-nio-8080-exec-76] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-30 11:41:34.298 [http-nio-8080-exec-75] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:41:34.313 [http-nio-8080-exec-75] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:41:34.622 [http-nio-8080-exec-74] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:41:34.634 [http-nio-8080-exec-74] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:41:36.836 [http-nio-8080-exec-77] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:41:36.846 [http-nio-8080-exec-77] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:41:37.257 [http-nio-8080-exec-79] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:41:37.267 [http-nio-8080-exec-79] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:41:37.460 [http-nio-8080-exec-80] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:41:37.470 [http-nio-8080-exec-80] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:41:37.872 [http-nio-8080-exec-78] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:41:37.884 [http-nio-8080-exec-78] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:41:37.956 [http-nio-8080-exec-81] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:41:37.966 [http-nio-8080-exec-81] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:41:38.154 [http-nio-8080-exec-82] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:41:38.164 [http-nio-8080-exec-82] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:41:38.688 [http-nio-8080-exec-84] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:41:38.698 [http-nio-8080-exec-84] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:41:39.517 [http-nio-8080-exec-86] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:41:39.528 [http-nio-8080-exec-86] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:41:43.299 [http-nio-8080-exec-85] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:41:43.312 [http-nio-8080-exec-85] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:41:50.154 [http-nio-8080-exec-83] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:41:50.162 [http-nio-8080-exec-83] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:41:50.571 [http-nio-8080-exec-87] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:41:50.579 [http-nio-8080-exec-87] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:41:52.411 [http-nio-8080-exec-89] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:41:52.420 [http-nio-8080-exec-89] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:41:52.925 [http-nio-8080-exec-90] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:41:52.936 [http-nio-8080-exec-90] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:41:53.640 [http-nio-8080-exec-88] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:41:53.649 [http-nio-8080-exec-88] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:42:04.604 [http-nio-8080-exec-91] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:42:04.619 [http-nio-8080-exec-91] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:42:10.676 [http-nio-8080-exec-92] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:42:10.686 [http-nio-8080-exec-92] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:42:11.304 [http-nio-8080-exec-93] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:42:11.313 [http-nio-8080-exec-93] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:42:14.119 [http-nio-8080-exec-94] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:42:14.129 [http-nio-8080-exec-94] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:42:14.634 [http-nio-8080-exec-99] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:42:14.643 [http-nio-8080-exec-99] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:42:15.851 [http-nio-8080-exec-96] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:42:15.860 [http-nio-8080-exec-96] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:42:17.345 [http-nio-8080-exec-95] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:42:17.353 [http-nio-8080-exec-95] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:42:18.111 [http-nio-8080-exec-97] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:42:18.119 [http-nio-8080-exec-97] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:42:20.372 [http-nio-8080-exec-1] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:42:20.383 [http-nio-8080-exec-1] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:42:37.200 [http-nio-8080-exec-4] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM project_approval WHERE creator  = ?  order by id asc LIMIT 0,20
2025-07-30 11:42:37.206 [http-nio-8080-exec-4] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: select count(1) as count from project_approval WHERE creator  = ? 
2025-07-30 11:42:42.884 [http-nio-8080-exec-8] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM xedasd order by id asc LIMIT 0,20
2025-07-30 11:42:42.888 [http-nio-8080-exec-8] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-30 11:42:44.329 [http-nio-8080-exec-11] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:42:44.337 [http-nio-8080-exec-11] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:42:45.573 [http-nio-8080-exec-13] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:42:45.581 [http-nio-8080-exec-13] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:42:46.375 [http-nio-8080-exec-12] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:42:46.384 [http-nio-8080-exec-12] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:42:48.854 [http-nio-8080-exec-14] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:42:48.863 [http-nio-8080-exec-14] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:42:49.546 [http-nio-8080-exec-17] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:42:49.554 [http-nio-8080-exec-17] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:42:52.057 [http-nio-8080-exec-15] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:42:52.067 [http-nio-8080-exec-15] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:42:52.540 [http-nio-8080-exec-16] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:42:52.549 [http-nio-8080-exec-16] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 11:50:50.704 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 11:50:50.705 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 11:50:50.705 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 11:50:50.706 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 11:50:50.706 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 11:50:50.706 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 11:50:50.706 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 11:50:50.706 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 11:50:50.706 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 11:50:50.879 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-30 11:50:50.879 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 11:50:50.879 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-30 11:50:50.888 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-30 11:50:50.916 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-30 11:50:50.928 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-30 11:50:55.280 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-30 11:50:55.293 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 51277 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-30 11:50:55.293 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-30 11:50:56.976 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-30 11:50:56.976 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-30 11:50:56.976 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-30 11:50:57.007 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-30 11:50:57.628 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-30 11:50:58.344 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-30 11:50:58.455 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for 192.168.61.10/192.168.61.10:7379
2025-07-30 11:50:58.497 [redisson-netty-4-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for 192.168.61.10/192.168.61.10:7379
2025-07-30 11:51:01.772 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-30 11:51:02.874 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-30 11:51:02.938 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-30 11:51:02.938 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 11:51:02.938 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 11:51:03.049 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 11:51:03.064 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-30 11:51:05.235 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-30 11:51:05.241 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-30 11:51:05.241 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-30 11:51:05.241 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-30 11:51:05.242 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-30 11:51:05.242 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-30 11:51:05.242 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-30 11:51:05.242 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@3d2bb013
2025-07-30 11:51:06.647 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-30 11:51:06.659 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 11:51:06.754 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 11:51:06.754 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 11:51:06.788 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 11:51:06.788 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 11:51:12.234 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 11:51:12.677 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 11:51:12.677 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 11:51:12.714 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 11:51:12.715 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-30 11:51:12.748 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 17.628 seconds (process running for 18.894)
2025-07-30 11:51:12.888 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 106 ms to scan 26 urls, producing 298 keys and 1816 values
2025-07-30 11:51:12.908 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 5 ms to scan 2 urls, producing 14 keys and 37 values
2025-07-30 11:51:13.038 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-30 11:51:17.104 [schedule-pool-1] INFO  sys-user - [run,57] - [127.0.0.1]内网IP[admin][Success][登录成功]
2025-07-30 14:43:30.408 [http-nio-8080-exec-98] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countWaitingTasks(..)] countWaitingTasks 执行耗时: 8 ms
2025-07-30 14:43:30.427 [http-nio-8080-exec-98] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countFinishedTasks(..)] countFinishedTasks 执行耗时: 18 ms
2025-07-30 14:43:30.436 [http-nio-8080-exec-98] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countMyApplyTasks(..)] countMyApplyTasks 执行耗时: 9 ms
2025-07-30 14:43:30.439 [http-nio-8080-exec-98] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countCcTasks(..)] countCcTasks 执行耗时: 3 ms
2025-07-30 14:43:30.439 [http-nio-8080-exec-98] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [PmsOrderServiceImpl.getApprovalStat(..)] getApprovalStat 执行耗时: 39 ms
2025-07-30 14:50:04.341 [http-nio-8080-exec-27] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM project_approval WHERE creator  = ?  order by id asc LIMIT 0,20
2025-07-30 14:50:04.359 [http-nio-8080-exec-27] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: select count(1) as count from project_approval WHERE creator  = ? 
2025-07-30 14:51:31.031 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 14:51:31.032 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 14:51:31.032 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 14:51:31.032 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 14:51:31.032 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 14:51:31.032 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 14:51:31.032 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 14:51:31.032 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 14:51:31.032 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 14:51:31.210 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-30 14:51:31.211 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 14:51:31.211 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-30 14:51:31.221 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-30 14:51:31.245 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-30 14:51:31.262 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-30 14:51:53.226 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-30 14:51:53.248 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 61303 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-30 14:51:53.249 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-30 14:51:54.888 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-30 14:51:54.889 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-30 14:51:54.889 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-30 14:51:54.921 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-30 14:51:55.513 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-30 14:51:56.209 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-30 14:51:56.307 [redisson-netty-4-19] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for 192.168.61.10/192.168.61.10:7379
2025-07-30 14:51:56.344 [redisson-netty-4-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for 192.168.61.10/192.168.61.10:7379
2025-07-30 14:51:59.559 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-30 14:52:00.640 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-30 14:52:00.699 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-30 14:52:00.699 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 14:52:00.699 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 14:52:00.825 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 14:52:00.840 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-30 14:52:02.969 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-30 14:52:02.976 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-30 14:52:02.976 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-30 14:52:02.976 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-30 14:52:02.977 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-30 14:52:02.977 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-30 14:52:02.977 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-30 14:52:02.977 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@4236d55
2025-07-30 14:52:04.386 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-30 14:52:04.393 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 14:52:04.492 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 14:52:04.492 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 14:52:04.531 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 14:52:04.531 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 14:52:09.858 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 14:52:09.859 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 14:52:09.877 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 14:52:09.878 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-30 14:52:09.900 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 16.847 seconds (process running for 17.274)
2025-07-30 14:52:09.902 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 14:52:09.903 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 14:52:09.903 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 14:52:09.903 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 14:52:09.903 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 14:52:09.903 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 14:52:09.903 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 14:52:09.903 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 14:52:09.903 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 14:52:09.991 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 75 ms to scan 26 urls, producing 298 keys and 1816 values
2025-07-30 14:52:09.999 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 1 ms to scan 2 urls, producing 14 keys and 37 values
2025-07-30 14:52:10.027 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-30 14:52:10.027 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 14:52:10.027 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-30 14:52:10.033 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-30 14:52:10.053 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-30 14:52:10.069 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-30 14:52:13.542 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-30 14:52:13.553 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 61324 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-30 14:52:13.554 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-30 14:52:14.995 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-30 14:52:14.995 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-30 14:52:14.995 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-30 14:52:15.026 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-30 14:52:15.649 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-30 14:52:16.341 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-30 14:52:16.441 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for 192.168.61.10/192.168.61.10:7379
2025-07-30 14:52:16.481 [redisson-netty-4-20] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for 192.168.61.10/192.168.61.10:7379
2025-07-30 14:52:19.664 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-30 14:52:20.742 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-30 14:52:20.804 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-30 14:52:20.805 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 14:52:20.805 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 14:52:20.938 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 14:52:20.956 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-30 14:52:23.133 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-30 14:52:23.138 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-30 14:52:23.138 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-30 14:52:23.139 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-30 14:52:23.139 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-30 14:52:23.139 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-30 14:52:23.139 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-30 14:52:23.139 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@543eacd5
2025-07-30 14:52:24.526 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-30 14:52:24.538 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 14:52:24.641 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 14:52:24.641 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 14:52:24.676 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 14:52:24.677 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 14:52:30.970 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 14:52:30.972 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 14:52:30.986 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 14:52:30.986 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-30 14:52:31.001 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 17.596 seconds (process running for 18.749)
2025-07-30 14:52:31.164 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 135 ms to scan 26 urls, producing 298 keys and 1816 values
2025-07-30 14:52:31.197 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 1 ms to scan 2 urls, producing 14 keys and 37 values
2025-07-30 14:52:31.788 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-30 14:52:32.079 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 14:55:33.461 [http-nio-8080-exec-48] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM project_approval WHERE creator  = ?  order by id asc LIMIT 0,20
2025-07-30 14:55:33.471 [http-nio-8080-exec-48] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: select count(1) as count from project_approval WHERE creator  = ? 
2025-07-30 15:01:12.316 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 15:01:12.318 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 15:01:12.318 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 15:01:12.319 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 15:01:12.319 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 15:01:12.319 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 15:01:12.319 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 15:01:12.319 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 15:01:12.320 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 15:01:12.471 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-30 15:01:12.471 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 15:01:12.471 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-30 15:01:12.500 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-30 15:01:12.531 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-30 15:01:12.547 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-30 15:01:19.049 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-30 15:01:19.066 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 61947 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-30 15:01:19.068 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-30 15:01:20.905 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-30 15:01:20.906 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-30 15:01:20.906 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-30 15:01:20.940 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-30 15:01:21.589 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-30 15:01:22.304 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-30 15:01:22.410 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for 192.168.61.10/192.168.61.10:7379
2025-07-30 15:01:22.445 [redisson-netty-4-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for 192.168.61.10/192.168.61.10:7379
2025-07-30 15:01:25.594 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-30 15:01:26.736 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-30 15:01:26.801 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-30 15:01:26.802 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 15:01:26.802 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 15:01:26.945 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 15:01:26.967 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-30 15:01:29.485 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-30 15:01:29.491 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-30 15:01:29.491 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-30 15:01:29.492 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-30 15:01:29.492 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-30 15:01:29.492 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-30 15:01:29.492 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-30 15:01:29.493 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@164fba04
2025-07-30 15:01:31.178 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-30 15:01:31.190 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 15:01:31.289 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 15:01:31.289 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 15:01:31.325 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 15:01:31.325 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 15:01:37.136 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 15:01:37.139 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 15:01:37.159 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 15:01:37.159 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-30 15:01:37.174 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 18.283 seconds (process running for 19.533)
2025-07-30 15:01:37.375 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-30 15:01:37.480 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 98 ms to scan 26 urls, producing 298 keys and 1816 values
2025-07-30 15:01:37.491 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 1 ms to scan 2 urls, producing 14 keys and 37 values
2025-07-30 15:01:37.781 [RMI TCP Connection(7)-*************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 15:07:11.740 [http-nio-8080-exec-29] INFO  o.a.e.i.c.DeployCmd - [executeDeploy,103] - Launching new deployment with version: 1
2025-07-30 15:07:11.811 [http-nio-8080-exec-29] INFO  o.a.e.i.b.d.BpmnDeployer - [dispatchProcessDefinitionEntityInitializedEvent,240] - Process deployed: {id: Process_1:115:d07a7dab-6d13-11f0-bae3-ee9eca73c30d, key: Process_1, name: 1 }
2025-07-30 15:10:54.065 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 15:10:54.067 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 15:10:54.067 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 15:10:54.067 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 15:10:54.067 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 15:10:54.067 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 15:10:54.067 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 15:10:54.067 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 15:10:54.067 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 15:10:54.250 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-30 15:10:54.251 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 15:10:54.251 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-30 15:10:54.259 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-30 15:10:54.282 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-30 15:10:54.301 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-30 15:10:58.542 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-30 15:10:58.554 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 62622 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-30 15:10:58.555 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-30 15:11:00.331 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-30 15:11:00.332 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-30 15:11:00.332 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-30 15:11:00.394 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-30 15:11:01.445 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-30 15:11:02.161 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-30 15:11:02.269 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for 192.168.61.10/192.168.61.10:7379
2025-07-30 15:11:02.314 [redisson-netty-4-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for 192.168.61.10/192.168.61.10:7379
2025-07-30 15:11:05.678 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-30 15:11:06.680 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-30 15:11:06.742 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-30 15:11:06.742 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 15:11:06.742 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 15:11:06.871 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 15:11:06.897 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-30 15:11:09.104 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-30 15:11:09.110 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-30 15:11:09.110 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-30 15:11:09.110 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-30 15:11:09.110 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-30 15:11:09.110 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-30 15:11:09.110 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-30 15:11:09.111 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@a353dff
2025-07-30 15:11:10.533 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-30 15:11:10.552 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 15:11:10.633 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 15:11:10.633 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 15:11:10.664 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 15:11:10.665 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 15:11:18.814 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 15:11:18.814 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 15:11:18.829 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 15:11:18.829 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-30 15:11:18.837 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 20.46 seconds (process running for 21.69)
2025-07-30 15:11:18.944 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 98 ms to scan 26 urls, producing 298 keys and 1816 values
2025-07-30 15:11:18.952 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 1 ms to scan 2 urls, producing 14 keys and 37 values
2025-07-30 15:11:19.087 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-30 15:11:54.749 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 15:11:54.753 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 15:11:54.753 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 15:11:54.753 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 15:11:54.753 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 15:11:54.753 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 15:11:54.753 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 15:11:54.753 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 15:11:54.753 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 15:11:54.903 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-30 15:11:54.904 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 15:11:54.904 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-30 15:11:54.912 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-30 15:11:54.934 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-30 15:11:54.953 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-30 15:11:58.240 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-30 15:11:58.251 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 62702 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-30 15:11:58.251 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-30 15:11:59.840 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-30 15:11:59.841 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-30 15:11:59.841 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-30 15:11:59.871 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-30 15:12:00.487 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-30 15:12:01.178 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-30 15:12:01.277 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for 192.168.61.10/192.168.61.10:7379
2025-07-30 15:12:01.317 [redisson-netty-4-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for 192.168.61.10/192.168.61.10:7379
2025-07-30 15:12:04.399 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-30 15:12:05.440 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-30 15:12:05.501 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-30 15:12:05.501 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 15:12:05.501 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 15:12:05.629 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 15:12:05.647 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-30 15:12:07.798 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-30 15:12:07.804 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-30 15:12:07.804 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-30 15:12:07.804 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-30 15:12:07.804 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-30 15:12:07.804 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-30 15:12:07.804 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-30 15:12:07.804 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@671f70bc
2025-07-30 15:12:09.204 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-30 15:12:09.222 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 15:12:09.303 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 15:12:09.303 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 15:12:09.334 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 15:12:09.334 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 15:12:16.117 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 15:12:16.117 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 15:12:16.131 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 15:12:16.131 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-30 15:12:16.140 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 18.051 seconds (process running for 18.676)
2025-07-30 15:12:16.218 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 67 ms to scan 26 urls, producing 298 keys and 1816 values
2025-07-30 15:12:16.227 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 2 ms to scan 2 urls, producing 14 keys and 37 values
2025-07-30 15:12:16.375 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-30 15:12:17.086 [RMI TCP Connection(4)-*************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 15:50:35.039 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 15:50:35.041 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 15:50:35.041 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 15:50:35.041 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 15:50:35.041 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 15:50:35.041 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 15:50:35.041 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-30 15:50:35.041 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-30 15:50:35.041 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 15:50:35.232 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-30 15:50:35.232 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 15:50:35.232 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-30 15:50:35.244 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-30 15:50:35.282 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-30 15:50:35.283 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-30 15:50:39.182 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-30 15:50:39.192 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 64892 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-30 15:50:39.193 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-30 15:50:40.815 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-30 15:50:40.815 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-30 15:50:40.815 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-30 15:50:40.846 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-30 15:50:41.446 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-30 15:50:42.136 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-30 15:50:42.237 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for 192.168.61.10/192.168.61.10:7379
2025-07-30 15:50:42.275 [redisson-netty-4-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for 192.168.61.10/192.168.61.10:7379
2025-07-30 15:50:45.495 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-30 15:50:46.582 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-30 15:50:46.645 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-30 15:50:46.645 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 15:50:46.645 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 15:50:46.754 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-30 15:50:46.777 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-30 15:50:48.967 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-30 15:50:48.973 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-30 15:50:48.973 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-30 15:50:48.973 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-30 15:50:48.973 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-30 15:50:48.973 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-30 15:50:48.973 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-30 15:50:48.973 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@3e0d722f
2025-07-30 15:50:50.375 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-30 15:50:50.393 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 15:50:50.474 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 15:50:50.474 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 15:50:50.506 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 15:50:50.506 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 15:50:56.207 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 15:50:56.207 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-30 15:50:56.218 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-30 15:50:56.219 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-30 15:50:56.226 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 17.206 seconds (process running for 18.386)
2025-07-30 15:50:56.288 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 54 ms to scan 26 urls, producing 298 keys and 1816 values
2025-07-30 15:50:56.296 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 1 ms to scan 2 urls, producing 14 keys and 37 values
2025-07-30 15:50:56.398 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-30 15:50:56.864 [RMI TCP Connection(1)-*************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 16:41:35.156 [http-nio-8080-exec-22] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM project_approval WHERE creator  = ?  order by id asc LIMIT 0,20
2025-07-30 16:41:35.173 [http-nio-8080-exec-22] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: select count(1) as count from project_approval WHERE creator  = ? 
2025-07-30 16:45:33.231 [http-nio-8080-exec-27] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT name,planned_start_at,id FROM project_approval order by id asc
2025-07-30 16:48:27.556 [http-nio-8080-exec-40] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM project_approval WHERE creator  = ?  order by id asc LIMIT 0,20
2025-07-30 16:48:27.561 [http-nio-8080-exec-40] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: select count(1) as count from project_approval WHERE creator  = ? 
2025-07-30 16:51:10.218 [http-nio-8080-exec-50] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM project_approval WHERE creator  = ?  order by id asc LIMIT 0,20
2025-07-30 16:51:10.223 [http-nio-8080-exec-50] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: select count(1) as count from project_approval WHERE creator  = ? 
2025-07-30 16:53:45.189 [http-nio-8080-exec-63] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM project_approval WHERE creator  = ?  order by id asc LIMIT 0,20
2025-07-30 16:53:45.193 [http-nio-8080-exec-63] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: select count(1) as count from project_approval WHERE creator  = ? 
2025-07-30 16:54:28.392 [http-nio-8080-exec-73] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM xedasd order by id asc LIMIT 0,20
2025-07-30 16:54:28.396 [http-nio-8080-exec-73] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-30 16:54:29.873 [http-nio-8080-exec-75] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 16:54:29.886 [http-nio-8080-exec-75] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 16:56:06.853 [http-nio-8080-exec-77] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 16:56:06.871 [http-nio-8080-exec-77] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 16:56:07.417 [http-nio-8080-exec-78] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 16:56:07.427 [http-nio-8080-exec-78] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 16:58:22.937 [http-nio-8080-exec-79] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 16:58:22.954 [http-nio-8080-exec-79] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 16:58:23.785 [http-nio-8080-exec-80] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 16:58:23.801 [http-nio-8080-exec-80] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 16:58:24.675 [http-nio-8080-exec-81] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 16:58:24.684 [http-nio-8080-exec-81] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 16:58:27.061 [http-nio-8080-exec-82] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 16:58:27.070 [http-nio-8080-exec-82] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 16:58:29.108 [http-nio-8080-exec-83] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 16:58:29.118 [http-nio-8080-exec-83] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 16:58:35.678 [http-nio-8080-exec-87] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM project_approval WHERE creator  = ?  order by id asc LIMIT 0,20
2025-07-30 16:58:35.684 [http-nio-8080-exec-87] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: select count(1) as count from project_approval WHERE creator  = ? 
2025-07-30 16:58:57.254 [http-nio-8080-exec-98] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM xedasd order by id asc LIMIT 0,20
2025-07-30 16:58:57.258 [http-nio-8080-exec-98] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-30 16:59:11.335 [http-nio-8080-exec-5] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM xedasd order by id asc LIMIT 0,20
2025-07-30 16:59:11.338 [http-nio-8080-exec-5] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-30 16:59:30.073 [http-nio-8080-exec-10] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM xedasd order by id asc LIMIT 0,20
2025-07-30 16:59:30.077 [http-nio-8080-exec-10] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-30 16:59:37.639 [http-nio-8080-exec-16] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM xedasd order by id asc LIMIT 0,20
2025-07-30 16:59:37.642 [http-nio-8080-exec-16] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-30 16:59:40.667 [http-nio-8080-exec-18] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 16:59:40.680 [http-nio-8080-exec-18] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 16:59:50.478 [http-nio-8080-exec-19] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 16:59:50.501 [http-nio-8080-exec-19] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 16:59:54.028 [http-nio-8080-exec-21] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 16:59:54.038 [http-nio-8080-exec-21] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 16:59:58.076 [http-nio-8080-exec-20] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 16:59:58.086 [http-nio-8080-exec-20] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:00:09.250 [http-nio-8080-exec-31] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM xedasd order by id asc LIMIT 0,20
2025-07-30 17:00:09.253 [http-nio-8080-exec-31] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-30 17:00:35.466 [http-nio-8080-exec-33] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM xedasd order by id asc LIMIT 0,20
2025-07-30 17:00:35.469 [http-nio-8080-exec-33] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-30 17:07:01.981 [http-nio-8080-exec-34] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:07:01.992 [http-nio-8080-exec-34] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:07:16.674 [http-nio-8080-exec-36] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:07:16.684 [http-nio-8080-exec-36] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:07:21.543 [http-nio-8080-exec-35] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:07:21.553 [http-nio-8080-exec-35] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:07:23.526 [http-nio-8080-exec-41] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:07:23.536 [http-nio-8080-exec-41] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:09:03.146 [http-nio-8080-exec-42] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM xedasd order by id asc LIMIT 0,20
2025-07-30 17:09:03.153 [http-nio-8080-exec-42] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-30 17:09:21.327 [http-nio-8080-exec-45] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM xedasd order by id asc LIMIT 0,20
2025-07-30 17:09:21.334 [http-nio-8080-exec-45] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-30 17:09:28.232 [http-nio-8080-exec-55] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM xedasd order by id asc LIMIT 0,20
2025-07-30 17:09:28.236 [http-nio-8080-exec-55] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-30 17:09:32.588 [http-nio-8080-exec-57] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:09:32.602 [http-nio-8080-exec-57] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:09:33.905 [http-nio-8080-exec-56] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:09:33.914 [http-nio-8080-exec-56] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:09:52.898 [http-nio-8080-exec-62] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM xedasd order by id asc LIMIT 0,20
2025-07-30 17:09:52.901 [http-nio-8080-exec-62] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-30 17:09:57.671 [http-nio-8080-exec-71] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM xedasd order by id asc LIMIT 0,20
2025-07-30 17:09:57.675 [http-nio-8080-exec-71] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-30 17:10:01.695 [http-nio-8080-exec-74] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:10:01.705 [http-nio-8080-exec-74] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:10:07.870 [http-nio-8080-exec-75] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:10:07.882 [http-nio-8080-exec-75] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:11:23.054 [http-nio-8080-exec-78] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM xedasd order by id asc LIMIT 0,20
2025-07-30 17:11:23.058 [http-nio-8080-exec-78] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-30 17:12:15.898 [http-nio-8080-exec-90] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM xedasd order by id asc LIMIT 0,20
2025-07-30 17:12:15.901 [http-nio-8080-exec-90] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-30 17:12:59.646 [http-nio-8080-exec-2] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM xedasd order by id asc LIMIT 0,20
2025-07-30 17:12:59.650 [http-nio-8080-exec-2] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-30 17:13:01.405 [http-nio-8080-exec-3] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:13:01.413 [http-nio-8080-exec-3] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:15:41.269 [http-nio-8080-exec-12] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM xedasd order by id asc LIMIT 0,20
2025-07-30 17:15:41.272 [http-nio-8080-exec-12] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-30 17:15:42.543 [http-nio-8080-exec-14] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:15:42.552 [http-nio-8080-exec-14] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:16:27.434 [http-nio-8080-exec-15] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:16:27.460 [http-nio-8080-exec-15] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:16:27.954 [http-nio-8080-exec-16] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:16:27.965 [http-nio-8080-exec-16] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:16:40.492 [http-nio-8080-exec-17] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:16:40.502 [http-nio-8080-exec-17] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:16:40.999 [http-nio-8080-exec-18] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:16:41.008 [http-nio-8080-exec-18] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:16:45.661 [http-nio-8080-exec-19] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:16:45.675 [http-nio-8080-exec-19] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:16:46.164 [http-nio-8080-exec-21] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:16:46.175 [http-nio-8080-exec-21] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:17:04.005 [http-nio-8080-exec-20] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:17:04.019 [http-nio-8080-exec-20] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:17:06.502 [http-nio-8080-exec-24] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:17:06.513 [http-nio-8080-exec-24] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:19:59.053 [http-nio-8080-exec-45] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM xedasd order by id asc LIMIT 0,20
2025-07-30 17:19:59.056 [http-nio-8080-exec-45] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-30 17:20:06.237 [http-nio-8080-exec-47] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:20:06.248 [http-nio-8080-exec-47] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:20:40.563 [http-nio-8080-exec-37] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:20:40.576 [http-nio-8080-exec-37] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:20:41.403 [http-nio-8080-exec-48] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:20:41.414 [http-nio-8080-exec-48] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:20:46.201 [http-nio-8080-exec-50] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:20:46.214 [http-nio-8080-exec-50] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:20:47.008 [http-nio-8080-exec-49] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:20:47.017 [http-nio-8080-exec-49] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:21:01.956 [http-nio-8080-exec-52] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:21:01.967 [http-nio-8080-exec-52] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:21:04.810 [http-nio-8080-exec-51] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:21:04.821 [http-nio-8080-exec-51] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:21:05.262 [http-nio-8080-exec-53] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:21:05.277 [http-nio-8080-exec-53] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:21:20.818 [http-nio-8080-exec-54] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:21:20.836 [http-nio-8080-exec-54] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:21:21.801 [http-nio-8080-exec-55] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:21:21.814 [http-nio-8080-exec-55] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:21:56.298 [http-nio-8080-exec-67] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM xedasd order by id asc LIMIT 0,20
2025-07-30 17:21:56.301 [http-nio-8080-exec-67] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-30 17:21:58.428 [http-nio-8080-exec-65] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:21:58.439 [http-nio-8080-exec-65] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:22:00.058 [http-nio-8080-exec-69] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:22:00.067 [http-nio-8080-exec-69] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:22:05.033 [http-nio-8080-exec-68] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:22:05.045 [http-nio-8080-exec-68] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:22:06.022 [http-nio-8080-exec-70] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:22:06.031 [http-nio-8080-exec-70] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:22:15.736 [http-nio-8080-exec-71] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:22:15.754 [http-nio-8080-exec-71] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:22:19.683 [http-nio-8080-exec-73] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:22:19.695 [http-nio-8080-exec-73] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:22:54.241 [http-nio-8080-exec-93] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM xedasd order by id asc LIMIT 0,20
2025-07-30 17:22:54.370 [http-nio-8080-exec-93] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-30 17:22:57.043 [http-nio-8080-exec-96] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:22:57.114 [http-nio-8080-exec-96] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:29:36.142 [http-nio-8080-exec-1] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM xedasd order by id asc LIMIT 0,20
2025-07-30 17:29:36.145 [http-nio-8080-exec-1] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-30 17:29:38.113 [http-nio-8080-exec-5] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:29:38.123 [http-nio-8080-exec-5] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:30:19.109 [http-nio-8080-exec-14] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM xedasd order by id asc LIMIT 0,20
2025-07-30 17:30:19.113 [http-nio-8080-exec-14] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-30 17:30:21.841 [http-nio-8080-exec-16] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:30:21.850 [http-nio-8080-exec-16] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:32:52.540 [http-nio-8080-exec-30] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM xedasd order by id asc LIMIT 0,20
2025-07-30 17:32:52.544 [http-nio-8080-exec-30] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-30 17:32:54.394 [http-nio-8080-exec-33] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:32:54.406 [http-nio-8080-exec-33] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:33:45.691 [http-nio-8080-exec-40] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM xedasd order by id asc LIMIT 0,20
2025-07-30 17:33:45.696 [http-nio-8080-exec-40] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-30 17:33:47.769 [http-nio-8080-exec-39] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:33:47.780 [http-nio-8080-exec-39] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:37:12.912 [http-nio-8080-exec-54] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM xedasd order by id asc LIMIT 0,20
2025-07-30 17:37:12.919 [http-nio-8080-exec-54] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-30 17:37:14.590 [http-nio-8080-exec-58] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:37:14.601 [http-nio-8080-exec-58] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:37:54.929 [http-nio-8080-exec-57] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:37:54.942 [http-nio-8080-exec-57] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:37:55.643 [http-nio-8080-exec-60] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:37:55.673 [http-nio-8080-exec-60] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:38:01.819 [http-nio-8080-exec-59] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:38:01.829 [http-nio-8080-exec-59] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:38:03.297 [http-nio-8080-exec-56] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 17:38:03.307 [http-nio-8080-exec-56] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-30 18:40:35.466 [lettuce-eventExecutorLoop-1-1] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was /192.168.61.10:7379
2025-07-30 18:40:35.523 [lettuce-nioEventLoop-7-3] INFO  i.l.c.p.ReconnectionHandler - [lambda$null$3,174] - Reconnected to 192.168.61.10/<unresolved>:7379
2025-07-30 19:49:21.211 [lettuce-eventExecutorLoop-1-2] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was /192.168.61.10:7379
2025-07-30 19:49:21.345 [lettuce-nioEventLoop-7-4] INFO  i.l.c.p.ReconnectionHandler - [lambda$null$3,174] - Reconnected to 192.168.61.10/<unresolved>:7379
