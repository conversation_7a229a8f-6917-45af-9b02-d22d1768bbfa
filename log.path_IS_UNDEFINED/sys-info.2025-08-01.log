2025-08-01 09:22:12.906 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-08-01 09:22:13.019 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-08-01 09:22:13.019 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-08-01 09:22:13.020 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-08-01 09:22:13.020 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-08-01 09:22:13.020 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-08-01 09:22:13.020 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-08-01 09:22:13.021 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-08-01 09:22:13.022 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-08-01 09:22:13.443 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-08-01 09:22:13.444 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-08-01 09:22:13.444 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-08-01 09:22:13.481 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-08-01 09:22:13.592 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-08-01 09:22:13.596 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-08-01 09:22:18.466 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-08-01 09:22:18.497 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 66663 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-08-01 09:22:18.498 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-08-01 09:22:21.286 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 09:22:21.287 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-08-01 09:22:21.287 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-08-01 09:22:21.334 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-08-01 09:22:22.141 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-08-01 09:22:23.290 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-08-01 09:22:23.505 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for *************/*************:7379
2025-08-01 09:22:23.561 [redisson-netty-4-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for *************/*************:7379
2025-08-01 09:22:27.680 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-08-01 09:22:28.976 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-08-01 09:22:29.082 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-08-01 09:22:29.082 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-08-01 09:22:29.082 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-08-01 09:22:29.284 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-08-01 09:22:29.304 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-08-01 09:22:32.041 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-08-01 09:22:32.048 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-08-01 09:22:32.048 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-08-01 09:22:32.048 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-08-01 09:22:32.048 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-08-01 09:22:32.048 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-08-01 09:22:32.048 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-08-01 09:22:32.048 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@458c58e3
2025-08-01 09:22:33.699 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 09:22:33.726 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-08-01 09:22:33.834 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-08-01 09:22:33.834 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-08-01 09:22:33.884 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-08-01 09:22:33.885 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-08-01 09:22:41.840 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-08-01 09:22:41.841 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-08-01 09:22:41.859 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-08-01 09:22:41.859 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-08-01 09:22:41.893 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 23.607 seconds (process running for 25.43)
2025-08-01 09:22:42.063 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-08-01 09:22:42.204 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 131 ms to scan 26 urls, producing 298 keys and 1817 values
2025-08-01 09:22:42.222 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 3 ms to scan 2 urls, producing 14 keys and 38 values
2025-08-01 09:24:41.080 [http-nio-8080-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 09:41:16.157 [lettuce-eventExecutorLoop-1-1] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was /*************:7379
2025-08-01 09:41:16.185 [lettuce-nioEventLoop-7-2] INFO  i.l.c.p.ReconnectionHandler - [lambda$null$3,174] - Reconnected to *************/<unresolved>:7379
2025-08-01 09:41:18.271 [schedule-pool-1] INFO  sys-user - [run,57] - [127.0.0.1]内网IP[admin][Success][登录成功]
2025-08-01 11:43:07.661 [lettuce-eventExecutorLoop-1-2] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was /*************:7379
2025-08-01 11:43:07.726 [lettuce-nioEventLoop-7-3] INFO  i.l.c.p.ReconnectionHandler - [lambda$null$3,174] - Reconnected to *************/<unresolved>:7379
2025-08-01 14:16:17.760 [lettuce-eventExecutorLoop-1-3] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was /*************:7379
2025-08-01 14:16:17.815 [lettuce-nioEventLoop-7-4] INFO  i.l.c.p.ReconnectionHandler - [lambda$null$3,174] - Reconnected to *************/<unresolved>:7379
2025-08-01 14:16:20.976 [schedule-pool-1] INFO  sys-user - [run,57] - [127.0.0.1]内网IP[admin][Success][登录成功]
2025-08-01 14:46:25.330 [lettuce-eventExecutorLoop-1-4] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was /*************:7379
2025-08-01 14:46:25.359 [lettuce-nioEventLoop-7-5] INFO  i.l.c.p.ReconnectionHandler - [lambda$null$3,174] - Reconnected to *************/<unresolved>:7379
