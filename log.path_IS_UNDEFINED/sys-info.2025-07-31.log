2025-07-31 09:13:03.713 [lettuce-eventExecutorLoop-1-3] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was /*************:7379
2025-07-31 09:13:03.863 [lettuce-nioEventLoop-7-5] INFO  i.l.c.p.ReconnectionHandler - [lambda$null$3,174] - Reconnected to *************/<unresolved>:7379
2025-07-31 09:13:09.086 [schedule-pool-3] INFO  sys-user - [run,57] - [127.0.0.1]内网IP[admin][Success][登录成功]
2025-07-31 09:50:07.311 [schedule-pool-3] INFO  sys-user - [run,57] - [*************]内网IP[admin][Success][登录成功]
2025-07-31 09:50:08.119 [http-nio-8080-exec-52] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countWaitingTasks(..)] countWaitingTasks 执行耗时: 6 ms
2025-07-31 09:50:08.135 [http-nio-8080-exec-52] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countFinishedTasks(..)] countFinishedTasks 执行耗时: 16 ms
2025-07-31 09:50:08.143 [http-nio-8080-exec-52] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countMyApplyTasks(..)] countMyApplyTasks 执行耗时: 8 ms
2025-07-31 09:50:08.147 [http-nio-8080-exec-52] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countCcTasks(..)] countCcTasks 执行耗时: 4 ms
2025-07-31 09:50:08.147 [http-nio-8080-exec-52] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [PmsOrderServiceImpl.getApprovalStat(..)] getApprovalStat 执行耗时: 37 ms
2025-07-31 10:24:20.742 [http-nio-8080-exec-88] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM project_change_approval WHERE creator  = ?  order by id asc LIMIT 0,20
2025-07-31 10:24:20.749 [http-nio-8080-exec-88] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: select count(1) as count from project_change_approval WHERE creator  = ? 
2025-07-31 10:24:21.820 [http-nio-8080-exec-96] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM Trademark_inventory order by id asc LIMIT 0,20
2025-07-31 10:24:21.825 [http-nio-8080-exec-96] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: select count(1) as count from Trademark_inventory
2025-07-31 10:24:21.825 [http-nio-8080-exec-97] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM soft_inventory order by id asc LIMIT 0,20
2025-07-31 10:24:21.829 [http-nio-8080-exec-97] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: select count(1) as count from soft_inventory
2025-07-31 10:24:21.845 [http-nio-8080-exec-100] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM patent_inventory order by id asc LIMIT 0,20
2025-07-31 10:24:21.853 [http-nio-8080-exec-100] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: select count(1) as count from patent_inventory
2025-07-31 10:24:28.033 [http-nio-8080-exec-13] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM project_change_approval WHERE creator  = ?  order by id asc LIMIT 0,20
2025-07-31 10:24:28.040 [http-nio-8080-exec-13] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: select count(1) as count from project_change_approval WHERE creator  = ? 
2025-07-31 10:24:41.219 [http-nio-8080-exec-25] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: SELECT * FROM project_approval WHERE creator  = ?  order by id asc LIMIT 0,20
2025-07-31 10:24:41.224 [http-nio-8080-exec-25] INFO  c.r.lowcode.orm.ORM - [printSql,281] - [ORM] 执行sql: select count(1) as count from project_approval WHERE creator  = ? 
2025-07-31 10:27:27.170 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-31 10:27:27.171 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-31 10:27:27.171 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-31 10:27:27.171 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-31 10:27:27.171 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-31 10:27:27.171 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-31 10:27:27.171 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-31 10:27:27.171 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-31 10:27:27.171 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 10:27:27.333 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-31 10:27:27.333 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 10:27:27.333 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-31 10:27:27.341 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-31 10:27:27.365 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-31 10:27:27.378 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-31 10:27:31.286 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-31 10:27:31.299 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 98905 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-31 10:27:31.299 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-31 10:27:32.948 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-31 10:27:32.949 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-31 10:27:32.949 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-31 10:27:32.984 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-31 10:27:33.608 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-31 10:27:34.287 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-31 10:27:34.408 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for *************/*************:7379
2025-07-31 10:27:34.450 [redisson-netty-4-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for *************/*************:7379
2025-07-31 10:27:37.689 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-31 10:27:38.742 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-31 10:27:38.802 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-31 10:27:38.803 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-31 10:27:38.803 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-31 10:27:38.934 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-31 10:27:38.954 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-31 10:27:41.166 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-31 10:27:41.172 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 10:27:41.172 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-31 10:27:41.172 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-31 10:27:41.173 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 10:27:41.173 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-31 10:27:41.173 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-31 10:27:41.173 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@764b7ff7
2025-07-31 10:27:42.628 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-31 10:27:42.640 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-31 10:27:42.736 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-31 10:27:42.737 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-31 10:27:42.778 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-31 10:27:42.778 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-31 10:27:48.856 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 10:27:48.860 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-31 10:27:48.860 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-31 10:27:48.873 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-31 10:27:48.874 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-31 10:27:48.881 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 17.732 seconds (process running for 18.915)
2025-07-31 10:27:48.959 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 69 ms to scan 26 urls, producing 298 keys and 1816 values
2025-07-31 10:27:48.975 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 8 ms to scan 2 urls, producing 14 keys and 37 values
2025-07-31 10:27:49.114 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-31 10:35:24.736 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-31 10:35:24.737 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-31 10:35:24.737 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-31 10:35:24.737 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-31 10:35:24.737 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-31 10:35:24.737 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-31 10:35:24.737 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-31 10:35:24.737 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-31 10:35:24.737 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 10:35:24.884 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-31 10:35:24.884 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 10:35:24.884 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-31 10:35:24.891 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-31 10:35:24.915 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-31 10:35:24.930 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-31 10:35:27.594 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-31 10:35:27.606 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 99416 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-31 10:35:27.607 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-31 10:35:29.149 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-31 10:35:29.149 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-31 10:35:29.149 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-31 10:35:29.179 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-31 10:35:29.775 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-31 10:35:30.453 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-31 10:35:30.571 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for *************/*************:7379
2025-07-31 10:35:30.611 [redisson-netty-4-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for *************/*************:7379
2025-07-31 10:35:33.656 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-31 10:35:34.627 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-31 10:35:34.686 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-31 10:35:34.686 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-31 10:35:34.686 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-31 10:35:34.813 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-31 10:35:34.830 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-31 10:35:36.974 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-31 10:35:36.979 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 10:35:36.979 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-31 10:35:36.979 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-31 10:35:36.980 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 10:35:36.980 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-31 10:35:36.980 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-31 10:35:36.980 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@35ee7dc8
2025-07-31 10:35:38.374 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-31 10:35:38.395 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-31 10:35:38.490 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-31 10:35:38.491 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-31 10:35:38.525 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-31 10:35:38.525 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-31 10:35:44.209 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 10:35:47.719 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-31 10:35:47.719 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-31 10:35:47.734 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-31 10:35:47.734 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-31 10:35:47.741 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 20.286 seconds (process running for 20.92)
2025-07-31 10:35:47.832 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 84 ms to scan 26 urls, producing 298 keys and 1816 values
2025-07-31 10:35:47.840 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 1 ms to scan 2 urls, producing 14 keys and 37 values
2025-07-31 10:35:47.885 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-31 10:36:47.417 [http-nio-8080-exec-5] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: INSERT INTO table_eqpt_work_time_approval (`owner`,`owner_dept`,`creator`,`business_time`,`create_time`,`form_id`,`updater`,`update_time`,`flow_status`,`total_work_duration`,`project_id`,`table_eqpt_work_time_approve_line`,`creator_dept`,`id`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)
2025-07-31 10:37:43.387 [http-nio-8080-exec-14] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: INSERT INTO table_eqpt_work_time_approval (`owner`,`owner_dept`,`creator`,`business_time`,`create_time`,`form_id`,`updater`,`update_time`,`flow_status`,`total_work_duration`,`project_id`,`table_eqpt_work_time_approve_line`,`creator_dept`,`id`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)
2025-07-31 10:42:50.914 [http-nio-8080-exec-45] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: INSERT INTO table_eqpt_work_time_approval (`owner`,`owner_dept`,`creator`,`business_time`,`create_time`,`form_id`,`updater`,`update_time`,`flow_status`,`total_work_duration`,`project_id`,`table_eqpt_work_time_approve_line`,`creator_dept`,`id`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)
2025-07-31 10:45:59.653 [http-nio-8080-exec-79] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: INSERT INTO table_eqpt_work_time_approval (`owner`,`owner_dept`,`creator`,`business_time`,`create_time`,`form_id`,`updater`,`update_time`,`flow_status`,`total_work_duration`,`project_id`,`table_eqpt_work_time_approve_line`,`creator_dept`,`id`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)
2025-07-31 10:46:45.315 [http-nio-8080-exec-13] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countWaitingTasks(..)] countWaitingTasks 执行耗时: 5 ms
2025-07-31 10:46:45.329 [http-nio-8080-exec-13] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countFinishedTasks(..)] countFinishedTasks 执行耗时: 14 ms
2025-07-31 10:46:45.337 [http-nio-8080-exec-13] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countMyApplyTasks(..)] countMyApplyTasks 执行耗时: 8 ms
2025-07-31 10:46:45.341 [http-nio-8080-exec-13] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countCcTasks(..)] countCcTasks 执行耗时: 4 ms
2025-07-31 10:46:45.341 [http-nio-8080-exec-13] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [PmsOrderServiceImpl.getApprovalStat(..)] getApprovalStat 执行耗时: 31 ms
2025-07-31 10:50:22.137 [http-nio-8080-exec-8] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: INSERT INTO table_eqpt_work_time_approval (`owner`,`owner_dept`,`creator`,`business_time`,`create_time`,`form_id`,`updater`,`update_time`,`flow_status`,`total_work_duration`,`project_id`,`table_eqpt_work_time_approve_line`,`creator_dept`,`id`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)
2025-07-31 10:53:46.318 [http-nio-8080-exec-18] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: INSERT INTO table_eqpt_work_time_approval (`owner`,`owner_dept`,`creator`,`business_time`,`create_time`,`form_id`,`updater`,`update_time`,`flow_status`,`total_work_duration`,`project_id`,`table_eqpt_work_time_approve_line`,`creator_dept`,`id`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)
2025-07-31 10:55:12.289 [http-nio-8080-exec-24] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: INSERT INTO table_eqpt_work_time_approval (`owner`,`owner_dept`,`creator`,`business_time`,`create_time`,`form_id`,`updater`,`update_time`,`flow_status`,`total_work_duration`,`project_id`,`creator_dept`,`id`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?)
2025-07-31 10:55:12.316 [http-nio-8080-exec-24] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 10:55:12.323 [http-nio-8080-exec-24] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: INSERT INTO table_eqpt_work_time_approval_line (`owner`,`work_duration`,`owner_dept`,`creator`,`create_time`,`form_id`,`remark`,`updater`,`update_time`,`project_id`,`creator_dept`,`work_time`,`regular_work_duration`,`id`,`table_eqpt_work_time_approval_id`,`equipment_id`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
2025-07-31 10:55:20.122 [http-nio-8080-exec-25] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: INSERT INTO table_eqpt_work_time_approval (`owner`,`owner_dept`,`creator`,`business_time`,`create_time`,`form_id`,`updater`,`update_time`,`flow_status`,`total_work_duration`,`project_id`,`creator_dept`,`id`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?)
2025-07-31 10:55:20.131 [http-nio-8080-exec-25] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 10:55:20.135 [http-nio-8080-exec-25] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: INSERT INTO table_eqpt_work_time_approval_line (`owner`,`work_duration`,`owner_dept`,`creator`,`create_time`,`form_id`,`remark`,`updater`,`update_time`,`project_id`,`creator_dept`,`work_time`,`regular_work_duration`,`id`,`table_eqpt_work_time_approval_id`,`equipment_id`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
2025-07-31 11:01:40.538 [http-nio-8080-exec-75] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: INSERT INTO table_eqpt_work_time_approval (`owner`,`owner_dept`,`creator`,`business_time`,`create_time`,`form_id`,`updater`,`update_time`,`flow_status`,`total_work_duration`,`project_id`,`creator_dept`,`id`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?)
2025-07-31 11:01:40.547 [http-nio-8080-exec-75] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 11:01:40.551 [http-nio-8080-exec-75] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: INSERT INTO table_eqpt_work_time_approval_line (`owner`,`work_duration`,`owner_dept`,`creator`,`create_time`,`form_id`,`remark`,`updater`,`update_time`,`project_id`,`creator_dept`,`work_time`,`regular_work_duration`,`id`,`table_eqpt_work_time_approval_id`,`equipment_id`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
2025-07-31 11:01:40.578 [http-nio-8080-exec-75] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 11:01:40.584 [http-nio-8080-exec-75] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 11:02:03.999 [http-nio-8080-exec-95] INFO  o.a.e.i.c.DeployCmd - [executeDeploy,103] - Launching new deployment with version: 1
2025-07-31 11:02:04.126 [http-nio-8080-exec-95] INFO  o.a.e.i.b.d.BpmnDeployer - [dispatchProcessDefinitionEntityInitializedEvent,240] - Process deployed: {id: Process_1:116:bc6bc754-6dba-11f0-b50e-1610f766194b, key: Process_1, name: 1 }
2025-07-31 11:02:10.542 [http-nio-8080-exec-96] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: INSERT INTO table_eqpt_work_time_approval (`owner`,`owner_dept`,`creator`,`business_time`,`create_time`,`form_id`,`updater`,`update_time`,`flow_status`,`total_work_duration`,`project_id`,`creator_dept`,`id`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?)
2025-07-31 11:02:10.549 [http-nio-8080-exec-96] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 11:02:10.553 [http-nio-8080-exec-96] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: INSERT INTO table_eqpt_work_time_approval_line (`owner`,`work_duration`,`owner_dept`,`creator`,`create_time`,`form_id`,`remark`,`updater`,`update_time`,`project_id`,`creator_dept`,`work_time`,`regular_work_duration`,`id`,`table_eqpt_work_time_approval_id`,`equipment_id`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
2025-07-31 11:02:10.572 [http-nio-8080-exec-96] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 11:02:10.576 [http-nio-8080-exec-96] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 11:02:10.869 [http-nio-8080-exec-96] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 11:02:10.877 [http-nio-8080-exec-96] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 11:02:10.886 [http-nio-8080-exec-96] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 11:02:29.231 [http-nio-8080-exec-17] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 11:02:29.325 [http-nio-8080-exec-17] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 11:08:07.028 [http-nio-8080-exec-22] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM xedasd order by id asc LIMIT 0,20
2025-07-31 11:08:07.035 [http-nio-8080-exec-22] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-31 11:08:08.788 [http-nio-8080-exec-24] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-31 11:08:08.802 [http-nio-8080-exec-24] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-31 11:08:17.490 [http-nio-8080-exec-25] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-31 11:08:17.500 [http-nio-8080-exec-25] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-31 11:10:24.685 [http-nio-8080-exec-39] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countWaitingTasks(..)] countWaitingTasks 执行耗时: 18 ms
2025-07-31 11:10:24.699 [http-nio-8080-exec-39] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countFinishedTasks(..)] countFinishedTasks 执行耗时: 13 ms
2025-07-31 11:10:24.709 [http-nio-8080-exec-39] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countMyApplyTasks(..)] countMyApplyTasks 执行耗时: 10 ms
2025-07-31 11:10:24.713 [http-nio-8080-exec-39] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countCcTasks(..)] countCcTasks 执行耗时: 4 ms
2025-07-31 11:10:24.713 [http-nio-8080-exec-39] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [PmsOrderServiceImpl.getApprovalStat(..)] getApprovalStat 执行耗时: 46 ms
2025-07-31 11:12:48.048 [http-nio-8080-exec-53] INFO  o.a.e.i.c.DeployCmd - [executeDeploy,103] - Launching new deployment with version: 1
2025-07-31 11:12:48.081 [http-nio-8080-exec-53] INFO  o.a.e.i.b.d.BpmnDeployer - [dispatchProcessDefinitionEntityInitializedEvent,240] - Process deployed: {id: Process_1:117:3c40e325-6dbc-11f0-b50e-1610f766194b, key: Process_1, name: 1 }
2025-07-31 11:13:42.007 [http-nio-8080-exec-71] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: INSERT INTO table_eqpt_work_time_approval (`owner`,`owner_dept`,`creator`,`business_time`,`create_time`,`form_id`,`updater`,`update_time`,`flow_status`,`total_work_duration`,`project_id`,`creator_dept`,`id`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?)
2025-07-31 11:13:42.014 [http-nio-8080-exec-71] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 11:13:42.018 [http-nio-8080-exec-71] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: INSERT INTO table_eqpt_work_time_approval_line (`owner`,`work_duration`,`owner_dept`,`creator`,`create_time`,`form_id`,`remark`,`updater`,`update_time`,`project_id`,`creator_dept`,`work_time`,`regular_work_duration`,`id`,`table_eqpt_work_time_approval_id`,`equipment_id`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
2025-07-31 11:13:42.035 [http-nio-8080-exec-71] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 11:13:42.039 [http-nio-8080-exec-71] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 11:13:42.122 [http-nio-8080-exec-71] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 11:13:48.300 [http-nio-8080-exec-78] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 11:13:48.379 [http-nio-8080-exec-78] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 11:13:48.515 [http-nio-8080-exec-80] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 11:13:48.599 [http-nio-8080-exec-80] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 11:14:57.899 [http-nio-8080-exec-91] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 11:14:58.018 [http-nio-8080-exec-91] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 11:14:58.233 [http-nio-8080-exec-95] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM work_time_approve WHERE id  = ?  order by id asc
2025-07-31 11:14:58.327 [http-nio-8080-exec-95] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM work_time_approve_line WHERE work_time_approve_id  = ?  order by id asc
2025-07-31 11:16:25.317 [http-nio-8080-exec-5] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM work_time_approve WHERE id  = ?  order by id asc
2025-07-31 11:16:25.512 [http-nio-8080-exec-5] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM work_time_approve_line WHERE work_time_approve_id  = ?  order by id asc
2025-07-31 11:16:25.620 [http-nio-8080-exec-3] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 11:16:25.739 [http-nio-8080-exec-3] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 11:16:28.175 [http-nio-8080-exec-11] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 11:16:28.256 [http-nio-8080-exec-11] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 11:16:28.452 [http-nio-8080-exec-16] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 11:16:28.545 [http-nio-8080-exec-16] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 11:16:31.139 [http-nio-8080-exec-8] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 11:16:31.258 [http-nio-8080-exec-8] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 11:16:31.396 [http-nio-8080-exec-20] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 11:16:31.521 [http-nio-8080-exec-20] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 11:16:33.660 [http-nio-8080-exec-23] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 11:16:33.664 [http-nio-8080-exec-23] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 11:16:33.848 [http-nio-8080-exec-23] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 11:16:33.851 [http-nio-8080-exec-23] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 11:17:09.989 [http-nio-8080-exec-47] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 11:17:10.131 [http-nio-8080-exec-47] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 14:28:38.551 [http-nio-8080-exec-52] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countWaitingTasks(..)] countWaitingTasks 执行耗时: 37 ms
2025-07-31 14:28:38.570 [http-nio-8080-exec-52] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countFinishedTasks(..)] countFinishedTasks 执行耗时: 17 ms
2025-07-31 14:28:38.585 [http-nio-8080-exec-52] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countMyApplyTasks(..)] countMyApplyTasks 执行耗时: 15 ms
2025-07-31 14:28:38.606 [http-nio-8080-exec-52] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countCcTasks(..)] countCcTasks 执行耗时: 21 ms
2025-07-31 14:28:38.607 [http-nio-8080-exec-52] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [PmsOrderServiceImpl.getApprovalStat(..)] getApprovalStat 执行耗时: 93 ms
2025-07-31 14:29:49.608 [http-nio-8080-exec-50] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countWaitingTasks(..)] countWaitingTasks 执行耗时: 22 ms
2025-07-31 14:29:49.628 [http-nio-8080-exec-50] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countFinishedTasks(..)] countFinishedTasks 执行耗时: 19 ms
2025-07-31 14:29:49.640 [http-nio-8080-exec-50] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countMyApplyTasks(..)] countMyApplyTasks 执行耗时: 12 ms
2025-07-31 14:29:49.649 [http-nio-8080-exec-50] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countCcTasks(..)] countCcTasks 执行耗时: 9 ms
2025-07-31 14:29:49.649 [http-nio-8080-exec-50] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [PmsOrderServiceImpl.getApprovalStat(..)] getApprovalStat 执行耗时: 63 ms
2025-07-31 14:31:22.637 [http-nio-8080-exec-58] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countWaitingTasks(..)] countWaitingTasks 执行耗时: 24 ms
2025-07-31 14:31:22.651 [http-nio-8080-exec-58] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countFinishedTasks(..)] countFinishedTasks 执行耗时: 13 ms
2025-07-31 14:31:22.660 [http-nio-8080-exec-58] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countMyApplyTasks(..)] countMyApplyTasks 执行耗时: 9 ms
2025-07-31 14:31:22.665 [http-nio-8080-exec-58] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countCcTasks(..)] countCcTasks 执行耗时: 4 ms
2025-07-31 14:31:22.665 [http-nio-8080-exec-58] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [PmsOrderServiceImpl.getApprovalStat(..)] getApprovalStat 执行耗时: 54 ms
2025-07-31 14:35:59.854 [http-nio-8080-exec-67] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countWaitingTasks(..)] countWaitingTasks 执行耗时: 5 ms
2025-07-31 14:35:59.869 [http-nio-8080-exec-67] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countFinishedTasks(..)] countFinishedTasks 执行耗时: 14 ms
2025-07-31 14:35:59.883 [http-nio-8080-exec-67] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countMyApplyTasks(..)] countMyApplyTasks 执行耗时: 14 ms
2025-07-31 14:35:59.889 [http-nio-8080-exec-67] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countCcTasks(..)] countCcTasks 执行耗时: 6 ms
2025-07-31 14:35:59.889 [http-nio-8080-exec-67] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [PmsOrderServiceImpl.getApprovalStat(..)] getApprovalStat 执行耗时: 40 ms
2025-07-31 14:36:22.814 [http-nio-8080-exec-83] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countWaitingTasks(..)] countWaitingTasks 执行耗时: 10 ms
2025-07-31 14:36:22.821 [http-nio-8080-exec-83] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countFinishedTasks(..)] countFinishedTasks 执行耗时: 7 ms
2025-07-31 14:36:22.825 [http-nio-8080-exec-83] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countMyApplyTasks(..)] countMyApplyTasks 执行耗时: 4 ms
2025-07-31 14:36:22.834 [http-nio-8080-exec-83] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countCcTasks(..)] countCcTasks 执行耗时: 9 ms
2025-07-31 14:36:22.834 [http-nio-8080-exec-83] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [PmsOrderServiceImpl.getApprovalStat(..)] getApprovalStat 执行耗时: 30 ms
2025-07-31 14:36:59.096 [http-nio-8080-exec-96] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countWaitingTasks(..)] countWaitingTasks 执行耗时: 6 ms
2025-07-31 14:36:59.102 [http-nio-8080-exec-96] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countFinishedTasks(..)] countFinishedTasks 执行耗时: 6 ms
2025-07-31 14:36:59.106 [http-nio-8080-exec-96] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countMyApplyTasks(..)] countMyApplyTasks 执行耗时: 4 ms
2025-07-31 14:36:59.114 [http-nio-8080-exec-96] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countCcTasks(..)] countCcTasks 执行耗时: 8 ms
2025-07-31 14:36:59.114 [http-nio-8080-exec-96] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [PmsOrderServiceImpl.getApprovalStat(..)] getApprovalStat 执行耗时: 24 ms
2025-07-31 14:37:56.109 [http-nio-8080-exec-2] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countWaitingTasks(..)] countWaitingTasks 执行耗时: 17 ms
2025-07-31 14:37:56.126 [http-nio-8080-exec-2] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countFinishedTasks(..)] countFinishedTasks 执行耗时: 16 ms
2025-07-31 14:37:56.144 [http-nio-8080-exec-2] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countMyApplyTasks(..)] countMyApplyTasks 执行耗时: 17 ms
2025-07-31 14:37:56.149 [http-nio-8080-exec-2] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countCcTasks(..)] countCcTasks 执行耗时: 4 ms
2025-07-31 14:37:56.149 [http-nio-8080-exec-2] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [PmsOrderServiceImpl.getApprovalStat(..)] getApprovalStat 执行耗时: 57 ms
2025-07-31 14:39:38.392 [http-nio-8080-exec-45] INFO  o.a.e.i.c.DeployCmd - [executeDeploy,103] - Launching new deployment with version: 1
2025-07-31 14:39:38.463 [http-nio-8080-exec-45] INFO  o.a.e.i.b.d.BpmnDeployer - [dispatchProcessDefinitionEntityInitializedEvent,240] - Process deployed: {id: Process_1:118:216b8260-6dd9-11f0-b50e-1610f766194b, key: Process_1, name: 1 }
2025-07-31 14:39:45.119 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-31 14:39:45.120 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-31 14:39:45.120 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-31 14:39:45.120 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-31 14:39:45.120 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-31 14:39:45.120 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-31 14:39:45.120 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-31 14:39:45.120 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-31 14:39:45.120 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 14:39:45.278 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-31 14:39:45.278 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 14:39:45.278 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-31 14:39:45.285 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-31 14:39:45.309 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-31 14:39:45.325 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-31 14:39:49.123 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-31 14:39:49.136 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 13614 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-31 14:39:49.136 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-31 14:39:50.770 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-31 14:39:50.771 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-31 14:39:50.771 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-31 14:39:50.803 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-31 14:39:51.447 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-31 14:39:52.205 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-31 14:39:52.342 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for *************/*************:7379
2025-07-31 14:39:52.397 [redisson-netty-4-17] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for *************/*************:7379
2025-07-31 14:39:55.662 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-31 14:39:56.752 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-31 14:39:56.812 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-31 14:39:56.812 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-31 14:39:56.812 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-31 14:39:56.950 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-31 14:39:56.972 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-31 14:39:59.163 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-31 14:39:59.169 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 14:39:59.169 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-31 14:39:59.169 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-31 14:39:59.170 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 14:39:59.170 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-31 14:39:59.170 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-31 14:39:59.170 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@22e3d222
2025-07-31 14:40:00.634 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-31 14:40:00.646 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-31 14:40:00.738 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-31 14:40:00.738 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-31 14:40:00.770 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-31 14:40:00.770 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-31 14:40:07.042 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-31 14:40:07.043 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-31 14:40:07.057 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-31 14:40:07.057 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-31 14:40:07.066 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 18.1 seconds (process running for 19.299)
2025-07-31 14:40:07.136 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 63 ms to scan 26 urls, producing 298 keys and 1817 values
2025-07-31 14:40:07.144 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 1 ms to scan 2 urls, producing 14 keys and 38 values
2025-07-31 14:40:07.249 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-31 14:40:07.843 [RMI TCP Connection(3)-192.168.61.57] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 14:40:22.446 [http-nio-8080-exec-2] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countWaitingTasks(..)] countWaitingTasks 执行耗时: 20 ms
2025-07-31 14:40:22.457 [http-nio-8080-exec-2] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countFinishedTasks(..)] countFinishedTasks 执行耗时: 11 ms
2025-07-31 14:40:22.466 [http-nio-8080-exec-2] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countMyApplyTasks(..)] countMyApplyTasks 执行耗时: 9 ms
2025-07-31 14:40:22.473 [http-nio-8080-exec-2] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countCcTasks(..)] countCcTasks 执行耗时: 7 ms
2025-07-31 14:40:22.474 [http-nio-8080-exec-2] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [PmsOrderServiceImpl.getApprovalStat(..)] getApprovalStat 执行耗时: 49 ms
2025-07-31 14:41:01.382 [http-nio-8080-exec-33] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: INSERT INTO table_eqpt_work_time_approval (`owner`,`owner_dept`,`creator`,`business_time`,`create_time`,`form_id`,`updater`,`update_time`,`flow_status`,`total_work_duration`,`project_id`,`creator_dept`,`id`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?)
2025-07-31 14:41:01.395 [http-nio-8080-exec-33] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 14:41:01.399 [http-nio-8080-exec-33] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: INSERT INTO table_eqpt_work_time_approval_line (`owner`,`work_duration`,`owner_dept`,`creator`,`create_time`,`form_id`,`remark`,`updater`,`update_time`,`project_id`,`creator_dept`,`work_time`,`regular_work_duration`,`id`,`table_eqpt_work_time_approval_id`,`equipment_id`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
2025-07-31 14:41:01.416 [http-nio-8080-exec-33] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:41:01.419 [http-nio-8080-exec-33] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 14:41:01.588 [http-nio-8080-exec-33] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:41:10.813 [http-nio-8080-exec-40] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:41:10.918 [http-nio-8080-exec-40] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 14:41:16.931 [http-nio-8080-exec-42] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:41:16.945 [http-nio-8080-exec-42] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 14:41:44.057 [http-nio-8080-exec-42] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:41:44.062 [http-nio-8080-exec-42] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:41:54.100 [http-nio-8080-exec-53] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: INSERT INTO table_eqpt_work_time_approval (`owner`,`owner_dept`,`creator`,`business_time`,`create_time`,`form_id`,`updater`,`update_time`,`flow_status`,`total_work_duration`,`project_id`,`creator_dept`,`id`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?)
2025-07-31 14:41:54.108 [http-nio-8080-exec-53] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 14:41:54.111 [http-nio-8080-exec-53] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: INSERT INTO table_eqpt_work_time_approval_line (`owner`,`work_duration`,`owner_dept`,`creator`,`create_time`,`form_id`,`remark`,`updater`,`update_time`,`project_id`,`creator_dept`,`work_time`,`regular_work_duration`,`id`,`table_eqpt_work_time_approval_id`,`equipment_id`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
2025-07-31 14:41:54.128 [http-nio-8080-exec-53] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:41:54.132 [http-nio-8080-exec-53] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 14:41:54.189 [http-nio-8080-exec-53] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:42:01.923 [http-nio-8080-exec-57] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:42:02.062 [http-nio-8080-exec-57] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 14:44:04.995 [http-nio-8080-exec-61] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countWaitingTasks(..)] countWaitingTasks 执行耗时: 25 ms
2025-07-31 14:44:05.008 [http-nio-8080-exec-61] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countFinishedTasks(..)] countFinishedTasks 执行耗时: 12 ms
2025-07-31 14:44:05.017 [http-nio-8080-exec-61] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countMyApplyTasks(..)] countMyApplyTasks 执行耗时: 9 ms
2025-07-31 14:44:05.032 [http-nio-8080-exec-61] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countCcTasks(..)] countCcTasks 执行耗时: 14 ms
2025-07-31 14:44:05.032 [http-nio-8080-exec-61] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [PmsOrderServiceImpl.getApprovalStat(..)] getApprovalStat 执行耗时: 62 ms
2025-07-31 14:45:37.506 [schedule-pool-3] INFO  sys-user - [run,57] - [127.0.0.1]内网IP[15377360130][Success][登录成功]
2025-07-31 14:45:41.079 [http-nio-8080-exec-92] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countWaitingTasks(..)] countWaitingTasks 执行耗时: 17 ms
2025-07-31 14:45:41.086 [http-nio-8080-exec-92] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countFinishedTasks(..)] countFinishedTasks 执行耗时: 7 ms
2025-07-31 14:45:41.093 [http-nio-8080-exec-92] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countMyApplyTasks(..)] countMyApplyTasks 执行耗时: 7 ms
2025-07-31 14:45:41.103 [http-nio-8080-exec-92] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countCcTasks(..)] countCcTasks 执行耗时: 10 ms
2025-07-31 14:45:41.103 [http-nio-8080-exec-92] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [PmsOrderServiceImpl.getApprovalStat(..)] getApprovalStat 执行耗时: 41 ms
2025-07-31 14:45:46.257 [http-nio-8080-exec-3] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:45:46.384 [http-nio-8080-exec-3] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 14:45:49.484 [http-nio-8080-exec-7] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:45:49.488 [http-nio-8080-exec-7] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 14:45:52.710 [http-nio-8080-exec-7] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:45:52.715 [http-nio-8080-exec-7] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:46:24.948 [http-nio-8080-exec-19] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: INSERT INTO table_eqpt_work_time_approval (`owner`,`owner_dept`,`creator`,`business_time`,`create_time`,`form_id`,`updater`,`update_time`,`flow_status`,`total_work_duration`,`project_id`,`creator_dept`,`id`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?)
2025-07-31 14:46:24.959 [http-nio-8080-exec-19] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 14:46:24.963 [http-nio-8080-exec-19] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: INSERT INTO table_eqpt_work_time_approval_line (`owner`,`work_duration`,`owner_dept`,`creator`,`create_time`,`form_id`,`remark`,`updater`,`update_time`,`project_id`,`creator_dept`,`work_time`,`regular_work_duration`,`id`,`table_eqpt_work_time_approval_id`,`equipment_id`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
2025-07-31 14:46:24.978 [http-nio-8080-exec-19] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:46:24.982 [http-nio-8080-exec-19] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 14:46:25.044 [http-nio-8080-exec-19] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:46:25.190 [http-nio-8080-exec-19] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: INSERT INTO table_eqpt_work_time_approval (`owner`,`owner_dept`,`creator`,`business_time`,`create_time`,`form_id`,`updater`,`update_time`,`flow_status`,`total_work_duration`,`project_id`,`creator_dept`,`id`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?)
2025-07-31 14:46:25.197 [http-nio-8080-exec-19] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 14:46:25.200 [http-nio-8080-exec-19] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: INSERT INTO table_eqpt_work_time_approval_line (`owner`,`work_duration`,`owner_dept`,`creator`,`create_time`,`form_id`,`remark`,`updater`,`update_time`,`project_id`,`creator_dept`,`work_time`,`regular_work_duration`,`id`,`table_eqpt_work_time_approval_id`,`equipment_id`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
2025-07-31 14:46:25.218 [http-nio-8080-exec-19] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:46:25.221 [http-nio-8080-exec-19] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 14:46:25.289 [http-nio-8080-exec-19] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:46:25.408 [http-nio-8080-exec-19] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: INSERT INTO table_eqpt_work_time_approval (`owner`,`owner_dept`,`creator`,`business_time`,`create_time`,`form_id`,`updater`,`update_time`,`flow_status`,`total_work_duration`,`project_id`,`creator_dept`,`id`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?)
2025-07-31 14:46:25.415 [http-nio-8080-exec-19] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 14:46:25.418 [http-nio-8080-exec-19] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: INSERT INTO table_eqpt_work_time_approval_line (`owner`,`work_duration`,`owner_dept`,`creator`,`create_time`,`form_id`,`remark`,`updater`,`update_time`,`project_id`,`creator_dept`,`work_time`,`regular_work_duration`,`id`,`table_eqpt_work_time_approval_id`,`equipment_id`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
2025-07-31 14:46:25.433 [http-nio-8080-exec-19] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:46:25.437 [http-nio-8080-exec-19] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 14:46:25.481 [http-nio-8080-exec-19] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:46:41.443 [http-nio-8080-exec-25] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:46:41.511 [http-nio-8080-exec-25] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 14:46:43.865 [http-nio-8080-exec-33] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countWaitingTasks(..)] countWaitingTasks 执行耗时: 6 ms
2025-07-31 14:46:43.878 [http-nio-8080-exec-33] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countFinishedTasks(..)] countFinishedTasks 执行耗时: 13 ms
2025-07-31 14:46:43.885 [http-nio-8080-exec-33] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countMyApplyTasks(..)] countMyApplyTasks 执行耗时: 6 ms
2025-07-31 14:46:43.889 [http-nio-8080-exec-33] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countCcTasks(..)] countCcTasks 执行耗时: 4 ms
2025-07-31 14:46:43.889 [http-nio-8080-exec-33] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [PmsOrderServiceImpl.getApprovalStat(..)] getApprovalStat 执行耗时: 30 ms
2025-07-31 14:46:47.528 [http-nio-8080-exec-37] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:46:47.531 [http-nio-8080-exec-37] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 14:46:47.757 [http-nio-8080-exec-37] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:46:47.764 [http-nio-8080-exec-37] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:46:58.051 [http-nio-8080-exec-51] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:46:58.169 [http-nio-8080-exec-51] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 14:46:59.844 [http-nio-8080-exec-52] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:46:59.849 [http-nio-8080-exec-52] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 14:47:00.110 [http-nio-8080-exec-52] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:47:00.118 [http-nio-8080-exec-52] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:47:09.973 [http-nio-8080-exec-64] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:47:10.063 [http-nio-8080-exec-64] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 14:47:12.010 [http-nio-8080-exec-67] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:47:12.013 [http-nio-8080-exec-67] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 14:47:12.328 [http-nio-8080-exec-67] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:47:12.336 [http-nio-8080-exec-67] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:47:19.432 [http-nio-8080-exec-80] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM project_change_approval WHERE id  = ?  order by id asc
2025-07-31 14:47:35.936 [http-nio-8080-exec-96] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:47:35.997 [http-nio-8080-exec-96] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 14:47:40.724 [http-nio-8080-exec-2] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:47:40.905 [http-nio-8080-exec-2] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 14:47:41.039 [http-nio-8080-exec-7] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:47:41.093 [http-nio-8080-exec-8] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:47:41.179 [http-nio-8080-exec-7] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 14:47:41.216 [http-nio-8080-exec-8] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 14:47:45.275 [http-nio-8080-exec-13] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:47:45.278 [http-nio-8080-exec-11] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:47:45.427 [http-nio-8080-exec-11] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 14:47:45.427 [http-nio-8080-exec-13] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 14:47:45.561 [http-nio-8080-exec-18] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:47:45.598 [http-nio-8080-exec-20] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:47:45.637 [http-nio-8080-exec-22] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:47:45.730 [http-nio-8080-exec-20] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 14:47:45.730 [http-nio-8080-exec-18] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 14:47:45.746 [http-nio-8080-exec-22] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 14:49:09.931 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-31 14:49:09.932 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-31 14:49:09.932 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-31 14:49:09.932 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-31 14:49:09.932 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-31 14:49:09.932 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-31 14:49:09.933 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-31 14:49:09.933 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-31 14:49:09.933 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 14:49:10.095 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-31 14:49:10.095 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 14:49:10.096 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-31 14:49:10.108 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-31 14:49:10.135 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-31 14:49:10.155 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-31 14:49:13.697 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-31 14:49:13.711 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 14160 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-31 14:49:13.712 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-31 14:49:15.530 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-31 14:49:15.531 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-31 14:49:15.531 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-31 14:49:15.572 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-31 14:49:16.398 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-31 14:49:17.155 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-31 14:49:17.260 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for *************/*************:7379
2025-07-31 14:49:17.307 [redisson-netty-4-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for *************/*************:7379
2025-07-31 14:49:20.674 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-31 14:49:21.779 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-31 14:49:21.860 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-31 14:49:21.860 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-31 14:49:21.860 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-31 14:49:21.971 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-31 14:49:21.992 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-31 14:49:24.295 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-31 14:49:24.301 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 14:49:24.301 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-31 14:49:24.301 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-31 14:49:24.301 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 14:49:24.301 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-31 14:49:24.302 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-31 14:49:24.302 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@8f7544a
2025-07-31 14:49:25.741 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-31 14:49:25.752 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-31 14:49:25.864 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-31 14:49:25.864 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-31 14:49:25.913 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-31 14:49:25.913 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-31 14:49:33.074 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-31 14:49:33.076 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-31 14:49:33.093 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-31 14:49:33.093 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-31 14:49:33.116 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 19.588 seconds (process running for 20.872)
2025-07-31 14:49:33.232 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 99 ms to scan 26 urls, producing 298 keys and 1817 values
2025-07-31 14:49:33.241 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 1 ms to scan 2 urls, producing 14 keys and 38 values
2025-07-31 14:49:33.406 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-31 14:50:56.201 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 14:52:17.436 [http-nio-8080-exec-54] INFO  o.a.e.i.c.DeployCmd - [executeDeploy,103] - Launching new deployment with version: 1
2025-07-31 14:52:17.537 [http-nio-8080-exec-54] INFO  o.a.e.i.b.d.BpmnDeployer - [dispatchProcessDefinitionEntityInitializedEvent,240] - Process deployed: {id: Process_1:119:e5dcb44a-6dda-11f0-8206-a61efc1c38ec, key: Process_1, name: 1 }
2025-07-31 14:52:22.379 [http-nio-8080-exec-61] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countWaitingTasks(..)] countWaitingTasks 执行耗时: 16 ms
2025-07-31 14:52:22.392 [http-nio-8080-exec-61] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countFinishedTasks(..)] countFinishedTasks 执行耗时: 13 ms
2025-07-31 14:52:22.400 [http-nio-8080-exec-61] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countMyApplyTasks(..)] countMyApplyTasks 执行耗时: 8 ms
2025-07-31 14:52:22.413 [http-nio-8080-exec-61] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countCcTasks(..)] countCcTasks 执行耗时: 13 ms
2025-07-31 14:52:22.413 [http-nio-8080-exec-61] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [PmsOrderServiceImpl.getApprovalStat(..)] getApprovalStat 执行耗时: 50 ms
2025-07-31 14:52:28.592 [http-nio-8080-exec-67] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: INSERT INTO table_eqpt_work_time_approval (`owner`,`owner_dept`,`creator`,`business_time`,`create_time`,`form_id`,`updater`,`update_time`,`flow_status`,`total_work_duration`,`project_id`,`creator_dept`,`id`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?)
2025-07-31 14:52:28.608 [http-nio-8080-exec-67] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 14:52:28.615 [http-nio-8080-exec-67] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: INSERT INTO table_eqpt_work_time_approval_line (`owner`,`work_duration`,`owner_dept`,`creator`,`create_time`,`form_id`,`remark`,`updater`,`update_time`,`project_id`,`creator_dept`,`work_time`,`regular_work_duration`,`id`,`table_eqpt_work_time_approval_id`,`equipment_id`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
2025-07-31 14:52:28.633 [http-nio-8080-exec-67] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:52:28.637 [http-nio-8080-exec-67] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 14:52:28.845 [http-nio-8080-exec-67] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:52:32.502 [http-nio-8080-exec-73] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM project_change_approval WHERE id  = ?  order by id asc
2025-07-31 14:52:32.780 [http-nio-8080-exec-76] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM milestone_change_apply WHERE id  = ?  order by id asc
2025-07-31 14:52:34.255 [http-nio-8080-exec-80] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM milestone_change_apply WHERE id  = ?  order by id asc
2025-07-31 14:52:34.563 [http-nio-8080-exec-80] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM milestone_change_apply WHERE id  = ?  order by id asc
2025-07-31 14:52:34.571 [http-nio-8080-exec-80] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM milestone_change_apply WHERE id  = ?  order by id asc
2025-07-31 14:52:52.810 [http-nio-8080-exec-90] INFO  o.a.e.i.c.DeployCmd - [executeDeploy,103] - Launching new deployment with version: 1
2025-07-31 14:52:52.854 [http-nio-8080-exec-90] INFO  o.a.e.i.b.d.BpmnDeployer - [dispatchProcessDefinitionEntityInitializedEvent,240] - Process deployed: {id: Process_1:120:fae9cdc6-6dda-11f0-8206-a61efc1c38ec, key: Process_1, name: 1 }
2025-07-31 14:52:57.577 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-31 14:52:57.577 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-31 14:52:57.578 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-31 14:52:57.578 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-31 14:52:57.578 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-31 14:52:57.578 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-31 14:52:57.578 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-31 14:52:57.578 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-31 14:52:57.578 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 14:52:57.719 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-31 14:52:57.719 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 14:52:57.719 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-31 14:52:57.729 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-31 14:52:57.756 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-31 14:52:57.776 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-31 14:52:59.569 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-31 14:52:59.583 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 14358 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-31 14:52:59.583 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-31 14:53:01.242 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-31 14:53:01.243 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-31 14:53:01.243 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-31 14:53:01.276 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-31 14:53:01.853 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-31 14:53:02.592 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-31 14:53:02.689 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for *************/*************:7379
2025-07-31 14:53:02.733 [redisson-netty-4-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for *************/*************:7379
2025-07-31 14:53:05.787 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-31 14:53:06.760 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-31 14:53:06.824 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-31 14:53:06.824 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-31 14:53:06.824 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-31 14:53:06.957 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-31 14:53:06.981 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-31 14:53:09.134 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-31 14:53:09.140 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 14:53:09.140 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-31 14:53:09.140 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-31 14:53:09.140 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 14:53:09.140 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-31 14:53:09.140 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-31 14:53:09.141 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@559ac87f
2025-07-31 14:53:10.560 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-31 14:53:10.571 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-31 14:53:10.658 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-31 14:53:10.658 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-31 14:53:10.695 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-31 14:53:10.696 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-31 14:53:16.636 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-31 14:53:16.637 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-31 14:53:16.651 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-31 14:53:16.651 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-31 14:53:16.659 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 17.256 seconds (process running for 17.983)
2025-07-31 14:53:16.751 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-31 14:53:16.834 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 79 ms to scan 26 urls, producing 298 keys and 1817 values
2025-07-31 14:53:16.850 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 3 ms to scan 2 urls, producing 14 keys and 38 values
2025-07-31 14:53:17.117 [RMI TCP Connection(4)-192.168.61.57] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 14:53:45.071 [http-nio-8080-exec-3] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: INSERT INTO table_eqpt_work_time_approval (`owner`,`owner_dept`,`creator`,`business_time`,`create_time`,`form_id`,`updater`,`update_time`,`flow_status`,`total_work_duration`,`project_id`,`creator_dept`,`id`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?)
2025-07-31 14:53:45.086 [http-nio-8080-exec-3] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 14:53:45.093 [http-nio-8080-exec-3] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: INSERT INTO table_eqpt_work_time_approval_line (`owner`,`work_duration`,`owner_dept`,`creator`,`create_time`,`form_id`,`remark`,`updater`,`update_time`,`project_id`,`creator_dept`,`work_time`,`regular_work_duration`,`id`,`table_eqpt_work_time_approval_id`,`equipment_id`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
2025-07-31 14:53:45.115 [http-nio-8080-exec-3] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:53:45.120 [http-nio-8080-exec-3] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 14:53:45.397 [http-nio-8080-exec-3] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:53:50.427 [http-nio-8080-exec-8] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:53:50.523 [http-nio-8080-exec-8] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 14:53:52.105 [http-nio-8080-exec-11] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:53:52.108 [http-nio-8080-exec-11] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval_line WHERE table_eqpt_work_time_approval_id  = ?  order by id asc
2025-07-31 14:53:57.517 [http-nio-8080-exec-11] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 14:53:57.528 [http-nio-8080-exec-11] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_eqpt_work_time_approval WHERE id  = ?  order by id asc
2025-07-31 15:34:26.460 [http-nio-8080-exec-38] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countWaitingTasks(..)] countWaitingTasks 执行耗时: 9 ms
2025-07-31 15:34:26.484 [http-nio-8080-exec-38] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countFinishedTasks(..)] countFinishedTasks 执行耗时: 24 ms
2025-07-31 15:34:26.493 [http-nio-8080-exec-38] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countMyApplyTasks(..)] countMyApplyTasks 执行耗时: 9 ms
2025-07-31 15:34:26.499 [http-nio-8080-exec-38] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countCcTasks(..)] countCcTasks 执行耗时: 5 ms
2025-07-31 15:34:26.499 [http-nio-8080-exec-38] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [PmsOrderServiceImpl.getApprovalStat(..)] getApprovalStat 执行耗时: 48 ms
2025-07-31 15:42:01.316 [http-nio-8080-exec-10] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countWaitingTasks(..)] countWaitingTasks 执行耗时: 30 ms
2025-07-31 15:42:01.340 [http-nio-8080-exec-10] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countFinishedTasks(..)] countFinishedTasks 执行耗时: 23 ms
2025-07-31 15:42:01.353 [http-nio-8080-exec-10] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countMyApplyTasks(..)] countMyApplyTasks 执行耗时: 12 ms
2025-07-31 15:42:01.358 [http-nio-8080-exec-10] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countCcTasks(..)] countCcTasks 执行耗时: 4 ms
2025-07-31 15:42:01.359 [http-nio-8080-exec-10] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [PmsOrderServiceImpl.getApprovalStat(..)] getApprovalStat 执行耗时: 75 ms
2025-07-31 15:43:41.468 [http-nio-8080-exec-16] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countWaitingTasks(..)] countWaitingTasks 执行耗时: 24 ms
2025-07-31 15:43:41.482 [http-nio-8080-exec-16] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countFinishedTasks(..)] countFinishedTasks 执行耗时: 13 ms
2025-07-31 15:43:41.493 [http-nio-8080-exec-16] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countMyApplyTasks(..)] countMyApplyTasks 执行耗时: 11 ms
2025-07-31 15:43:41.500 [http-nio-8080-exec-16] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countCcTasks(..)] countCcTasks 执行耗时: 7 ms
2025-07-31 15:43:41.501 [http-nio-8080-exec-16] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [PmsOrderServiceImpl.getApprovalStat(..)] getApprovalStat 执行耗时: 57 ms
2025-07-31 15:44:01.601 [http-nio-8080-exec-25] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countWaitingTasks(..)] countWaitingTasks 执行耗时: 12 ms
2025-07-31 15:44:01.611 [http-nio-8080-exec-25] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countFinishedTasks(..)] countFinishedTasks 执行耗时: 9 ms
2025-07-31 15:44:01.616 [http-nio-8080-exec-25] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countMyApplyTasks(..)] countMyApplyTasks 执行耗时: 5 ms
2025-07-31 15:44:01.621 [http-nio-8080-exec-25] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countCcTasks(..)] countCcTasks 执行耗时: 4 ms
2025-07-31 15:44:01.621 [http-nio-8080-exec-25] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [PmsOrderServiceImpl.getApprovalStat(..)] getApprovalStat 执行耗时: 32 ms
2025-07-31 15:47:14.844 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-31 15:47:14.845 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-31 15:47:14.846 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-31 15:47:14.846 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-31 15:47:14.846 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-31 15:47:14.846 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-31 15:47:14.846 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-31 15:47:14.846 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-31 15:47:14.846 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 15:47:15.119 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-31 15:47:15.119 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 15:47:15.120 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-31 15:47:15.143 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-31 15:47:15.201 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-31 15:47:15.226 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-31 15:47:19.129 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-31 15:47:19.139 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 18940 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-31 15:47:19.140 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-31 15:47:20.873 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-31 15:47:20.874 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-31 15:47:20.874 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-31 15:47:20.904 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-31 15:47:21.520 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-31 15:47:22.199 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-31 15:47:22.318 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for *************/*************:7379
2025-07-31 15:47:22.357 [redisson-netty-4-17] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for *************/*************:7379
2025-07-31 15:47:25.459 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-31 15:47:26.463 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-31 15:47:26.522 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-31 15:47:26.522 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-31 15:47:26.522 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-31 15:47:26.648 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-31 15:47:26.676 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-31 15:47:28.860 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-31 15:47:28.866 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 15:47:28.866 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-31 15:47:28.866 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-31 15:47:28.867 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 15:47:28.867 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-31 15:47:28.867 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-31 15:47:28.867 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@4c2370b5
2025-07-31 15:47:30.282 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-31 15:47:30.305 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-31 15:47:30.410 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-31 15:47:30.410 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-31 15:47:30.447 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-31 15:47:30.447 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-31 15:47:37.377 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-31 15:47:37.377 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-31 15:47:37.391 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-31 15:47:37.392 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-31 15:47:37.400 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 18.418 seconds (process running for 19.682)
2025-07-31 15:47:37.469 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 60 ms to scan 26 urls, producing 298 keys and 1817 values
2025-07-31 15:47:37.476 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 1 ms to scan 2 urls, producing 14 keys and 38 values
2025-07-31 15:47:37.565 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-31 15:47:50.201 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 15:49:00.144 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-31 15:49:00.148 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-31 15:49:00.148 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-31 15:49:00.148 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-31 15:49:00.149 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-31 15:49:00.150 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-31 15:49:00.150 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-31 15:49:00.150 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-31 15:49:00.150 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 15:49:00.324 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-31 15:49:00.324 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 15:49:00.324 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-31 15:49:00.337 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-31 15:49:00.372 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-31 15:49:00.395 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-31 15:49:03.667 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-31 15:49:03.680 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 19155 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-31 15:49:03.680 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-31 15:49:05.290 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-31 15:49:05.290 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-31 15:49:05.290 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-31 15:49:05.322 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-31 15:49:05.937 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-31 15:49:06.632 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-31 15:49:06.735 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for *************/*************:7379
2025-07-31 15:49:06.778 [redisson-netty-4-18] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for *************/*************:7379
2025-07-31 15:49:10.031 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-31 15:49:12.050 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-31 15:49:12.194 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-31 15:49:12.194 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-31 15:49:12.195 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-31 15:49:12.394 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-31 15:49:12.432 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-31 15:49:15.677 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-31 15:49:15.685 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 15:49:15.685 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-31 15:49:15.686 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-31 15:49:15.686 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 15:49:15.686 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-31 15:49:15.687 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-31 15:49:15.687 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@6910e725
2025-07-31 15:49:17.179 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-31 15:49:17.200 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-31 15:49:17.305 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-31 15:49:17.305 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-31 15:49:17.338 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-31 15:49:17.338 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-31 15:49:22.597 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 15:49:23.702 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-31 15:49:23.702 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-31 15:49:23.721 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-31 15:49:23.722 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-31 15:49:23.730 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 20.201 seconds (process running for 21.465)
2025-07-31 15:49:23.760 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-31 15:49:23.908 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 140 ms to scan 26 urls, producing 298 keys and 1817 values
2025-07-31 15:49:23.933 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 2 ms to scan 2 urls, producing 14 keys and 38 values
2025-07-31 16:13:30.280 [http-nio-8080-exec-21] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countWaitingTasks(..)] countWaitingTasks 执行耗时: 6 ms
2025-07-31 16:13:30.299 [http-nio-8080-exec-21] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countFinishedTasks(..)] countFinishedTasks 执行耗时: 18 ms
2025-07-31 16:13:30.311 [http-nio-8080-exec-21] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countMyApplyTasks(..)] countMyApplyTasks 执行耗时: 10 ms
2025-07-31 16:13:30.315 [http-nio-8080-exec-21] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countCcTasks(..)] countCcTasks 执行耗时: 4 ms
2025-07-31 16:13:30.315 [http-nio-8080-exec-21] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [PmsOrderServiceImpl.getApprovalStat(..)] getApprovalStat 执行耗时: 46 ms
2025-07-31 16:16:31.854 [http-nio-8080-exec-30] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countWaitingTasks(..)] countWaitingTasks 执行耗时: 9 ms
2025-07-31 16:16:31.861 [http-nio-8080-exec-30] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countFinishedTasks(..)] countFinishedTasks 执行耗时: 7 ms
2025-07-31 16:16:31.866 [http-nio-8080-exec-30] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countMyApplyTasks(..)] countMyApplyTasks 执行耗时: 5 ms
2025-07-31 16:16:31.871 [http-nio-8080-exec-30] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countCcTasks(..)] countCcTasks 执行耗时: 4 ms
2025-07-31 16:16:31.871 [http-nio-8080-exec-30] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [PmsOrderServiceImpl.getApprovalStat(..)] getApprovalStat 执行耗时: 26 ms
2025-07-31 16:18:19.744 [http-nio-8080-exec-39] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countWaitingTasks(..)] countWaitingTasks 执行耗时: 9 ms
2025-07-31 16:18:19.758 [http-nio-8080-exec-39] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countFinishedTasks(..)] countFinishedTasks 执行耗时: 13 ms
2025-07-31 16:18:19.767 [http-nio-8080-exec-39] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countMyApplyTasks(..)] countMyApplyTasks 执行耗时: 9 ms
2025-07-31 16:18:19.772 [http-nio-8080-exec-39] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countCcTasks(..)] countCcTasks 执行耗时: 4 ms
2025-07-31 16:18:19.773 [http-nio-8080-exec-39] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [PmsOrderServiceImpl.getApprovalStat(..)] getApprovalStat 执行耗时: 39 ms
2025-07-31 16:21:53.613 [http-nio-8080-exec-52] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countWaitingTasks(..)] countWaitingTasks 执行耗时: 5 ms
2025-07-31 16:21:53.626 [http-nio-8080-exec-52] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countFinishedTasks(..)] countFinishedTasks 执行耗时: 13 ms
2025-07-31 16:21:53.634 [http-nio-8080-exec-52] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countMyApplyTasks(..)] countMyApplyTasks 执行耗时: 8 ms
2025-07-31 16:21:53.641 [http-nio-8080-exec-52] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countCcTasks(..)] countCcTasks 执行耗时: 7 ms
2025-07-31 16:21:53.641 [http-nio-8080-exec-52] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [PmsOrderServiceImpl.getApprovalStat(..)] getApprovalStat 执行耗时: 33 ms
2025-07-31 16:22:12.268 [http-nio-8080-exec-61] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countWaitingTasks(..)] countWaitingTasks 执行耗时: 5 ms
2025-07-31 16:22:12.274 [http-nio-8080-exec-61] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countFinishedTasks(..)] countFinishedTasks 执行耗时: 6 ms
2025-07-31 16:22:12.279 [http-nio-8080-exec-61] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countMyApplyTasks(..)] countMyApplyTasks 执行耗时: 5 ms
2025-07-31 16:22:12.292 [http-nio-8080-exec-61] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countCcTasks(..)] countCcTasks 执行耗时: 13 ms
2025-07-31 16:22:12.292 [http-nio-8080-exec-61] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [PmsOrderServiceImpl.getApprovalStat(..)] getApprovalStat 执行耗时: 29 ms
2025-07-31 16:23:53.894 [schedule-pool-1] INFO  sys-user - [run,57] - [127.0.0.1]内网IP[admin][Success][登录成功]
2025-07-31 16:54:58.878 [http-nio-8080-exec-21] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM project_approval WHERE creator  = ?  order by id asc LIMIT 0,20
2025-07-31 16:54:58.908 [http-nio-8080-exec-21] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: select count(1) as count from project_approval WHERE creator  = ? 
2025-07-31 17:16:50.004 [http-nio-8080-exec-31] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM project_approval WHERE creator  = ?  order by id asc LIMIT 0,20
2025-07-31 17:16:50.011 [http-nio-8080-exec-31] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: select count(1) as count from project_approval WHERE creator  = ? 
2025-07-31 17:23:01.735 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-31 17:23:01.736 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-31 17:23:01.736 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-31 17:23:01.736 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-31 17:23:01.736 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-31 17:23:01.736 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-31 17:23:01.736 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-31 17:23:01.736 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-31 17:23:01.737 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 17:23:01.904 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-31 17:23:01.905 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-31 17:23:01.905 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-31 17:23:01.932 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-31 17:23:02.047 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-31 17:23:02.072 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-31 17:23:05.805 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-31 17:23:05.816 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 29191 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-31 17:23:05.817 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-31 17:23:07.463 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-31 17:23:07.464 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-31 17:23:07.464 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-31 17:23:07.495 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-31 17:23:08.100 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-31 17:23:08.816 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-31 17:23:08.918 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for *************/*************:7379
2025-07-31 17:23:08.957 [redisson-netty-4-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for *************/*************:7379
2025-07-31 17:23:12.110 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-31 17:23:13.110 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-31 17:23:13.171 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-31 17:23:13.171 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-31 17:23:13.172 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-31 17:23:13.308 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-31 17:23:13.330 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-31 17:23:15.571 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-31 17:23:15.577 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-31 17:23:15.579 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-31 17:23:15.579 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-31 17:23:15.580 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-31 17:23:15.580 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-31 17:23:15.580 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-31 17:23:15.580 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@164fba04
2025-07-31 17:23:17.015 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-31 17:23:17.040 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-31 17:23:17.154 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-31 17:23:17.154 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-31 17:23:17.189 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-31 17:23:17.189 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-31 17:23:17.702 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 17:23:24.077 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-31 17:23:24.077 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-31 17:23:24.089 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-31 17:23:24.089 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-31 17:23:24.097 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 18.468 seconds (process running for 19.617)
2025-07-31 17:23:24.150 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-31 17:23:24.213 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 59 ms to scan 26 urls, producing 298 keys and 1817 values
2025-07-31 17:23:24.221 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 1 ms to scan 2 urls, producing 14 keys and 38 values
2025-07-31 22:59:15.057 [lettuce-eventExecutorLoop-1-1] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was /*************:7379
2025-07-31 22:59:15.106 [lettuce-nioEventLoop-7-3] INFO  i.l.c.p.ReconnectionHandler - [lambda$null$3,174] - Reconnected to *************/<unresolved>:7379
