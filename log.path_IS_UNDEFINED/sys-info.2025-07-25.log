2025-07-25 17:09:48.658 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-25 17:09:48.732 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-25 17:09:48.732 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-25 17:09:48.732 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-25 17:09:48.732 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-25 17:09:48.732 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-25 17:09:48.732 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-25 17:09:48.732 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-25 17:09:48.733 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-25 17:09:48.953 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-25 17:09:48.953 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-25 17:09:48.953 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-25 17:09:48.968 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-25 17:09:49.010 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-25 17:09:49.013 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-25 17:09:52.976 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-25 17:09:52.987 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 30426 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-25 17:09:52.987 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-25 17:10:04.600 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-25 17:10:04.613 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 30445 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-25 17:10:04.613 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-25 17:10:06.112 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-25 17:10:06.113 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-25 17:10:06.113 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-25 17:10:06.142 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-25 17:10:06.721 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-25 17:10:07.380 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-25 17:10:07.483 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for 192.168.61.10/192.168.61.10:7379
2025-07-25 17:10:07.523 [redisson-netty-4-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for 192.168.61.10/192.168.61.10:7379
2025-07-25 17:10:10.382 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-25 17:10:11.309 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-25 17:10:11.365 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-25 17:10:11.365 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-25 17:10:11.365 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-25 17:10:11.470 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-25 17:10:11.489 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-25 17:10:13.553 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-25 17:10:13.558 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-25 17:10:13.558 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-25 17:10:13.559 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-25 17:10:13.559 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-25 17:10:13.559 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-25 17:10:13.559 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-25 17:10:13.559 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@1f600254
2025-07-25 17:10:15.013 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-25 17:10:15.021 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-25 17:10:15.123 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-25 17:10:15.123 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-25 17:10:15.166 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-25 17:10:15.166 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-25 17:10:20.944 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-25 17:10:20.946 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-25 17:10:20.963 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-25 17:10:20.964 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-25 17:10:20.986 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 16.509 seconds (process running for 17.062)
2025-07-25 17:10:21.088 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-25 17:10:21.146 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 54 ms to scan 26 urls, producing 297 keys and 1804 values
2025-07-25 17:10:21.154 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 1 ms to scan 2 urls, producing 14 keys and 37 values
2025-07-25 17:10:21.367 [RMI TCP Connection(4)-*************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 17:11:12.946 [schedule-pool-1] INFO  sys-user - [run,57] - [127.0.0.1]内网IP[admin][Success][登录成功]
2025-07-25 17:11:13.604 [http-nio-8080-exec-16] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM xedasd order by id asc LIMIT 0,20
2025-07-25 17:11:13.609 [http-nio-8080-exec-16] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-25 17:11:17.109 [http-nio-8080-exec-26] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM xedasd order by id asc LIMIT 0,20
2025-07-25 17:11:17.113 [http-nio-8080-exec-26] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-25 17:11:21.361 [http-nio-8080-exec-40] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  is null  order by id asc
2025-07-25 17:11:21.378 [http-nio-8080-exec-40] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  is null  order by id asc
2025-07-25 17:11:27.347 [http-nio-8080-exec-46] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-25 17:11:27.351 [http-nio-8080-exec-46] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-25 17:11:28.040 [http-nio-8080-exec-49] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM xedasd order by id asc LIMIT 0,20
2025-07-25 17:11:28.043 [http-nio-8080-exec-49] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-25 17:11:29.777 [http-nio-8080-exec-51] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-25 17:11:29.793 [http-nio-8080-exec-51] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-25 17:11:30.177 [http-nio-8080-exec-52] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-25 17:11:30.191 [http-nio-8080-exec-52] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ?  order by id asc
2025-07-25 17:11:46.659 [http-nio-8080-exec-61] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM xedasd order by id asc LIMIT 0,20
2025-07-25 17:11:46.682 [http-nio-8080-exec-61] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-25 17:12:09.914 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-25 17:12:09.916 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-25 17:12:09.916 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-25 17:12:09.916 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-25 17:12:09.916 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-25 17:12:09.916 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-25 17:12:09.916 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-25 17:12:09.916 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-25 17:12:09.916 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-25 17:12:10.076 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-25 17:12:10.076 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-25 17:12:10.076 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-25 17:12:10.083 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-25 17:12:10.108 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-25 17:12:10.127 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-25 17:12:12.607 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-25 17:12:12.618 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 30530 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-25 17:12:12.618 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-25 17:12:14.108 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-25 17:12:14.108 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-25 17:12:14.108 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-25 17:12:14.138 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-25 17:12:14.715 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-25 17:12:15.390 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-25 17:12:15.490 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for 192.168.61.10/192.168.61.10:7379
2025-07-25 17:12:15.530 [redisson-netty-4-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for 192.168.61.10/192.168.61.10:7379
2025-07-25 17:12:18.497 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-25 17:12:19.465 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-25 17:12:19.524 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-25 17:12:19.524 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-25 17:12:19.524 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-25 17:12:19.638 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-25 17:12:19.654 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-25 17:12:21.701 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-25 17:12:21.706 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-25 17:12:21.706 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-25 17:12:21.706 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-25 17:12:21.706 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-25 17:12:21.706 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-25 17:12:21.706 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-25 17:12:21.706 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@376314dd
2025-07-25 17:12:23.014 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-25 17:12:23.022 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-25 17:12:23.115 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-25 17:12:23.115 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-25 17:12:23.148 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-25 17:12:23.148 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-25 17:12:28.729 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-25 17:12:28.731 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-25 17:12:28.748 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-25 17:12:28.749 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-25 17:12:28.771 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 16.293 seconds (process running for 16.911)
2025-07-25 17:12:28.865 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 78 ms to scan 26 urls, producing 297 keys and 1804 values
2025-07-25 17:12:28.873 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 1 ms to scan 2 urls, producing 14 keys and 37 values
2025-07-25 17:12:28.963 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-25 17:12:29.365 [RMI TCP Connection(3)-*************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 17:13:05.004 [http-nio-8080-exec-9] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM xedasd order by id asc LIMIT 0,20
2025-07-25 17:13:05.013 [http-nio-8080-exec-9] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-25 17:13:11.925 [http-nio-8080-exec-19] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM xedasd order by id asc LIMIT 0,20
2025-07-25 17:13:19.813 [http-nio-8080-exec-19] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-25 17:13:42.469 [http-nio-8080-exec-40] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM xedasd order by id asc LIMIT 0,20
2025-07-25 17:13:42.472 [http-nio-8080-exec-40] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-25 17:13:45.808 [http-nio-8080-exec-44] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM xedasd WHERE creator  = ?  order by id asc LIMIT 0,20
2025-07-25 17:13:45.814 [http-nio-8080-exec-44] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: select count(1) as count from xedasd WHERE creator  = ? 
2025-07-25 17:13:47.349 [http-nio-8080-exec-45] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM xedasd WHERE creator  = ?  order by id asc LIMIT 0,20
2025-07-25 17:13:47.352 [http-nio-8080-exec-45] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: select count(1) as count from xedasd WHERE creator  = ? 
2025-07-25 17:13:50.803 [http-nio-8080-exec-46] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM xedasd WHERE creator  = ?  order by id asc LIMIT 0,20
2025-07-25 17:14:10.725 [http-nio-8080-exec-46] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: select count(1) as count from xedasd WHERE creator  = ? 
2025-07-25 18:23:51.724 [http-nio-8080-exec-73] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: SELECT * FROM xedasd order by id asc LIMIT 0,20
2025-07-25 18:23:51.727 [http-nio-8080-exec-73] INFO  c.r.lowcode.orm.ORM - [printSql,282] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-25 21:03:54.064 [lettuce-eventExecutorLoop-1-1] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was /192.168.61.10:7379
2025-07-25 21:03:54.078 [lettuce-nioEventLoop-7-3] INFO  i.l.c.p.ReconnectionHandler - [lambda$null$3,174] - Reconnected to 192.168.61.10/<unresolved>:7379
