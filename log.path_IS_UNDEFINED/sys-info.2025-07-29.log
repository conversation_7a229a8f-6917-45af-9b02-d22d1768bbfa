2025-07-29 09:22:17.486 [lettuce-eventExecutorLoop-1-2] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was /*************:7379
2025-07-29 09:22:17.531 [lettuce-nioEventLoop-7-4] INFO  i.l.c.p.ReconnectionHandler - [lambda$null$3,174] - Reconnected to *************/<unresolved>:7379
2025-07-29 09:22:37.697 [schedule-pool-1] INFO  sys-user - [run,57] - [127.0.0.1]内网IP[admin][Success][登录成功]
2025-07-29 09:23:56.806 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-29 09:23:56.807 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-29 09:23:56.807 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-29 09:23:56.807 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-29 09:23:56.807 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-29 09:23:56.807 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-29 09:23:56.807 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-29 09:23:56.807 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-29 09:23:56.809 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-29 09:23:56.977 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-29 09:23:56.977 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-29 09:23:56.977 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-29 09:23:56.993 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-29 09:23:57.020 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-29 09:23:57.033 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-29 09:24:19.733 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-29 09:24:19.746 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 88665 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-29 09:24:19.747 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-29 09:24:21.429 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-29 09:24:21.429 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-29 09:24:21.429 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-29 09:24:21.460 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-29 09:24:22.165 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-29 09:24:23.013 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-29 09:24:23.120 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for *************/*************:7379
2025-07-29 09:24:23.161 [redisson-netty-4-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for *************/*************:7379
2025-07-29 09:24:26.393 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-29 09:24:28.049 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-29 09:24:28.126 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-29 09:24:28.126 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-29 09:24:28.126 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-29 09:24:28.262 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-29 09:24:28.287 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-29 09:24:30.465 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-29 09:24:30.470 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-29 09:24:30.470 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-29 09:24:30.470 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-29 09:24:30.470 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-29 09:24:30.470 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-29 09:24:30.470 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-29 09:24:30.470 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@18211894
2025-07-29 09:24:31.878 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-29 09:24:31.888 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-29 09:24:31.992 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-29 09:24:31.992 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-29 09:24:32.028 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-29 09:24:32.028 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-29 09:24:32.969 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-29 09:24:37.495 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-29 09:24:37.496 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-29 09:24:37.509 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-29 09:24:37.509 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-29 09:24:37.517 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 17.926 seconds (process running for 18.983)
2025-07-29 09:24:37.599 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-29 09:24:37.678 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 74 ms to scan 26 urls, producing 298 keys and 1815 values
2025-07-29 09:24:37.685 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 1 ms to scan 2 urls, producing 14 keys and 37 values
2025-07-29 09:24:44.971 [schedule-pool-1] INFO  sys-user - [run,57] - [127.0.0.1]内网IP[admin][Logout][退出成功]
2025-07-29 09:24:55.085 [schedule-pool-1] INFO  sys-user - [run,57] - [127.0.0.1]内网IP[admin][Success][登录成功]
2025-07-29 09:24:55.618 [http-nio-8080-exec-29] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countWaitingTasks(..)] countWaitingTasks 执行耗时: 13 ms
2025-07-29 09:24:55.633 [http-nio-8080-exec-29] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countFinishedTasks(..)] countFinishedTasks 执行耗时: 15 ms
2025-07-29 09:24:55.643 [http-nio-8080-exec-29] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countMyApplyTasks(..)] countMyApplyTasks 执行耗时: 10 ms
2025-07-29 09:24:55.646 [http-nio-8080-exec-29] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countCcTasks(..)] countCcTasks 执行耗时: 3 ms
2025-07-29 09:24:55.646 [http-nio-8080-exec-29] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [PmsOrderServiceImpl.getApprovalStat(..)] getApprovalStat 执行耗时: 44 ms
2025-07-29 10:24:09.053 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-29 10:24:09.068 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 91019 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-29 10:24:09.069 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-29 10:24:10.656 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-29 10:24:10.657 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-29 10:24:10.657 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-29 10:24:10.690 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-29 10:24:12.909 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-29 10:24:13.862 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-29 10:24:14.149 [redisson-netty-4-22] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for *************/*************:7379
2025-07-29 10:24:14.379 [redisson-netty-4-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for *************/*************:7379
2025-07-29 10:24:17.772 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-29 10:24:18.762 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-29 10:24:18.826 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-29 10:24:18.826 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-29 10:24:18.826 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-29 10:24:18.964 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-29 10:24:19.104 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-29 10:24:23.140 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-29 10:24:23.145 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-29 10:24:23.145 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-29 10:24:23.146 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-29 10:24:23.146 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-29 10:24:23.146 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-29 10:24:23.146 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-29 10:24:23.146 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@750a4fc1
2025-07-29 10:24:27.234 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-29 10:24:27.358 [main] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-29 10:24:27.358 [main] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-29 10:24:27.359 [main] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-29 10:24:27.367 [main] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-29 10:24:27.406 [main] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-29 10:24:27.425 [main] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-29 10:25:42.845 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-29 10:25:42.865 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 91087 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-29 10:25:42.866 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-29 10:25:44.562 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-29 10:25:44.563 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-29 10:25:44.563 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-29 10:25:44.594 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-29 10:25:45.842 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-29 10:25:46.527 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-29 10:25:46.723 [redisson-netty-4-21] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for *************/*************:7379
2025-07-29 10:25:46.956 [redisson-netty-4-18] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for *************/*************:7379
2025-07-29 10:25:50.184 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-29 10:25:51.206 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-29 10:25:51.268 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-29 10:25:51.268 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-29 10:25:51.268 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-29 10:25:51.376 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-29 10:25:51.533 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-29 10:25:55.324 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-29 10:25:55.330 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-29 10:25:55.330 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-29 10:25:55.330 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-29 10:25:55.331 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-29 10:25:55.331 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-29 10:25:55.331 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-29 10:25:55.331 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@16d8a25e
2025-07-29 10:25:59.620 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-29 10:25:59.732 [main] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-29 10:25:59.732 [main] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-29 10:25:59.732 [main] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-29 10:25:59.738 [main] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-29 10:25:59.781 [main] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-29 10:25:59.801 [main] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-29 10:35:11.262 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-29 10:35:11.276 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 91649 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-29 10:35:11.276 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-29 10:35:12.996 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-29 10:35:12.997 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-29 10:35:12.997 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-29 10:35:13.029 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-29 10:35:14.908 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-29 10:35:15.672 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-29 10:35:15.989 [redisson-netty-4-26] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for *************/*************:7379
2025-07-29 10:35:16.384 [redisson-netty-4-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for *************/*************:7379
2025-07-29 10:35:19.820 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-29 10:35:20.856 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-29 10:35:20.920 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-29 10:35:20.920 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-29 10:35:20.920 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-29 10:35:21.052 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-29 10:35:21.168 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-29 10:35:24.790 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-29 10:35:24.796 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-29 10:35:24.796 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-29 10:35:24.796 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-29 10:35:24.796 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-29 10:35:24.796 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-29 10:35:24.796 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-29 10:35:24.796 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@79f6e671
2025-07-29 10:35:30.226 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-29 10:35:30.244 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-29 10:35:33.314 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-29 10:35:33.314 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-29 10:35:33.920 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-29 10:35:33.920 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-29 10:35:42.858 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-29 10:36:23.745 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-29 10:36:23.746 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-29 10:36:23.780 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-29 10:36:23.780 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-29 10:36:23.798 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 72.678 seconds (process running for 73.656)
2025-07-29 10:36:23.993 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-29 10:36:24.154 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 155 ms to scan 26 urls, producing 298 keys and 1815 values
2025-07-29 10:36:24.178 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 4 ms to scan 2 urls, producing 14 keys and 37 values
2025-07-29 11:21:53.330 [quartzScheduler_Worker-1] INFO  c.r.e.s.i.EqptEquipmentWorkRecordExtServiceImpl - [genEquipmentWorkRecordStat,52] - start genEquipmentWorkRecordStat: startTime=2025-06-01 00:00:00, endTime=2025-06-30 23:59:59
2025-07-29 11:21:53.797 [quartzScheduler_Worker-1] INFO  c.r.e.s.i.EqptEquipmentWorkRecordExtServiceImpl - [genEquipmentWorkRecordStat,205] - end genEquipmentWorkRecordStat
2025-07-29 14:27:59.818 [lettuce-eventExecutorLoop-1-1] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was /*************:7379
2025-07-29 14:27:59.849 [lettuce-nioEventLoop-7-3] INFO  i.l.c.p.ReconnectionHandler - [lambda$null$3,174] - Reconnected to *************/<unresolved>:7379
2025-07-29 14:28:00.962 [schedule-pool-2] INFO  sys-user - [run,57] - [127.0.0.1]内网IP[admin][Logout][退出成功]
2025-07-29 14:28:04.724 [schedule-pool-1] INFO  sys-user - [run,57] - [127.0.0.1]内网IP[admin][Success][登录成功]
2025-07-29 14:28:05.233 [http-nio-8080-exec-3] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countWaitingTasks(..)] countWaitingTasks 执行耗时: 7 ms
2025-07-29 14:28:05.247 [http-nio-8080-exec-3] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countFinishedTasks(..)] countFinishedTasks 执行耗时: 14 ms
2025-07-29 14:28:05.257 [http-nio-8080-exec-3] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countMyApplyTasks(..)] countMyApplyTasks 执行耗时: 10 ms
2025-07-29 14:28:05.262 [http-nio-8080-exec-3] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countCcTasks(..)] countCcTasks 执行耗时: 5 ms
2025-07-29 14:28:05.263 [http-nio-8080-exec-3] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [PmsOrderServiceImpl.getApprovalStat(..)] getApprovalStat 执行耗时: 38 ms
2025-07-29 14:47:11.391 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-29 14:47:11.392 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-29 14:47:11.392 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-29 14:47:11.392 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-29 14:47:11.392 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-29 14:47:11.392 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-29 14:47:11.392 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-29 14:47:11.392 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-29 14:47:11.393 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-29 14:47:11.555 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-29 14:47:11.555 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-29 14:47:11.555 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-29 14:47:11.566 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-29 14:47:11.591 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-29 14:47:11.594 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-29 14:49:03.987 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-29 14:49:03.997 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 11534 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-29 14:49:03.998 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-29 14:49:05.704 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-29 14:49:05.705 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-29 14:49:05.705 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-29 14:49:05.737 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-29 14:49:06.349 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-29 14:49:07.104 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-29 14:49:07.218 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for *************/*************:7379
2025-07-29 14:49:07.258 [redisson-netty-4-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for *************/*************:7379
2025-07-29 14:49:10.461 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-29 14:49:11.520 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-29 14:49:11.581 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-29 14:49:11.581 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-29 14:49:11.581 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-29 14:49:11.685 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-29 14:49:11.701 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-29 14:49:13.854 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-29 14:49:13.859 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-29 14:49:13.859 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-29 14:49:13.859 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-29 14:49:13.860 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-29 14:49:13.860 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-29 14:49:13.860 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-29 14:49:13.860 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@25a8ff84
2025-07-29 14:49:15.209 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-29 14:49:15.221 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-29 14:49:15.321 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-29 14:49:15.321 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-29 14:49:15.356 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-29 14:49:15.356 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-29 14:49:21.787 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-29 14:49:21.788 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-29 14:49:21.800 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-29 14:49:21.800 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-29 14:49:21.808 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 17.96 seconds (process running for 19.213)
2025-07-29 14:49:21.904 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-29 14:49:21.999 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 90 ms to scan 26 urls, producing 298 keys and 1816 values
2025-07-29 14:49:22.012 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 1 ms to scan 2 urls, producing 14 keys and 37 values
2025-07-29 14:49:35.508 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-29 15:10:42.114 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-29 15:10:42.115 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-29 15:10:42.115 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-29 15:10:42.115 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-29 15:10:42.115 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-29 15:10:42.115 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-29 15:10:42.115 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-29 15:10:42.115 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-29 15:10:42.115 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-29 15:10:42.273 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-29 15:10:42.274 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-29 15:10:42.274 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-29 15:10:42.281 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-29 15:10:42.307 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-29 15:10:42.321 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-29 15:10:46.635 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-29 15:10:46.648 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 13036 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-29 15:10:46.649 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-29 15:10:48.307 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-29 15:10:48.308 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-29 15:10:48.308 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-29 15:10:48.340 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-29 15:10:48.952 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-29 15:10:49.807 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-29 15:10:49.917 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for *************/*************:7379
2025-07-29 15:10:49.967 [redisson-netty-4-17] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for *************/*************:7379
2025-07-29 15:10:53.311 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-29 15:10:54.436 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-29 15:10:54.497 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-29 15:10:54.497 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-29 15:10:54.497 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-29 15:10:54.608 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-29 15:10:54.627 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-29 15:10:56.804 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-29 15:10:56.809 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-29 15:10:56.809 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-29 15:10:56.810 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-29 15:10:56.810 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-29 15:10:56.810 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-29 15:10:56.810 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-29 15:10:56.810 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@251d3913
2025-07-29 15:10:58.248 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-29 15:10:58.260 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-29 15:10:58.360 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-29 15:10:58.360 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-29 15:10:58.408 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-29 15:10:58.409 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-29 15:11:04.729 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-29 15:11:04.731 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-29 15:11:04.745 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-29 15:11:04.745 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-29 15:11:04.761 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 18.268 seconds (process running for 19.5)
2025-07-29 15:11:04.843 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 73 ms to scan 26 urls, producing 298 keys and 1816 values
2025-07-29 15:11:04.852 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 1 ms to scan 2 urls, producing 14 keys and 37 values
2025-07-29 15:11:04.994 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-29 15:11:05.320 [RMI TCP Connection(4)-192.168.61.57] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-29 16:15:38.356 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-29 16:15:38.357 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-29 16:15:38.357 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-29 16:15:38.357 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-29 16:15:38.357 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-29 16:15:38.357 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-29 16:15:38.357 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-29 16:15:38.357 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-29 16:15:38.357 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-29 16:15:38.500 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-29 16:15:38.500 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-29 16:15:38.500 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-29 16:15:38.513 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-29 16:15:38.541 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-29 16:15:38.550 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-29 16:15:43.168 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-29 16:15:43.180 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 17125 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-29 16:15:43.181 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-29 16:15:44.784 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-29 16:15:44.785 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-29 16:15:44.785 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-29 16:15:44.815 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-29 16:15:45.429 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-29 16:15:46.124 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-29 16:15:46.230 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for *************/*************:7379
2025-07-29 16:15:46.266 [redisson-netty-4-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for *************/*************:7379
2025-07-29 16:15:49.437 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-29 16:15:50.471 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-29 16:15:50.548 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-29 16:15:50.548 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-29 16:15:50.548 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-29 16:15:50.663 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-29 16:15:50.692 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-29 16:15:52.940 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-29 16:15:52.945 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-29 16:15:52.945 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-29 16:15:52.945 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-29 16:15:52.946 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-29 16:15:52.946 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-29 16:15:52.946 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-29 16:15:52.946 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@2181e6b6
2025-07-29 16:15:54.425 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-29 16:15:54.448 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-29 16:15:54.558 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-29 16:15:54.558 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-29 16:15:54.599 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-29 16:15:54.599 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-29 16:16:00.524 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-29 16:16:00.525 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-29 16:16:00.539 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-29 16:16:00.540 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-29 16:16:00.553 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 17.526 seconds (process running for 18.752)
2025-07-29 16:16:00.664 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-29 16:16:00.728 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 59 ms to scan 26 urls, producing 298 keys and 1816 values
2025-07-29 16:16:00.741 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 2 ms to scan 2 urls, producing 14 keys and 37 values
2025-07-29 16:18:29.466 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-29 16:29:36.226 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-29 16:29:36.228 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-29 16:29:36.229 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-29 16:29:36.229 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-29 16:29:36.229 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-29 16:29:36.229 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-29 16:29:36.229 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-29 16:29:36.229 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-29 16:29:36.230 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-29 16:29:36.392 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-29 16:29:36.393 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-29 16:29:36.393 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-29 16:29:36.400 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-29 16:29:36.424 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-29 16:29:36.447 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-29 16:29:39.278 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-29 16:29:39.288 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 17944 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-29 16:29:39.289 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-29 16:29:40.904 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-29 16:29:40.905 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-29 16:29:40.905 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-29 16:29:40.935 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-29 16:29:41.538 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-29 16:29:42.240 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-29 16:29:42.341 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for *************/*************:7379
2025-07-29 16:29:42.380 [redisson-netty-4-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for *************/*************:7379
2025-07-29 16:29:46.071 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-29 16:29:47.113 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-29 16:29:47.184 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-29 16:29:47.184 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-29 16:29:47.184 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-29 16:29:47.318 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-29 16:29:47.334 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-29 16:29:49.932 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-29 16:29:49.937 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-29 16:29:49.937 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-29 16:29:49.938 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-29 16:29:49.938 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-29 16:29:49.938 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-29 16:29:49.938 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-29 16:29:49.938 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@671f70bc
2025-07-29 16:29:51.606 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-29 16:29:51.617 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-29 16:29:51.729 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-29 16:29:51.729 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-29 16:29:51.815 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-29 16:29:51.815 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-29 16:29:58.339 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-29 16:29:58.339 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-29 16:29:58.352 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-29 16:29:58.354 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-29 16:29:58.366 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 19.241 seconds (process running for 19.892)
2025-07-29 16:29:58.470 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-29 16:29:58.577 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 103 ms to scan 26 urls, producing 298 keys and 1816 values
2025-07-29 16:29:58.591 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 2 ms to scan 2 urls, producing 14 keys and 37 values
2025-07-29 16:29:58.784 [RMI TCP Connection(3)-192.168.61.57] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-29 21:06:42.493 [lettuce-eventExecutorLoop-1-1] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was /*************:7379
2025-07-29 21:06:42.512 [lettuce-nioEventLoop-7-3] INFO  i.l.c.p.ReconnectionHandler - [lambda$null$3,174] - Reconnected to *************/<unresolved>:7379
