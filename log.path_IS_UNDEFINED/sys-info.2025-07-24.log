2025-07-24 09:56:52.076 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 09:56:52.081 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 09:56:52.081 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 09:56:52.081 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 09:56:52.081 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 09:56:52.081 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 09:56:52.081 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 09:56:52.082 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 09:56:52.082 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-24 09:56:52.378 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-24 09:56:52.379 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-24 09:56:52.379 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-24 09:56:52.395 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-24 09:56:52.442 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-24 09:56:52.445 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-24 09:56:58.322 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-24 09:56:58.337 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 85040 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-24 09:56:58.338 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-24 09:57:00.618 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-24 09:57:00.620 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-24 09:57:00.620 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-24 09:57:00.663 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-24 09:57:01.395 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-24 09:57:02.175 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-24 09:57:02.284 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for *************/*************:7379
2025-07-24 09:57:02.321 [redisson-netty-4-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for *************/*************:7379
2025-07-24 09:57:05.583 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-24 09:57:06.650 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-24 09:57:06.722 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-24 09:57:06.722 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 09:57:06.722 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 09:57:07.121 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 09:57:07.144 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-24 09:57:09.548 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-24 09:57:09.557 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-24 09:57:09.557 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-24 09:57:09.558 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-24 09:57:09.558 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-24 09:57:09.558 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-24 09:57:09.558 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-24 09:57:09.558 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@671f70bc
2025-07-24 09:57:11.324 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-24 09:57:11.338 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 09:57:11.453 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 09:57:11.454 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 09:57:11.488 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 09:57:11.488 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 09:57:17.313 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 09:57:17.316 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 09:57:17.336 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 09:57:17.336 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-24 09:57:17.351 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 19.346 seconds (process running for 20.926)
2025-07-24 09:57:17.575 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 211 ms to scan 26 urls, producing 296 keys and 1803 values
2025-07-24 09:57:17.586 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 2 ms to scan 2 urls, producing 13 keys and 34 values
2025-07-24 09:57:17.683 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-24 09:57:17.908 [RMI TCP Connection(1)-*************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-24 09:58:07.714 [schedule-pool-1] INFO  sys-user - [run,57] - [127.0.0.1]内网IP[admin][Success][登录成功]
2025-07-24 10:04:50.464 [http-nio-8080-exec-11] INFO  c.r.lowcode.orm.ORM - [selectPageToMap,94] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-24 10:05:16.164 [http-nio-8080-exec-16] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT owner_dept,updater,id FROM xedasd
2025-07-24 10:05:16.194 [http-nio-8080-exec-16] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_child
2025-07-24 10:05:16.206 [http-nio-8080-exec-16] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_test_child
2025-07-24 10:07:54.401 [http-nio-8080-exec-23] INFO  c.r.lowcode.orm.ORM - [selectPageToMap,94] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-24 10:09:25.146 [http-nio-8080-exec-57] INFO  c.r.lowcode.orm.ORM - [selectPageToMap,94] - [ORM] 执行sql: select count(1) as count from project_approval WHERE creator  = ? 
2025-07-24 10:09:39.431 [http-nio-8080-exec-61] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT id,name,create_time,associated_project_id,project_abbr,update_time,code,project_status,project_supervisor_id,project_assistant_id,category_code,planned_start_at,id FROM project_approval
2025-07-24 10:34:23.904 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 10:34:23.905 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 10:34:23.905 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 10:34:23.905 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 10:34:23.905 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 10:34:23.905 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 10:34:23.905 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 10:34:23.905 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 10:34:23.905 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-24 10:34:24.051 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-24 10:34:24.051 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-24 10:34:24.051 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-24 10:34:24.060 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-24 10:34:24.085 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-24 10:34:24.087 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-24 10:35:20.107 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-24 10:35:20.119 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 86642 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-24 10:35:20.119 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-24 10:35:21.644 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-24 10:35:21.644 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-24 10:35:21.644 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-24 10:35:21.674 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-24 10:35:22.345 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-24 10:35:22.996 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-24 10:35:23.094 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for *************/*************:7379
2025-07-24 10:35:23.137 [redisson-netty-4-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for *************/*************:7379
2025-07-24 10:35:26.140 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-24 10:35:27.154 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-24 10:35:27.210 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-24 10:35:27.210 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 10:35:27.210 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 10:35:27.309 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 10:35:27.324 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-24 10:35:29.307 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-24 10:35:29.312 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-24 10:35:29.312 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-24 10:35:29.312 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-24 10:35:29.313 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-24 10:35:29.313 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-24 10:35:29.313 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-24 10:35:29.313 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@167eab4
2025-07-24 10:35:30.630 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-24 10:35:30.640 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 10:35:30.730 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 10:35:30.730 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 10:35:30.765 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 10:35:30.765 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 10:35:36.936 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 10:35:36.938 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 10:35:36.954 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 10:35:36.955 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-24 10:35:36.983 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 17.017 seconds (process running for 18.209)
2025-07-24 10:35:37.182 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 167 ms to scan 26 urls, producing 296 keys and 1803 values
2025-07-24 10:35:37.194 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 1 ms to scan 2 urls, producing 13 keys and 34 values
2025-07-24 10:35:37.340 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-24 10:35:37.865 [RMI TCP Connection(2)-*************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-24 10:35:41.078 [http-nio-8080-exec-1] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT id,name,create_time,associated_project_id,project_abbr,update_time,code,project_status,project_supervisor_id,project_assistant_id,category_code,planned_start_at,id FROM project_approval
2025-07-24 10:36:24.768 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 10:36:24.769 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 10:36:24.769 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 10:36:24.769 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 10:36:24.769 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 10:36:24.769 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 10:36:24.770 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 10:36:24.770 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 10:36:24.770 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-24 10:36:24.915 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-24 10:36:24.915 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-24 10:36:24.916 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-24 10:36:24.933 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-24 10:36:24.983 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-24 10:36:25.010 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-24 10:36:28.845 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-24 10:36:28.857 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 86723 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-24 10:36:28.857 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-24 10:36:30.462 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-24 10:36:30.463 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-24 10:36:30.464 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-24 10:36:30.498 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-24 10:36:31.083 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-24 10:36:31.769 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-24 10:36:31.876 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for *************/*************:7379
2025-07-24 10:36:31.914 [redisson-netty-4-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for *************/*************:7379
2025-07-24 10:36:34.878 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-24 10:36:35.824 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-24 10:36:35.882 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-24 10:36:35.882 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 10:36:35.882 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 10:36:35.988 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 10:36:36.003 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-24 10:36:38.012 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-24 10:36:38.018 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-24 10:36:38.018 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-24 10:36:38.018 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-24 10:36:38.018 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-24 10:36:38.018 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-24 10:36:38.018 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-24 10:36:38.018 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@525dd5af
2025-07-24 10:36:39.331 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-24 10:36:39.339 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 10:36:39.432 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 10:36:39.432 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 10:36:39.468 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 10:36:39.468 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 10:36:45.113 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 10:36:45.115 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 10:36:45.132 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 10:36:45.133 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-24 10:36:45.154 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 16.471 seconds (process running for 17.35)
2025-07-24 10:36:45.267 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-24 10:36:45.342 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 71 ms to scan 26 urls, producing 296 keys and 1803 values
2025-07-24 10:36:45.352 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 2 ms to scan 2 urls, producing 13 keys and 34 values
2025-07-24 10:36:45.805 [RMI TCP Connection(2)-*************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-24 10:37:28.936 [http-nio-8080-exec-1] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT id,name,create_time,associated_project_id,project_abbr,update_time,code,project_status,project_supervisor_id,project_assistant_id,category_code,planned_start_at,id FROM project_approval
2025-07-24 10:38:32.686 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 10:38:32.688 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 10:38:32.688 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 10:38:32.688 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 10:38:32.688 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 10:38:32.688 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 10:38:32.688 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 10:38:32.688 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 10:38:32.688 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-24 10:38:32.842 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-24 10:38:32.843 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-24 10:38:32.843 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-24 10:38:32.849 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-24 10:38:32.866 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-24 10:38:32.887 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-24 10:38:35.282 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-24 10:38:35.292 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 86800 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-24 10:38:35.293 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-24 10:38:36.776 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-24 10:38:36.777 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-24 10:38:36.777 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-24 10:38:36.806 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-24 10:38:37.355 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-24 10:38:38.081 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-24 10:38:38.185 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for *************/*************:7379
2025-07-24 10:38:38.224 [redisson-netty-4-17] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for *************/*************:7379
2025-07-24 10:38:41.276 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-24 10:38:42.306 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-24 10:38:42.363 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-24 10:38:42.363 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 10:38:42.363 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 10:38:42.479 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 10:38:42.498 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-24 10:38:44.461 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-24 10:38:44.465 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-24 10:38:44.466 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-24 10:38:44.466 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-24 10:38:44.466 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-24 10:38:44.466 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-24 10:38:44.466 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-24 10:38:44.466 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@4d9ef371
2025-07-24 10:38:45.781 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-24 10:38:45.789 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 10:38:45.881 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 10:38:45.881 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 10:38:45.914 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 10:38:45.914 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 10:38:51.636 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 10:38:51.637 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 10:38:51.652 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 10:38:51.652 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-24 10:38:51.667 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 16.523 seconds (process running for 17.075)
2025-07-24 10:38:51.770 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-24 10:38:51.857 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 82 ms to scan 26 urls, producing 296 keys and 1803 values
2025-07-24 10:38:51.872 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 2 ms to scan 2 urls, producing 13 keys and 34 values
2025-07-24 10:38:52.077 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-24 10:38:52.377 [http-nio-8080-exec-1] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT id,name,create_time,associated_project_id,project_abbr,update_time,code,project_status,project_supervisor_id,project_assistant_id,category_code,planned_start_at,id FROM project_approval
2025-07-24 10:39:13.386 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 10:39:13.387 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 10:39:13.387 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 10:39:13.387 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 10:39:13.387 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 10:39:13.387 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 10:39:13.387 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 10:39:13.387 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 10:39:13.387 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-24 10:39:13.521 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-24 10:39:13.521 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-24 10:39:13.521 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-24 10:39:13.528 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-24 10:39:13.548 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-24 10:39:13.568 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-24 10:39:16.101 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-24 10:39:16.113 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 86833 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-24 10:39:16.114 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-24 10:39:17.642 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-24 10:39:17.643 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-24 10:39:17.643 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-24 10:39:17.678 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-24 10:39:18.234 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-24 10:39:18.881 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-24 10:39:18.977 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for *************/*************:7379
2025-07-24 10:39:19.014 [redisson-netty-4-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for *************/*************:7379
2025-07-24 10:39:22.005 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-24 10:39:22.991 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-24 10:39:23.047 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-24 10:39:23.048 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 10:39:23.048 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 10:39:23.159 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 10:39:23.178 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-24 10:39:25.260 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-24 10:39:25.265 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-24 10:39:25.265 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-24 10:39:25.266 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-24 10:39:25.266 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-24 10:39:25.266 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-24 10:39:25.266 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-24 10:39:25.266 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@607c0933
2025-07-24 10:39:26.589 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-24 10:39:26.604 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 10:39:26.698 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 10:39:26.698 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 10:39:26.729 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 10:39:26.729 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 10:39:32.392 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 10:39:32.393 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 10:39:32.411 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 10:39:32.412 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-24 10:39:32.436 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 16.476 seconds (process running for 17.092)
2025-07-24 10:39:32.542 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 87 ms to scan 26 urls, producing 296 keys and 1803 values
2025-07-24 10:39:32.550 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 1 ms to scan 2 urls, producing 13 keys and 34 values
2025-07-24 10:39:32.636 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-24 10:39:32.816 [RMI TCP Connection(2)-*************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-24 10:40:13.110 [http-nio-8080-exec-1] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT id,name,create_time,associated_project_id,project_abbr,update_time,code,project_status,project_supervisor_id,project_assistant_id,category_code,planned_start_at,id FROM project_approval
2025-07-24 10:40:56.250 [http-nio-8080-exec-2] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT id,name,create_time,associated_project_id,project_abbr,update_time,code,project_status,project_supervisor_id,project_assistant_id,category_code,planned_start_at,id FROM project_approval
2025-07-24 10:43:55.713 [http-nio-8080-exec-3] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT id,name,create_time,associated_project_id,project_abbr,update_time,code,project_status,project_supervisor_id,project_assistant_id,category_code,planned_start_at,id FROM project_approval
2025-07-24 10:44:12.445 [http-nio-8080-exec-4] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT id,name,create_time,associated_project_id,project_abbr,update_time,code,project_status,project_supervisor_id,project_assistant_id,category_code,planned_start_at,id FROM project_approval
2025-07-24 10:49:57.668 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 10:49:57.670 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 10:49:57.670 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 10:49:57.670 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 10:49:57.670 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 10:49:57.670 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 10:49:57.670 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 10:49:57.670 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 10:49:57.670 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-24 10:49:57.818 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-24 10:49:57.818 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-24 10:49:57.818 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-24 10:49:57.825 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-24 10:49:57.846 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-24 10:49:57.864 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-24 10:50:01.043 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-24 10:50:01.061 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 87312 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-24 10:50:01.062 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-24 10:50:02.716 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-24 10:50:02.717 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-24 10:50:02.717 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-24 10:50:02.747 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-24 10:50:03.332 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-24 10:50:04.050 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-24 10:50:04.160 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for *************/*************:7379
2025-07-24 10:50:04.197 [redisson-netty-4-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for *************/*************:7379
2025-07-24 10:50:07.172 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-24 10:50:08.134 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-24 10:50:08.190 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-24 10:50:08.191 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 10:50:08.191 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 10:50:08.298 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 10:50:08.313 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-24 10:50:10.281 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-24 10:50:10.286 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-24 10:50:10.286 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-24 10:50:10.286 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-24 10:50:10.286 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-24 10:50:10.287 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-24 10:50:10.287 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-24 10:50:10.287 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@1a596879
2025-07-24 10:50:11.641 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-24 10:50:11.654 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 10:50:11.754 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 10:50:11.754 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 10:50:11.785 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 10:50:11.786 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 10:50:17.711 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 10:50:17.712 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 10:50:17.730 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 10:50:17.731 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-24 10:50:17.752 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 16.878 seconds (process running for 18.064)
2025-07-24 10:50:17.856 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-24 10:50:17.932 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 71 ms to scan 26 urls, producing 296 keys and 1803 values
2025-07-24 10:50:17.946 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 2 ms to scan 2 urls, producing 13 keys and 34 values
2025-07-24 10:50:18.108 [RMI TCP Connection(3)-*************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-24 10:51:10.153 [http-nio-8080-exec-1] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT id,name,create_time,associated_project_id,project_abbr,update_time,code,project_status,project_supervisor_id,project_assistant_id,category_code,planned_start_at,id FROM project_approval
2025-07-24 11:00:48.246 [http-nio-8080-exec-7] INFO  c.r.lowcode.orm.ORM - [selectPageToMap,94] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-24 11:00:48.266 [http-nio-8080-exec-6] INFO  c.r.lowcode.orm.ORM - [selectPageToMap,94] - [ORM] 执行sql: select count(1) as count from project_approval WHERE creator  = ? 
2025-07-24 11:01:11.823 [http-nio-8080-exec-16] INFO  c.r.lowcode.orm.ORM - [selectPageToMap,94] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-24 11:01:11.831 [http-nio-8080-exec-17] INFO  c.r.lowcode.orm.ORM - [selectPageToMap,94] - [ORM] 执行sql: select count(1) as count from project_approval WHERE creator  = ? 
2025-07-24 11:28:53.246 [http-nio-8080-exec-36] INFO  c.r.lowcode.orm.ORM - [selectPageToMap,94] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-24 11:28:58.416 [http-nio-8080-exec-47] INFO  c.r.lowcode.orm.ORM - [selectPageToMap,94] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-24 11:29:02.380 [http-nio-8080-exec-57] INFO  c.r.lowcode.orm.ORM - [selectPageToMap,94] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-24 14:04:38.099 [lettuce-eventExecutorLoop-1-1] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was /*************:7379
2025-07-24 14:04:38.131 [lettuce-nioEventLoop-7-3] INFO  i.l.c.p.ReconnectionHandler - [lambda$null$3,174] - Reconnected to *************/<unresolved>:7379
2025-07-24 14:04:41.707 [schedule-pool-1] INFO  sys-user - [run,57] - [127.0.0.1]内网IP[admin][Success][登录成功]
2025-07-24 14:04:42.693 [http-nio-8080-exec-84] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countWaitingTasks(..)] countWaitingTasks 执行耗时: 12 ms
2025-07-24 14:04:42.715 [http-nio-8080-exec-84] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countFinishedTasks(..)] countFinishedTasks 执行耗时: 21 ms
2025-07-24 14:04:42.721 [http-nio-8080-exec-84] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countMyApplyTasks(..)] countMyApplyTasks 执行耗时: 6 ms
2025-07-24 14:04:42.725 [http-nio-8080-exec-84] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [WfRuntimeService.countCcTasks(..)] countCcTasks 执行耗时: 4 ms
2025-07-24 14:04:42.725 [http-nio-8080-exec-84] INFO  c.r.c.a.ExecutionTimeLoggerAspect - [logExecutionTime,35] - [PmsOrderServiceImpl.getApprovalStat(..)] getApprovalStat 执行耗时: 44 ms
2025-07-24 14:21:01.115 [http-nio-8080-exec-96] INFO  c.r.lowcode.orm.ORM - [selectPageToMap,94] - [ORM] 执行sql: select count(1) as count from soft_inventory
2025-07-24 14:21:01.115 [http-nio-8080-exec-97] INFO  c.r.lowcode.orm.ORM - [selectPageToMap,94] - [ORM] 执行sql: select count(1) as count from Trademark_inventory
2025-07-24 14:21:01.121 [http-nio-8080-exec-98] INFO  c.r.lowcode.orm.ORM - [selectPageToMap,94] - [ORM] 执行sql: select count(1) as count from patent_inventory
2025-07-24 14:21:26.873 [http-nio-8080-exec-7] INFO  c.r.lowcode.orm.ORM - [selectPageToMap,94] - [ORM] 执行sql: select count(1) as count from project_approval WHERE creator  = ? 
2025-07-24 14:24:05.050 [http-nio-8080-exec-43] INFO  c.r.lowcode.orm.ORM - [selectPageToMap,94] - [ORM] 执行sql: select count(1) as count from project_approval WHERE creator  = ? 
2025-07-24 14:29:39.836 [http-nio-8080-exec-50] INFO  c.r.lowcode.orm.ORM - [selectPageToMap,94] - [ORM] 执行sql: select count(1) as count from project_approval WHERE creator  = ? 
2025-07-24 14:29:47.893 [http-nio-8080-exec-56] INFO  c.r.lowcode.orm.ORM - [selectPageToMap,94] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-24 14:31:36.650 [http-nio-8080-exec-60] INFO  c.r.lowcode.orm.ORM - [selectPageToMap,94] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-24 14:31:38.193 [http-nio-8080-exec-62] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ? 
2025-07-24 14:31:38.217 [http-nio-8080-exec-62] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ? 
2025-07-24 14:31:41.590 [http-nio-8080-exec-63] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ? 
2025-07-24 14:31:41.605 [http-nio-8080-exec-63] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ? 
2025-07-24 14:31:41.978 [http-nio-8080-exec-64] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ? 
2025-07-24 14:31:41.990 [http-nio-8080-exec-64] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ? 
2025-07-24 14:31:42.365 [http-nio-8080-exec-65] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ? 
2025-07-24 14:31:42.382 [http-nio-8080-exec-65] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ? 
2025-07-24 14:32:18.332 [http-nio-8080-exec-68] INFO  c.r.lowcode.orm.ORM - [selectPageToMap,94] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-24 14:32:20.197 [http-nio-8080-exec-71] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ? 
2025-07-24 14:32:20.209 [http-nio-8080-exec-71] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ? 
2025-07-24 14:32:21.156 [http-nio-8080-exec-70] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ? 
2025-07-24 14:32:21.170 [http-nio-8080-exec-70] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ? 
2025-07-24 14:32:24.181 [http-nio-8080-exec-75] INFO  c.r.lowcode.orm.ORM - [selectPageToMap,94] - [ORM] 执行sql: select count(1) as count from project_approval WHERE creator  = ? 
2025-07-24 14:34:16.969 [http-nio-8080-exec-86] INFO  c.r.lowcode.orm.ORM - [selectPageToMap,94] - [ORM] 执行sql: select count(1) as count from project_approval WHERE creator  = ? 
2025-07-24 14:34:24.051 [http-nio-8080-exec-100] INFO  c.r.lowcode.orm.ORM - [selectPageToMap,94] - [ORM] 执行sql: select count(1) as count from project_approval WHERE creator  = ? 
2025-07-24 14:34:52.588 [http-nio-8080-exec-9] INFO  c.r.lowcode.orm.ORM - [selectPageToMap,94] - [ORM] 执行sql: select count(1) as count from project_approval WHERE creator  = ? 
2025-07-24 14:35:00.672 [http-nio-8080-exec-12] INFO  c.r.lowcode.orm.ORM - [selectPageToMap,94] - [ORM] 执行sql: select count(1) as count from project_approval WHERE creator  = ? 
2025-07-24 14:35:52.538 [http-nio-8080-exec-17] INFO  c.r.lowcode.orm.ORM - [selectPageToMap,94] - [ORM] 执行sql: select count(1) as count from project_approval WHERE creator  = ? 
2025-07-24 14:36:59.870 [http-nio-8080-exec-29] INFO  c.r.lowcode.orm.ORM - [selectPageToMap,94] - [ORM] 执行sql: select count(1) as count from project_approval WHERE creator  = ? 
2025-07-24 14:37:09.971 [http-nio-8080-exec-36] INFO  c.r.lowcode.orm.ORM - [selectPageToMap,94] - [ORM] 执行sql: select count(1) as count from project_approval WHERE creator  = ? 
2025-07-24 14:37:14.326 [http-nio-8080-exec-46] INFO  c.r.lowcode.orm.ORM - [selectPageToMap,94] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-24 14:37:15.393 [http-nio-8080-exec-43] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ? 
2025-07-24 14:37:15.408 [http-nio-8080-exec-43] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ? 
2025-07-24 14:37:16.098 [http-nio-8080-exec-47] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ? 
2025-07-24 14:37:16.114 [http-nio-8080-exec-47] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ? 
2025-07-24 14:37:16.480 [http-nio-8080-exec-48] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ? 
2025-07-24 14:37:16.496 [http-nio-8080-exec-48] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ? 
2025-07-24 14:37:18.548 [http-nio-8080-exec-49] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ? 
2025-07-24 14:37:18.560 [http-nio-8080-exec-49] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ? 
2025-07-24 14:37:21.707 [http-nio-8080-exec-51] INFO  c.r.lowcode.orm.ORM - [selectPageToMap,94] - [ORM] 执行sql: select count(1) as count from project_approval WHERE creator  = ? 
2025-07-24 14:37:54.683 [http-nio-8080-exec-59] INFO  c.r.lowcode.orm.ORM - [selectPageToMap,94] - [ORM] 执行sql: select count(1) as count from project_approval WHERE creator  = ? 
2025-07-24 14:39:16.616 [http-nio-8080-exec-65] INFO  c.r.lowcode.orm.ORM - [selectPageToMap,94] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-24 14:39:18.577 [http-nio-8080-exec-67] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ? 
2025-07-24 14:39:18.595 [http-nio-8080-exec-67] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ? 
2025-07-24 14:39:19.190 [http-nio-8080-exec-68] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ? 
2025-07-24 14:39:19.208 [http-nio-8080-exec-68] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ? 
2025-07-24 14:39:19.534 [http-nio-8080-exec-69] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ? 
2025-07-24 14:39:19.547 [http-nio-8080-exec-69] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ? 
2025-07-24 14:39:19.861 [http-nio-8080-exec-71] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ? 
2025-07-24 14:39:19.875 [http-nio-8080-exec-71] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ? 
2025-07-24 14:39:20.194 [http-nio-8080-exec-70] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ? 
2025-07-24 14:39:20.205 [http-nio-8080-exec-70] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ? 
2025-07-24 14:39:20.494 [http-nio-8080-exec-72] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ? 
2025-07-24 14:39:20.506 [http-nio-8080-exec-72] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ? 
2025-07-24 14:39:54.506 [http-nio-8080-exec-92] INFO  c.r.lowcode.orm.ORM - [selectPageToMap,94] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-24 14:39:57.702 [http-nio-8080-exec-93] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ? 
2025-07-24 14:39:57.715 [http-nio-8080-exec-93] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ? 
2025-07-24 14:39:58.415 [http-nio-8080-exec-95] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ? 
2025-07-24 14:39:58.429 [http-nio-8080-exec-95] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ? 
2025-07-24 14:39:59.222 [http-nio-8080-exec-91] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ? 
2025-07-24 14:39:59.235 [http-nio-8080-exec-91] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ? 
2025-07-24 14:39:59.934 [http-nio-8080-exec-98] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ? 
2025-07-24 14:39:59.949 [http-nio-8080-exec-98] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ? 
2025-07-24 14:40:00.804 [http-nio-8080-exec-99] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ? 
2025-07-24 14:40:00.819 [http-nio-8080-exec-99] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ? 
2025-07-24 14:40:01.173 [http-nio-8080-exec-97] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ? 
2025-07-24 14:40:01.187 [http-nio-8080-exec-97] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ? 
2025-07-24 14:40:01.559 [http-nio-8080-exec-96] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ? 
2025-07-24 14:40:01.572 [http-nio-8080-exec-96] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ? 
2025-07-24 14:40:02.034 [http-nio-8080-exec-1] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ? 
2025-07-24 14:40:02.048 [http-nio-8080-exec-1] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ? 
2025-07-24 14:40:04.863 [http-nio-8080-exec-4] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ? 
2025-07-24 14:40:04.876 [http-nio-8080-exec-4] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ? 
2025-07-24 14:40:05.405 [http-nio-8080-exec-100] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ? 
2025-07-24 14:40:05.415 [http-nio-8080-exec-100] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ? 
2025-07-24 14:40:06.197 [http-nio-8080-exec-5] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ? 
2025-07-24 14:40:06.221 [http-nio-8080-exec-5] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ? 
2025-07-24 14:40:06.655 [http-nio-8080-exec-2] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ? 
2025-07-24 14:40:06.766 [http-nio-8080-exec-2] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ? 
2025-07-24 14:40:07.116 [http-nio-8080-exec-3] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ? 
2025-07-24 14:40:07.129 [http-nio-8080-exec-3] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ? 
2025-07-24 14:40:07.562 [http-nio-8080-exec-8] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ? 
2025-07-24 14:40:07.577 [http-nio-8080-exec-8] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ? 
2025-07-24 14:40:08.227 [http-nio-8080-exec-6] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ? 
2025-07-24 14:40:08.252 [http-nio-8080-exec-6] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ? 
2025-07-24 15:39:58.695 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 15:39:58.697 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 15:39:58.697 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 15:39:58.697 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 15:39:58.697 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 15:39:58.697 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 15:39:58.697 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 15:39:58.697 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 15:39:58.697 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-24 15:39:59.046 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-24 15:39:59.046 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-24 15:39:59.046 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-24 15:39:59.061 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-24 15:39:59.105 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-24 15:39:59.107 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-24 15:40:03.700 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-24 15:40:03.713 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 97453 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-24 15:40:03.713 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-24 15:40:05.236 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-24 15:40:05.237 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-24 15:40:05.237 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-24 15:40:05.266 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-24 15:40:05.841 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-24 15:40:06.499 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-24 15:40:06.596 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for *************/*************:7379
2025-07-24 15:40:06.638 [redisson-netty-4-18] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for *************/*************:7379
2025-07-24 15:40:09.600 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-24 15:40:10.613 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-24 15:40:10.671 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-24 15:40:10.671 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 15:40:10.671 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 15:40:10.792 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 15:40:10.812 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-24 15:40:13.317 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-24 15:40:13.322 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-24 15:40:13.322 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-24 15:40:13.323 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-24 15:40:13.323 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-24 15:40:13.323 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-24 15:40:13.323 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-24 15:40:13.323 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@d303dc9
2025-07-24 15:40:14.684 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-24 15:40:14.710 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 15:40:14.824 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 15:40:14.824 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 15:40:14.855 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 15:40:14.855 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 15:40:20.556 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 15:40:20.556 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 15:40:20.575 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 15:40:20.575 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-24 15:40:20.598 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 17.033 seconds (process running for 18.191)
2025-07-24 15:40:20.700 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-24 15:40:20.780 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 76 ms to scan 26 urls, producing 296 keys and 1803 values
2025-07-24 15:40:20.792 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 1 ms to scan 2 urls, producing 13 keys and 34 values
2025-07-24 15:40:20.912 [RMI TCP Connection(2)-*************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-24 15:42:15.842 [http-nio-8080-exec-17] INFO  c.r.lowcode.orm.ORM - [selectPageToMap,94] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-24 15:42:18.100 [http-nio-8080-exec-19] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ? 
2025-07-24 15:42:18.120 [http-nio-8080-exec-19] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ? 
2025-07-24 15:42:19.209 [http-nio-8080-exec-20] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ? 
2025-07-24 15:42:19.219 [http-nio-8080-exec-20] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ? 
2025-07-24 15:42:24.001 [http-nio-8080-exec-21] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM xedasd WHERE id  in  (?)
2025-07-24 15:43:22.624 [http-nio-8080-exec-30] INFO  c.r.lowcode.orm.ORM - [selectPageToMap,94] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-24 15:43:23.752 [http-nio-8080-exec-32] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ? 
2025-07-24 15:43:23.765 [http-nio-8080-exec-32] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ? 
2025-07-24 15:43:24.514 [http-nio-8080-exec-33] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ? 
2025-07-24 15:43:24.530 [http-nio-8080-exec-33] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ? 
2025-07-24 15:49:55.447 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 15:49:55.450 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 15:49:55.450 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 15:49:55.450 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 15:49:55.451 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 15:49:55.451 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 15:49:55.451 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 15:49:55.451 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 15:49:55.451 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-24 15:49:55.594 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-24 15:49:55.594 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-24 15:49:55.595 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-24 15:49:55.602 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-24 15:49:55.620 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-24 15:49:55.639 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-24 15:49:58.982 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-24 15:49:58.992 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 97751 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-24 15:49:58.993 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-24 15:50:00.627 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-24 15:50:00.628 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-24 15:50:00.628 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-24 15:50:00.657 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-24 15:50:01.228 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-24 15:50:01.878 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-24 15:50:01.973 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for *************/*************:7379
2025-07-24 15:50:02.019 [redisson-netty-4-18] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for *************/*************:7379
2025-07-24 15:50:04.949 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-24 15:50:05.910 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-24 15:50:05.968 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-24 15:50:05.968 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 15:50:05.968 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 15:50:06.078 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 15:50:06.093 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-24 15:50:08.583 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-24 15:50:08.590 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-24 15:50:08.590 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-24 15:50:08.590 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-24 15:50:08.590 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-24 15:50:08.590 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-24 15:50:08.590 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-24 15:50:08.590 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@4d9ef371
2025-07-24 15:50:09.953 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-24 15:50:09.969 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 15:50:10.062 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 15:50:10.063 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 15:50:10.099 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 15:50:10.099 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 15:50:15.823 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 15:50:15.824 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 15:50:15.842 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 15:50:15.843 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-24 15:50:15.864 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 17.003 seconds (process running for 17.564)
2025-07-24 15:50:15.963 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-24 15:50:16.046 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 79 ms to scan 26 urls, producing 296 keys and 1803 values
2025-07-24 15:50:16.057 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 2 ms to scan 2 urls, producing 13 keys and 34 values
2025-07-24 15:50:16.450 [RMI TCP Connection(6)-*************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-24 15:58:13.182 [http-nio-8080-exec-1] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM xedasd WHERE id  in  (?)
2025-07-24 16:01:45.133 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 16:01:45.134 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 16:01:45.134 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 16:01:45.134 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 16:01:45.134 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 16:01:45.134 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 16:01:45.134 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 16:01:45.134 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 16:01:45.134 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-24 16:01:45.266 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-24 16:01:45.266 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-24 16:01:45.266 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-24 16:01:45.274 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-24 16:01:45.314 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-24 16:01:45.332 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-24 16:01:47.753 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-24 16:01:47.763 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 98102 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-24 16:01:47.763 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-24 16:01:49.301 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-24 16:01:49.302 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-24 16:01:49.302 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-24 16:01:49.331 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-24 16:01:49.893 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-24 16:01:50.547 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-24 16:01:50.643 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for *************/*************:7379
2025-07-24 16:01:50.683 [redisson-netty-4-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for *************/*************:7379
2025-07-24 16:01:53.740 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-24 16:01:54.784 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-24 16:01:54.850 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-24 16:01:54.850 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 16:01:54.851 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 16:01:54.951 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 16:01:54.966 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-24 16:01:56.978 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-24 16:01:56.983 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-24 16:01:56.983 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-24 16:01:56.983 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-24 16:01:56.983 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-24 16:01:56.984 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-24 16:01:56.984 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-24 16:01:56.984 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@1332d4ae
2025-07-24 16:01:58.289 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-24 16:01:58.297 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 16:01:58.387 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 16:01:58.387 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 16:01:58.420 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 16:01:58.420 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 16:02:03.947 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 16:02:03.948 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 16:02:03.965 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 16:02:03.965 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-24 16:02:03.987 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 16.361 seconds (process running for 16.919)
2025-07-24 16:02:04.101 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-24 16:02:04.188 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 81 ms to scan 26 urls, producing 296 keys and 1803 values
2025-07-24 16:02:04.199 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 2 ms to scan 2 urls, producing 13 keys and 34 values
2025-07-24 16:02:04.382 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-24 16:02:04.666 [http-nio-8080-exec-1] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM xedasd WHERE id  in  (?)
2025-07-24 16:02:04.680 [http-nio-8080-exec-1] INFO  c.r.lowcode.orm.ORM - [delete,248] - [ORM] 执行sql: DELETE FROM table_test_child WHERE xedasd_id  in  (?)
2025-07-24 16:02:49.894 [http-nio-8080-exec-10] INFO  c.r.lowcode.orm.ORM - [selectPageToMap,94] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-24 16:02:52.078 [http-nio-8080-exec-12] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM xedasd WHERE id  in  (?)
2025-07-24 16:02:52.090 [http-nio-8080-exec-12] INFO  c.r.lowcode.orm.ORM - [delete,248] - [ORM] 执行sql: DELETE FROM table_test_child WHERE xedasd_id  in  (?)
2025-07-24 16:05:52.353 [http-nio-8080-exec-13] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM xedasd WHERE id  in  (?)
2025-07-24 16:09:08.863 [http-nio-8080-exec-13] INFO  c.r.lowcode.orm.ORM - [delete,248] - [ORM] 执行sql: DELETE FROM table_test_child WHERE xedasd_id  in  (?)
2025-07-24 16:22:54.064 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 16:22:54.065 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 16:22:54.065 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 16:22:54.065 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 16:22:54.065 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 16:22:54.065 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 16:22:54.065 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 16:22:54.065 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 16:22:54.066 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-24 16:22:54.289 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-24 16:22:54.289 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-24 16:22:54.289 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-24 16:22:54.308 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-24 16:22:54.371 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-24 16:22:54.380 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-24 16:22:58.541 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-24 16:22:58.554 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 98967 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-24 16:22:58.555 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-24 16:23:00.592 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-24 16:23:00.593 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-24 16:23:00.593 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-24 16:23:00.631 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-24 16:23:02.855 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-24 16:23:03.557 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-24 16:23:03.665 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for *************/*************:7379
2025-07-24 16:23:03.706 [redisson-netty-4-18] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for *************/*************:7379
2025-07-24 16:23:06.661 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-24 16:23:07.630 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-24 16:23:07.687 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-24 16:23:07.687 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 16:23:07.687 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 16:23:07.802 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 16:23:07.834 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-24 16:23:10.261 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-24 16:23:10.266 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-24 16:23:10.266 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-24 16:23:10.267 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-24 16:23:10.267 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-24 16:23:10.267 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-24 16:23:10.267 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-24 16:23:10.267 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@66fd7b2
2025-07-24 16:23:12.082 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-24 16:23:12.099 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 16:23:12.242 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 16:23:12.242 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 16:23:12.314 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 16:23:12.315 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 16:23:14.163 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-24 16:23:15.109 [http-nio-8080-exec-9] INFO  c.r.lowcode.orm.ORM - [selectPageToMap,94] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-24 16:23:17.386 [http-nio-8080-exec-11] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM xedasd WHERE id  in  (?)
2025-07-24 16:23:17.411 [http-nio-8080-exec-11] INFO  c.r.lowcode.orm.ORM - [delete,248] - [ORM] 执行sql: DELETE FROM table_test_child WHERE xedasd_id  in  (?)
2025-07-24 16:23:22.511 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 16:23:22.513 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 16:23:22.544 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 16:23:22.545 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-24 16:23:22.588 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 24.209 seconds (process running for 25.372)
2025-07-24 16:23:22.682 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 78 ms to scan 26 urls, producing 296 keys and 1801 values
2025-07-24 16:23:22.708 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 1 ms to scan 2 urls, producing 13 keys and 34 values
2025-07-24 16:23:22.797 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-24 16:24:01.657 [http-nio-8080-exec-12] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM xedasd WHERE id  in  (?)
2025-07-24 16:24:10.854 [http-nio-8080-exec-12] INFO  c.r.lowcode.orm.ORM - [delete,248] - [ORM] 执行sql: DELETE FROM table_test_child WHERE xedasd_id  in  (?)
2025-07-24 16:27:06.694 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 16:27:06.695 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 16:27:06.696 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 16:27:06.696 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 16:27:06.696 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 16:27:06.696 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 16:27:06.696 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 16:27:06.696 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 16:27:06.696 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-24 16:27:06.841 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-24 16:27:06.841 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-24 16:27:06.841 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-24 16:27:06.853 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-24 16:27:06.873 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-24 16:27:06.895 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-24 16:27:09.197 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-24 16:27:09.208 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 99120 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-24 16:27:09.209 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-24 16:27:10.765 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-24 16:27:10.766 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-24 16:27:10.766 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-24 16:27:10.793 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-24 16:27:12.916 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-24 16:27:13.667 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-24 16:27:13.780 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for *************/*************:7379
2025-07-24 16:27:13.823 [redisson-netty-4-18] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for *************/*************:7379
2025-07-24 16:27:17.105 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-24 16:27:18.192 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-24 16:27:18.267 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-24 16:27:18.267 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 16:27:18.267 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 16:27:18.478 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 16:27:18.497 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-24 16:27:21.007 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-24 16:27:21.012 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-24 16:27:21.012 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-24 16:27:21.013 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-24 16:27:21.013 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-24 16:27:21.013 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-24 16:27:21.013 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-24 16:27:21.013 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@4a92652b
2025-07-24 16:27:22.353 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-24 16:27:22.362 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 16:27:22.457 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 16:27:22.458 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 16:27:22.490 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 16:27:22.490 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 16:27:28.084 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 16:27:28.084 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 16:27:28.103 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 16:27:28.104 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-24 16:27:28.122 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 19.051 seconds (process running for 19.617)
2025-07-24 16:27:28.234 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-24 16:27:28.322 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 82 ms to scan 26 urls, producing 296 keys and 1801 values
2025-07-24 16:27:28.331 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 1 ms to scan 2 urls, producing 13 keys and 34 values
2025-07-24 16:27:28.444 [RMI TCP Connection(4)-*************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-24 16:27:36.111 [http-nio-8080-exec-1] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM xedasd WHERE id  in  (?)
2025-07-24 16:27:36.126 [http-nio-8080-exec-1] INFO  c.r.lowcode.orm.ORM - [delete,248] - [ORM] 执行sql: DELETE FROM table_test_child WHERE xedasd_id  in  (?)
2025-07-24 16:27:36.130 [http-nio-8080-exec-1] INFO  c.r.lowcode.orm.ORM - [delete,248] - [ORM] 执行sql: DELETE FROM table_test_test_child WHERE xedasd_id  in  (?)
2025-07-24 16:27:36.237 [http-nio-8080-exec-2] INFO  c.r.lowcode.orm.ORM - [selectPageToMap,94] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-24 16:27:40.440 [http-nio-8080-exec-3] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ? 
2025-07-24 16:27:40.473 [http-nio-8080-exec-3] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ? 
2025-07-24 16:27:43.007 [http-nio-8080-exec-4] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_child WHERE xedasd_id  = ? 
2025-07-24 16:27:43.034 [http-nio-8080-exec-4] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM table_test_test_child WHERE xedasd_id  = ? 
2025-07-24 16:28:02.148 [http-nio-8080-exec-5] INFO  c.r.lowcode.orm.ORM - [selectListToMap,61] - [ORM] 执行sql: SELECT * FROM xedasd WHERE id  in  (?)
2025-07-24 16:28:02.158 [http-nio-8080-exec-5] INFO  c.r.lowcode.orm.ORM - [delete,248] - [ORM] 执行sql: DELETE FROM table_test_child WHERE xedasd_id  in  (?)
2025-07-24 16:28:02.162 [http-nio-8080-exec-5] INFO  c.r.lowcode.orm.ORM - [delete,248] - [ORM] 执行sql: DELETE FROM table_test_test_child WHERE xedasd_id  in  (?)
2025-07-24 16:28:02.222 [http-nio-8080-exec-6] INFO  c.r.lowcode.orm.ORM - [selectPageToMap,94] - [ORM] 执行sql: select count(1) as count from xedasd
2025-07-24 16:48:27.148 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 16:48:27.150 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 16:48:27.150 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 16:48:27.150 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 16:48:27.150 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 16:48:27.150 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 16:48:27.150 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 16:48:27.150 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 16:48:27.151 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-24 16:48:27.324 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-24 16:48:27.324 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-24 16:48:27.324 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-24 16:48:27.333 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-24 16:48:27.362 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-24 16:48:27.364 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-24 16:48:33.626 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-24 16:48:33.638 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 158 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-24 16:48:33.639 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-24 16:48:35.244 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-24 16:48:35.245 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-24 16:48:35.245 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-24 16:48:35.281 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-24 16:48:36.140 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-24 16:48:36.896 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-24 16:48:36.994 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for *************/*************:7379
2025-07-24 16:48:37.039 [redisson-netty-4-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for *************/*************:7379
2025-07-24 16:48:40.103 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-24 16:48:41.108 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-24 16:48:41.166 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-24 16:48:41.166 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 16:48:41.166 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 16:48:41.268 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 16:48:41.287 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-24 16:48:43.289 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-24 16:48:43.294 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-24 16:48:43.294 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-24 16:48:43.294 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-24 16:48:43.294 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-24 16:48:43.294 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-24 16:48:43.295 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-24 16:48:43.295 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@726c1889
2025-07-24 16:48:44.621 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-24 16:48:44.638 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 16:48:44.733 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 16:48:44.733 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 16:48:44.765 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 16:48:44.765 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 16:48:50.425 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 16:48:50.426 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 16:48:50.444 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 16:48:50.445 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-24 16:48:50.468 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 16.976 seconds (process running for 18.085)
2025-07-24 16:48:50.573 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-24 16:48:50.652 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 75 ms to scan 26 urls, producing 297 keys and 1803 values
2025-07-24 16:48:50.661 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 1 ms to scan 2 urls, producing 14 keys and 36 values
2025-07-24 16:48:50.751 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 16:48:50.751 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 16:48:50.751 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 16:48:50.751 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 16:48:50.751 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 16:48:50.751 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 16:48:50.751 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 16:48:50.751 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 16:48:50.751 [main] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-24 16:48:50.867 [main] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-24 16:48:50.867 [main] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-24 16:48:50.867 [main] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-24 16:48:50.873 [main] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-24 16:48:50.893 [main] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-24 16:48:50.913 [main] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-24 16:49:04.440 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-24 16:49:04.453 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 204 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-24 16:49:04.453 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-24 16:49:06.015 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-24 16:49:06.015 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-24 16:49:06.015 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-24 16:49:06.053 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-24 16:49:06.693 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-24 16:49:07.365 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-24 16:49:07.470 [redisson-netty-4-20] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for *************/*************:7379
2025-07-24 16:49:07.513 [redisson-netty-4-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for *************/*************:7379
2025-07-24 16:49:10.500 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-24 16:49:11.525 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-24 16:49:11.586 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-24 16:49:11.586 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 16:49:11.586 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 16:49:11.690 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 16:49:11.711 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-24 16:49:14.369 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-24 16:49:14.375 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-24 16:49:14.375 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-24 16:49:14.375 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-24 16:49:14.376 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-24 16:49:14.376 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-24 16:49:14.376 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-24 16:49:14.376 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@726bbe64
2025-07-24 16:49:16.020 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-24 16:49:16.030 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 16:49:16.127 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 16:49:16.127 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 16:49:16.160 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 16:49:16.160 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 16:49:22.087 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 16:49:22.089 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 16:49:22.104 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 16:49:22.105 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-24 16:49:22.127 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 17.849 seconds (process running for 18.787)
2025-07-24 16:49:22.251 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-24 16:49:22.380 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 124 ms to scan 26 urls, producing 297 keys and 1803 values
2025-07-24 16:49:22.391 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 1 ms to scan 2 urls, producing 14 keys and 36 values
2025-07-24 16:49:22.483 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 16:49:22.483 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 16:49:22.484 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 16:49:22.484 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 16:49:22.484 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 16:49:22.484 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 16:49:22.484 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 16:49:22.484 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 16:49:22.484 [main] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-24 16:49:22.601 [main] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-24 16:49:22.602 [main] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-24 16:49:22.602 [main] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-24 16:49:22.609 [main] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-24 16:49:22.628 [main] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-24 16:49:22.646 [main] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-24 16:49:47.326 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-24 16:49:47.337 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 235 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-24 16:49:47.338 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-24 16:49:48.867 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-24 16:49:48.868 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-24 16:49:48.868 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-24 16:49:48.898 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-24 16:49:49.467 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-24 16:49:50.119 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-24 16:49:50.214 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for *************/*************:7379
2025-07-24 16:49:50.261 [redisson-netty-4-17] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for *************/*************:7379
2025-07-24 16:49:53.279 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-24 16:49:54.248 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-24 16:49:54.304 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-24 16:49:54.305 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 16:49:54.305 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 16:49:54.413 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 16:49:54.429 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-24 16:49:56.387 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-24 16:49:56.392 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-24 16:49:56.392 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-24 16:49:56.392 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-24 16:49:56.393 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-24 16:49:56.393 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-24 16:49:56.393 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-24 16:49:56.393 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@251d3913
2025-07-24 16:49:57.755 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-24 16:49:57.765 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 16:49:57.871 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 16:49:57.874 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 16:49:57.912 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 16:49:57.912 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 16:50:03.779 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 16:50:03.780 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 16:50:03.794 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 16:50:03.795 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-24 16:50:03.809 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 16.615 seconds (process running for 17.234)
2025-07-24 16:50:03.915 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-24 16:50:03.977 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 57 ms to scan 26 urls, producing 297 keys and 1804 values
2025-07-24 16:50:03.985 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 1 ms to scan 2 urls, producing 14 keys and 37 values
2025-07-24 16:50:04.528 [RMI TCP Connection(3)-*************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-24 17:28:12.119 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 17:28:12.121 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 17:28:12.121 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 17:28:12.121 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 17:28:12.121 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 17:28:12.121 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 17:28:12.121 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,95] - Stopping...
2025-07-24 17:28:12.121 [SpringApplicationShutdownHook] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [stop,99] - Stopped.
2025-07-24 17:28:12.121 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-24 17:28:12.260 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-24 17:28:12.260 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-24 17:28:12.261 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-24 17:28:12.272 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-07-24 17:28:12.295 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
2025-07-24 17:28:12.296 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
2025-07-24 17:38:37.313 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
2025-07-24 17:38:37.325 [main] INFO  c.r.a.Application - [logStarting,50] - Starting Application using Java 21.0.5 with PID 7510 (/Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin/rcszh-admin/target/classes started by tutu in /Users/<USER>/Downloads/project/rong_cheng/recode-pms-admin/pms-admin)
2025-07-24 17:38:37.325 [main] INFO  c.r.a.Application - [logStartupProfileInfo,660] - The following 1 profile is active: "local"
2025-07-24 17:38:39.007 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-24 17:38:39.007 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-07-24 17:38:39.008 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-24 17:38:39.040 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-07-24 17:38:41.256 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
2025-07-24 17:38:42.146 [main] INFO  o.redisson.Version - [logVersion,41] - Redisson 3.17.1
2025-07-24 17:38:42.272 [redisson-netty-4-18] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for *************/*************:7379
2025-07-24 17:38:42.313 [redisson-netty-4-18] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for *************/*************:7379
2025-07-24 17:38:45.949 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process definitions were found for auto-deployment in the location `classpath*:**/processes/`
2025-07-24 17:38:47.005 [main] INFO  o.a.s.r.ResourceFinder - [discoverResources,49] - No process extensions were found for auto-deployment in the location 'classpath*:**/processes/'
2025-07-24 17:38:47.082 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1570] - Found 1 Process Engine Configurators in total:
2025-07-24 17:38:47.082 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1572] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 17:38:47.082 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1582] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 17:38:47.217 [main] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1589] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$SpringCGLIB$$0 (priority:10000)
2025-07-24 17:38:47.233 [main] INFO  o.a.e.i.ProcessEngineImpl - [<init>,78] - ProcessEngine default created
2025-07-24 17:38:49.452 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
2025-07-24 17:38:49.457 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-24 17:38:49.457 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
2025-07-24 17:38:49.458 [main] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
2025-07-24 17:38:49.458 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-24 17:38:49.458 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-24 17:38:49.458 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
2025-07-24 17:38:49.458 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@24546513
2025-07-24 17:38:51.098 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-24 17:38:51.108 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 17:38:51.203 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 17:38:51.203 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 17:38:51.236 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 17:38:51.237 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 17:38:56.458 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 17:38:56.459 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,81] - Starting...
2025-07-24 17:38:56.474 [main] INFO  o.a.s.AbstractActivitiSmartLifeCycle - [start,86] - Started.
2025-07-24 17:38:56.474 [main] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-24 17:38:56.490 [main] INFO  c.r.a.Application - [logStarted,56] - Started Application in 19.433 seconds (process running for 20.657)
2025-07-24 17:38:56.675 [main] INFO  c.r.c.r.CounterStartupRunner - [run,49] - 本地计数器预加载完成，数量：2
2025-07-24 17:38:56.827 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 147 ms to scan 26 urls, producing 297 keys and 1804 values
2025-07-24 17:38:56.839 [main] INFO  o.r.Reflections - [scan,219] - Reflections took 1 ms to scan 2 urls, producing 14 keys and 37 values
2025-07-24 17:38:57.073 [RMI TCP Connection(4)-*************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-24 19:21:07.865 [lettuce-eventExecutorLoop-1-1] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was /*************:7379
2025-07-24 19:21:07.879 [lettuce-nioEventLoop-7-3] INFO  i.l.c.p.ReconnectionHandler - [lambda$null$3,174] - Reconnected to *************/<unresolved>:7379
