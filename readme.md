
### 钉钉配置指南
1. 创建应用<研发项目工时系统>--》进入应用
2. 添加应用能力 --》网页应用
3. 网页应用--》配置首页地址
4. 权限管理--》配置权限（待确认 有些权限可能不需要）
   1. 个人权限
      1. 个人手机号信息 *
      2. 通讯录个人信息读权限 *
   2. 通讯录管理
      1. 邮箱等个人信息 *
      2. 成员信息读权限 *
   3. OA审批
### 钉钉配置指南
1. 创建应用<研发项目工时系统>--》进入应用
2. 添加应用能力 --》网页应用
3. 网页应用--》配置首页地址
4. 权限管理--》配置权限（待确认 有些权限可能不需要）
   1. 个人权限
      1. 个人手机号信息 *
      2. 通讯录个人信息读权限 *
   2. 通讯录管理
      1. 邮箱等个人信息 *
      2. 成员信息读权限 *
   3. OA审批
      1. 工作流实例写权限 *
      2. 工作流模板读权限 *
      3. 工作流实例读权限 *
   4. 考勤
      1. 调用企业API基础权限 *
      2. 考勤组查询权限 *
5. 事件订阅
   配置推送方式、加密 aes_key、签名 token、请求网址；开启审批事件（审批实例开始，结束）
6. 安全设置

   服务器出口IP：服务器公网IP

   重定向URL（回调域名）： https://{域名}

6. 发布版本: 修改完网页应用配置需发布版本

### 钉钉上线前数据初始化
1. 审批模板配置：pms_approval_template、pms_approval_template_control
2. 考勤字段配置：sys_checkin_column
3. 法定节假日拉取（跑定时器，注意改定时器参数）
4. 修改平台配置：pms_config 指定 dingTalk
      1. 工作流实例写权限 *
      2. 工作流模板读权限 *
      3. 工作流实例读权限 *
   4. 考勤
      1. 调用企业API基础权限 *
      2. 考勤组查询权限 *
5. 事件订阅
    配置推送方式、加密 aes_key、签名 token、请求网址；开启审批事件（审批实例开始，结束）
6. 安全设置

   服务器出口IP：服务器公网IP

   重定向URL（回调域名）： https://{域名}

6. 发布版本: 修改完网页应用配置需发布版本

### 钉钉上线前数据初始化
1. 审批模板配置：pms_approval_template、pms_approval_template_control
2. 考勤字段配置：sys_checkin_column
3. 法定节假日拉取（跑定时器，注意改定时器参数）
4. 修改平台配置：pms_config 指定 dingTalk