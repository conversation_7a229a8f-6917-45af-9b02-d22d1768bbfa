### 1.1.0版本更新日志

    产品优化迭代版本
    发布时间：2025-06-23

### 更新内容

    1. 新增密码规则设置
    2. 新增代理审批功能
    3. 支持项目经理项目主管等标题的设置(i18n)
    4. 新增配置中心，能够直接通过界面配置整个项目的配置信息`pms_config`
    5. 新增“是否启用组长”配置功能
    6. 新增“项目展示字段”和“项目任务展示”字段配置功能
    7. 替换redis缓存，支持local本地缓存,local和redis切换使用
    8. PmsConfig配置调整：删除了key: platform、loginPlatform、checkinPlatform、messagePlatform、projectUserApprove

### 注意事项

- 创建密码规则设置菜单
- 项目经理、项目主管 这2个词后续有使用的话，需要在i18n中添加；并使用MessageUtils.messageKey()方法获取对应的值
- 本项目默认采用local缓存,如果需要redis缓存，需要在配置文件里配置redis连接信息,rcszh-admin的pom.xml加入依赖

```
<dependency>
   <groupId>com.rcszh</groupId>
   <artifactId>rcszh-framework-redis</artifactId>
</dependency>
```
- 由于组长功能，所以会导致表单的很多流程都需要改变
1. 项目立项
2. 成员变更
3. 项目变更

- 流程中需要配置项目立项单中的保存项目成员的方法
```
projectMemberJavaDelegate.saveMembersV2(execution,成员字段,项目字段（这个项目是来自于新增项目后的节点Id）)
```
- 配置项目变更流程，改变其保存项目成员的方法
```
${projectMemberJavaDelegate.saveProjectAndMember(execution, "project_id")}
```
- 成员调入调出，需要变更流程执行方法：
```
- `${projectMemberJavaDelegate.updateMembersV2(execution, "change_list", "project_id")}
```

- 旧版本中成员是不包括项目经理这些内容的
目前新版本中已经把这些内容整合到pms_project_user中，后续需要调用接口`/pms/project/initProjectUser`将老数据整理修正
该方法会删除非成员，然后根据项目中的人员信息同步到成员中

### i18n功能使用说明

    1. 优先加载file:./i18n 以messages开头的文件；（如：在rcszh-admin.jar同级目录创建i18n目录，在其中创建messages.properties文件）
    2. 然后加载classpath:i18n中的messages开头的文件；
    3. 以先加载的为准；

### 如何升级到重构1.1.0版本

    1. 执行代理人需求的sql脚本 -- approval.sql (脚本中包含菜单创建)，上线后注意给对应角色添加“代理审批”权限
    2. 执行《数据库更新.sql》，补充配置中心的表结构
    3. 执行[upgrade.sql] 创建密码规则,计数器表结构
    4. 调用接口/pms/project/initProjectUser将老项目成员数据整理修正