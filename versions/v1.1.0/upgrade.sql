-- 升级脚本
-- 密码规则设置
CREATE TABLE `sys_password_rule_setting`
(
    `id`                           varchar(32) NOT NULL COMMENT '主键',
    `password_rule`                varchar(50) DEFAULT NULL COMMENT '密码规则设置',
    `password_min_length`          int(2) DEFAULT NULL COMMENT '密码最小长度',
    `password_interval_same`       int(2) DEFAULT NULL COMMENT '密码间隔多少次相同',
    `force_password_modify`        varchar(50) DEFAULT NULL COMMENT '强制修改密码时机',
    `password_expired_day`         int(10) DEFAULT NULL COMMENT '密码超期,密码有效期',
    `init_password_setting`        varchar(50) DEFAULT NULL COMMENT '初始密码设置',
    `send_init_password_email`     varchar(50) DEFAULT NULL COMMENT '为用户发送初始密码邮件时机',
    `password_error_count`         int(10) DEFAULT NULL COMMENT '输入密码错误次数',
    `account_lock_hour`            int(10) DEFAULT NULL COMMENT '锁定时间设置（小时）',
    `account_lock_minute`          int(255) DEFAULT NULL COMMENT '锁定时间设置（分钟）',
    `retrieve_password_settings`   varchar(50) DEFAULT NULL COMMENT '找回密码设置',
    `allow_multiple_account_login` varchar(1)  DEFAULT NULL COMMENT '同一个账号允许同时在多台机器上登录',
    `create_by`                    varchar(64) DEFAULT NULL COMMENT '创建者',
    `create_date`                  datetime    DEFAULT NULL COMMENT '创建时间',
    `last_update_by`               varchar(64) DEFAULT NULL COMMENT '更新者',
    `last_update_date`             datetime    DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='密码规则设置';

-- 缓存表
CREATE TABLE `persistent_counter`
(
    `id`            bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `counter_key`   varchar(100) NOT NULL COMMENT '缓存键',
    `counter_value` bigint(20) NOT NULL DEFAULT 0 COMMENT '缓存值',
    `expire_time`   datetime DEFAULT NULL COMMENT '缓存过期时间',
    `updated_time`  datetime DEFAULT current_timestamp() ON UPDATE current_timestamp () COMMENT '缓存更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='计数器表--记录自增';

ALTER TABLE `pms_project_user`
    ADD UNIQUE INDEX `project_user_uq_idx`(`project_id`, `user_id`) USING BTREE;