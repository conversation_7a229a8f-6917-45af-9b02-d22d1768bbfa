CREATE TABLE `wf_approval_agent`
(
    `id`          varchar(32) NOT NULL,
    `create_by`   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
    `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
    `update_by`   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
    `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
    `start_time`  datetime(0) NULL DEFAULT NULL COMMENT '开始时间',
    `end_time`    datetime(0) NULL DEFAULT NULL COMMENT '结束时间',
    `user_id`     bigint(20) NOT NULL COMMENT '被代理人',
    PRIMARY KEY (`id`),
    INDEX         `wf_approval_agent_user_id`(`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='审批代理人';

CREATE TABLE `wf_approval_agent_user`
(
    `id`                varchar(32) NOT NULL,
    `create_by`         varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
    `create_time`       datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
    `update_by`         varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
    `update_time`       datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
    `approval_agent_id` varchar(32) NOT NULL COMMENT '审批代理人ID',
    `agent_id`          bigint(20) NOT NULL COMMENT '代理人ID',
    PRIMARY KEY (`id`),
    INDEX               `wf_approval_agent_user_approval_agent_id`(`approval_agent_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='审批代理人明细';

CREATE TABLE `wf_approval_agent_type`
(
    `id`                varchar(32) NOT NULL,
    `create_by`         varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
    `create_time`       datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
    `update_by`         varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
    `update_time`       datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
    `approval_agent_id` varchar(32) NOT NULL COMMENT '审批代理人ID',
    `type`              varchar(32) not NULL COMMENT '代理流程类型',
    PRIMARY KEY (`id`),
    INDEX               `wf_approval_agent_type_approval_agent_id`(`approval_agent_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='审批代理人类型';

INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`,
                       `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`,
                       `update_by`, `update_time`, `remark`)
VALUES (null, '代理审批', 1, 12, 'proxyApproval', 'system/proxyApproval/index', NULL, 1, 0, 'C', '0', '0',
        'workflow:agent:list', 'server', 'admin', '2025-06-16 11:05:10', 'admin', '2025-06-16 11:06:50', '');

select @menu_id := menu_id
from `sys_menu`
where menu_name='代理审批' and menu_type='C';

INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`,
                       `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`,
                       `update_by`, `update_time`, `remark`)
VALUES (null, '代理审批详情', @menu_id, 1, 'proxyApproval', NULL, NULL, 1, 0, 'F', '0', '0', 'workflow:agent:query',
        '#', 'admin', '2025-06-23 14:42:29', '', NULL, '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`,
                       `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`,
                       `update_by`, `update_time`, `remark`)
VALUES (null, '代理审批新增', @menu_id, 2, 'proxyApproval', NULL, NULL, 1, 0, 'F', '0', '0', 'workflow:agent:add', '#',
        'admin', '2025-06-23 14:42:46', '', NULL, '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`,
                       `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`,
                       `update_by`, `update_time`, `remark`)
VALUES (null, '代理审批编辑', @menu_id, 3, 'proxyApproval', NULL, NULL, 1, 0, 'F', '0', '0', 'workflow:agent:edit', '#',
        'admin', '2025-06-23 14:43:01', '', NULL, '');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`,
                       `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`,
                       `update_by`, `update_time`, `remark`)
VALUES (null, '代理审批删除', @menu_id, 4, 'proxyApproval', NULL, NULL, 1, 0, 'F', '0', '0', 'workflow:agent:remove',
        '#', 'admin', '2025-06-23 14:43:15', '', NULL, '');
