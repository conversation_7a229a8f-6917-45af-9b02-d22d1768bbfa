CREATE TABLE `sys_read_log` (
                                `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键 ID',
                                `business_id` varchar(255) NOT NULL COMMENT '业务表 ID',
                                `business_type` varchar(255) NOT NULL COMMENT '业务类型',
                                `create_by` varchar(255) NOT NULL COMMENT '创建人',
                                `update_by` varchar(255) NOT NULL COMMENT '更新人',
                                `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='阅读日志表';