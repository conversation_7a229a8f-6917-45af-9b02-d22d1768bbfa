location /pms/api/sse/ {
        proxy_pass http://pms-jimu:8080/sse/;  # 根据实际后端路径调整

        # 关键配置：禁用代理缓冲和缓存
        proxy_buffering off;
        proxy_cache off;

        # 长连接超时设置（根据需求调整）
        proxy_read_timeout 20000s;  # 24小时
        proxy_send_timeout 20000s;

        # 保持连接活动
        proxy_http_version 1.1;
        proxy_set_header Connection '';

        # 标准代理头（与您的 API 配置一致）
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

        # 可选：SSE 特定头
        proxy_set_header Accept text/event-stream;
        }