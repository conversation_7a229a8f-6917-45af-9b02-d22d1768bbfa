CREATE TABLE `eqpt_equipment_category`
(
    `id`          varchar(32)  NOT NULL,
    `create_by`   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
    `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
    `update_by`   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
    `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
    `name`        varchar(64)  NOT NULL COMMENT '名称',
    `parent_id`   varchar(32)  NOT NULL COMMENT '父级ID',
    `ancestors`   varchar(255) NOT NULL COMMENT '祖级列表',
    `description` varchar(255) NULL COMMENT '描述',
    `sort`        int(11) default 0 NOT NULL COMMENT '排序',

    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='设备分类';

CREATE TABLE `eqpt_equipment`
(
    `id`            varchar(32) NOT NULL,
    `create_by`     varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
    `create_time`   datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
    `update_by`     varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
    `update_time`   datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
    `code`          varchar(32) NOT NULL COMMENT '编号',
    `name`          varchar(64) NOT NULL COMMENT '名称',
    `model`         varchar(64) NULL COMMENT '型号',
    `status`        varchar(32) NULL COMMENT '状态',
    `category_id`   varchar(32) NULL COMMENT '分类',
    `register_date` datetime(0) NULL DEFAULT NULL COMMENT '登记日期',
    `dept_id`       bigint(20) NULL DEFAULT NULL COMMENT '归属部门',
    PRIMARY KEY (`id`),
    INDEX `eqpt_equipment_category_id`(`category_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='设备';

CREATE TABLE `eqpt_equipment_share`
(
    `id`           varchar(32) NOT NULL,
    `create_by`    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
    `create_time`  datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
    `update_by`    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
    `update_time`  datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
    `equipment_id` varchar(32) NULL COMMENT '设备ID',
    `deleted`      varchar(32) NULL DEFAULT 'N' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    INDEX          `eqpt_equipment_share_equipment_id`(`equipment_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='设备分摊';

CREATE TABLE `eqpt_equipment_share_line`
(
    `id`                 varchar(32) NOT NULL,
    `create_by`          varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
    `create_time`        datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
    `update_by`          varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
    `update_time`        datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
    `equipment_share_id` varchar(32) NOT NULL COMMENT '公共人员ID',
    `equipment_id`       varchar(32) NOT NULL COMMENT '设备ID',
    `allocation_type`    varchar(32) NOT NULL COMMENT '分摊类型：全部、部分',
    `start_time`         datetime(0) NULL DEFAULT NULL COMMENT '开始时间',
    `end_time`           datetime(0) NULL DEFAULT NULL COMMENT '结束时间',

    PRIMARY KEY (`id`) USING BTREE,
    INDEX                `eqpt_equipment_share_line_equipment_share_id`(`equipment_share_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '设备分摊明细';

CREATE TABLE `eqpt_equipment_share_project`
(
    `id`                      varchar(32) NOT NULL,
    `create_by`               varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
    `create_time`             datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
    `update_by`               varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
    `update_time`             datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
    `equipment_share_line_id` varchar(32) NOT NULL COMMENT '设备分摊明细ID',
    `project_id`              bigint(20) NOT NULL COMMENT '项目ID',
    `ratio`                   decimal(10, 2) NOT NULL COMMENT '分摊比例',

    PRIMARY KEY (`id`) USING BTREE,
    INDEX                     `eqpt_equipment_share_project_idx`(`equipment_share_line_id`) USING BTREE,
    INDEX                     `eqpt_equipment_share_project_project_id`(`project_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '设备分摊项目';

CREATE TABLE `eqpt_equipment_work_record`
(
    `id`                    varchar(32) NOT NULL,
    `create_by`             varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
    `create_time`           datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
    `update_by`             varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
    `update_time`           datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
    `equipment_id`          varchar(32) NULL COMMENT '设备ID',
    `project_id`            bigint(20) NULL DEFAULT NULL COMMENT '项目ID',
    `work_time`             datetime(0) NULL DEFAULT NULL COMMENT '日期',
    `work_duration`         int(11) NULL DEFAULT NULL COMMENT '填报工时（分钟）',
    `work_duration_percent` decimal(13, 4) NULL DEFAULT NULL COMMENT '填报工时（百分比）',
    `regular_work_duration` int(11) NULL DEFAULT NULL COMMENT '运行时长（分钟）',
    `status`                varchar(32) NULL DEFAULT NULL COMMENT '状态',
    `type`                  varchar(32) NULL DEFAULT NULL COMMENT '类型',
    `remark`                text NULL DEFAULT NULL COMMENT '备注',

    PRIMARY KEY (`id`) USING BTREE,
    INDEX                   `eqpt_equipment_work_record_equipment_id`(`equipment_id`) USING BTREE,
    INDEX                   `eqpt_equipment_work_record_project_id`(`project_id`) USING BTREE,
    INDEX                   `eqpt_equipment_work_record_idx`(`project_id`, `equipment_id`, `work_time`) USING BTREE,
    INDEX                   `eqpt_equipment_work_record_work_time`(`work_time`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '设备工时记录' ROW_FORMAT = Dynamic;

-- 定时任务
INSERT INTO `sys_job`(`job_id`, `job_name`, `job_group`, `invoke_target`, `cron_expression`, `misfire_policy`,
                      `concurrent`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (null, '设备工时分摊', 'DEFAULT', 'equipmentWorkRecordTask.apportionEquipmentWorkRecord', '0 0 6 2 * ?', '1',
        '1', '0', 'admin', '2025-07-04 11:19:50', 'admin', '2025-07-10 14:52:44', '');

-- 菜单：设备工时
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`,
                       `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`,
                       `update_by`, `update_time`, `remark`, `view_list`)
VALUES (null, '设备管理', 0, 8, 'equip', NULL, NULL, 1, 0, 'M', '0', '0', '', 'monitor', 'admin', '2025-07-01 09:44:34',
        'admin', '2025-07-01 11:38:52', '', NULL);
select @menu_id := menu_id
from `sys_menu`
where menu_name='设备管理' and menu_type='M';

INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`,
                       `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`,
                       `update_by`, `update_time`, `remark`, `view_list`)
VALUES (null, '设备信息维护', @menu_id, 1, 'equipPm', 'equipment/equipPm/index', NULL, 1, 0, 'C', '0', '0',
        'eqpt:equipment:list', 'monitor', 'admin', '2025-07-01 09:45:38', 'admin', '2025-07-10 16:01:45', '', '[]');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`,
                       `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`,
                       `update_by`, `update_time`, `remark`, `view_list`)
VALUES (null, '设备工时登记', @menu_id, 2, 'equipWorktime', 'equipment/equipWorktime/index', NULL, 1, 0, 'C', '0', '0',
        'eqpt:equipmentRuntime:list', 'education', 'admin', '2025-07-01 11:38:29', 'admin', '2025-07-10 16:03:48', '',
        '[]');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`,
                       `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`,
                       `update_by`, `update_time`, `remark`, `view_list`)
VALUES (null, '设备工时分摊', @menu_id, 3, 'equipWorkTimeAllocate', 'equipment/equipWorkTimeAllocate/index', NULL, 1, 0,
        'C', '0', '0', 'eqpt:equipmentShare:list', 'dashboard', 'admin', '2025-07-01 17:49:19', 'admin',
        '2025-07-10 16:00:21', '', '[]');
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`,
                       `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`,
                       `update_by`, `update_time`, `remark`, `view_list`)
VALUES (null, '设备工时分摊明细', @menu_id, 4, 'equipWorkTimeAllocateLine', 'equipment/equipWorkTimeAllocate/line',
        NULL, 1, 0, 'C', '1', '0', 'eqpt:equipmentShareLine:list', 'druid', 'admin', '2025-07-02 09:34:59', 'admin',
        '2025-07-10 16:00:33', '', '[]');

select @menu_id1 := menu_id
from `sys_menu`
where menu_name='设备信息维护' and menu_type='C';

select @menu_id2 := menu_id
from `sys_menu`
where menu_name='设备工时登记' and menu_type='C';

select @menu_id3 := menu_id
from `sys_menu`
where menu_name='设备工时分摊' and menu_type='C';

INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`,
                       `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`,
                       `update_by`, `update_time`, `remark`, `view_list`)
VALUES (null, '设备信息详情', @menu_id1, 1, NULL, NULL, NULL, 1, 0, 'F', '0', '0', 'eqpt:equipment:query', '#', 'admin',
        '2025-07-10 16:02:11', '', NULL, '', NULL);
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`,
                       `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`,
                       `update_by`, `update_time`, `remark`, `view_list`)
VALUES (null, '新增设备', @menu_id1, 2, NULL, NULL, NULL, 1, 0, 'F', '0', '0', 'eqpt:equipment:add', '#', 'admin',
        '2025-07-10 16:02:22', '', NULL, '', NULL);
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`,
                       `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`,
                       `update_by`, `update_time`, `remark`, `view_list`)
VALUES (null, '修改设备', @menu_id1, 3, NULL, NULL, NULL, 1, 0, 'F', '0', '0', 'eqpt:equipment:edit', '#', 'admin',
        '2025-07-10 16:02:39', '', NULL, '', NULL);
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`,
                       `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`,
                       `update_by`, `update_time`, `remark`, `view_list`)
VALUES (null, '删除设备', @menu_id1, 4, NULL, NULL, NULL, 1, 0, 'F', '0', '0', 'eqpt:equipment:remove', '#', 'admin',
        '2025-07-10 16:02:52', '', NULL, '', NULL);
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`,
                       `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`,
                       `update_by`, `update_time`, `remark`, `view_list`)
VALUES (null, '批量修改设备状态', @menu_id1, 5, NULL, NULL, NULL, 1, 0, 'F', '0', '0', 'eqpt:equipment:updateStatus',
        '#', 'admin', '2025-07-10 16:03:18', 'admin', '2025-07-10 16:03:24', '', NULL);
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`,
                       `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`,
                       `update_by`, `update_time`, `remark`, `view_list`)
VALUES (null, '设备运行时长详细', @menu_id2, 1, NULL, NULL, NULL, 1, 0, 'F', '0', '0', 'eqpt:equipmentRuntime:query',
        '#', 'admin', '2025-07-10 16:04:07', '', NULL, '', NULL);
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`,
                       `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`,
                       `update_by`, `update_time`, `remark`, `view_list`)
VALUES (null, '新增设备运行时长', @menu_id2, 2, NULL, NULL, NULL, 1, 0, 'F', '0', '0', 'eqpt:equipmentRuntime:add', '#',
        'admin', '2025-07-10 16:04:26', '', NULL, '', NULL);
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`,
                       `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`,
                       `update_by`, `update_time`, `remark`, `view_list`)
VALUES (null, '删除设备运行时长', @menu_id2, 3, NULL, NULL, NULL, 1, 0, 'F', '0', '0', 'eqpt:equipmentRuntime:remove',
        '#', 'admin', '2025-07-10 16:04:38', '', NULL, '', NULL);
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`,
                       `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`,
                       `update_by`, `update_time`, `remark`, `view_list`)
VALUES (null, '新增设备分摊', @menu_id3, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'eqpt:equipmentShare:add', '#',
        'admin', '2025-07-10 15:59:13', '', NULL, '', NULL);
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`,
                       `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`,
                       `update_by`, `update_time`, `remark`, `view_list`)
VALUES (null, '删除设备分摊', @menu_id3, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'eqpt:equipmentShare:delete', '#',
        'admin', '2025-07-10 15:59:32', '', NULL, '', NULL);
INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`,
                       `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`,
                       `update_by`, `update_time`, `remark`, `view_list`)
VALUES (null, '配置设备分摊', @menu_id3, 3, '', NULL, NULL, 1, 0, 'F', '0', '0', 'eqpt:equipmentShare:config', '#',
        'admin', '2025-07-10 15:59:50', '', NULL, '', NULL);
