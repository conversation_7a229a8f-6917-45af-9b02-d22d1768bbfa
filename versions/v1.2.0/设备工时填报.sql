CREATE TABLE `eqpt_equipment_runtime` (
`id` varchar(32) NOT NULL,
`equipment_id` varchar(32) DEFAULT NULL COMMENT '设备id',
`work_date` datetime DEFAULT NULL COMMENT '工作日期',
`run_time` int(11) DEFAULT NULL COMMENT '运行时长',
`create_by` varchar(255) DEFAULT NULL,
`update_by` varchar(255) DEFAULT NULL,
`create_time` datetime DEFAULT NULL,
`update_time` datetime DEFAULT NULL,
PRIMARY KEY (`id`),
KEY `idx_work_date` (`work_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `eqpt_equipment_runtime_log` (
`id` bigint(20) NOT NULL,
`operator_id` bigint(20) DEFAULT NULL COMMENT '操作人',
`operator_content` longtext DEFAULT NULL COMMENT '操作内容',
`create_by` varchar(255) DEFAULT NULL,
`create_time` datetime DEFAULT NULL,
`update_by` varchar(255) DEFAULT NULL,
`update_time` datetime DEFAULT NULL,
PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


CREATE TABLE `eqpt_work_record` (
`id` bigint(20) NOT NULL AUTO_INCREMENT,
`create_by` varchar(64) DEFAULT '' COMMENT '创建者',
`create_time` datetime DEFAULT NULL COMMENT '创建时间',
`update_by` varchar(64) DEFAULT '' COMMENT '更新者',
`update_time` datetime DEFAULT NULL COMMENT '更新时间',
`user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
`project_id` bigint(20) DEFAULT NULL COMMENT '项目ID',
`work_time` datetime DEFAULT NULL COMMENT '日期',
`approver_id` bigint(20) DEFAULT NULL COMMENT '审批人',
`status` varchar(32) DEFAULT NULL COMMENT '状态',
`type` varchar(32) DEFAULT NULL COMMENT '类型',
`work_duration` int(11) DEFAULT NULL COMMENT '填报工时（分钟）',
`work_duration_percent` decimal(13,4) DEFAULT NULL COMMENT '填报工时（百分比）',
`max_work_duration` int(11) DEFAULT NULL COMMENT '最大工时（分钟）',
`regular_work_duration` int(11) DEFAULT NULL COMMENT '实际工时（分钟）',
`standard_work_duration` int(11) DEFAULT NULL COMMENT '标准工作时长（分钟）',
`remark` text DEFAULT NULL COMMENT '备注',
`is_init_data` varchar(32) DEFAULT 'N' COMMENT '是否初始化数据',
`work_log_id` bigint(20) DEFAULT NULL COMMENT '工作日志id',
PRIMARY KEY (`id`) USING BTREE,
KEY `sys_work_record_user_id` (`user_id`) USING BTREE,
KEY `sys_work_record_project_id` (`project_id`) USING BTREE,
KEY `sys_work_record_work_time` (`work_time`) USING BTREE,
KEY `sys_word_record_idx` (`project_id`,`user_id`,`work_time`) USING BTREE,
KEY `idx_type_time` (`type`,`work_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=8963 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='设备工时填报';