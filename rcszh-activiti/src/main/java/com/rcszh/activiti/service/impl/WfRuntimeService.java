package com.rcszh.activiti.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.rcszh.activiti.constant.VariableConstants;
import com.rcszh.activiti.domain.WfApplyOrg;
import com.rcszh.activiti.domain.WfTemplate;
import com.rcszh.activiti.domain.WfWorkflow;
import com.rcszh.activiti.domain.dto.*;
import com.rcszh.activiti.domain.vo.*;
import com.rcszh.activiti.enums.ActTaskStatus;
import com.rcszh.activiti.enums.ApproveAction;
import com.rcszh.activiti.enums.OwnerType;
import com.rcszh.activiti.enums.PublishStatus;
import com.rcszh.activiti.mapper.WfWorkflowMapper;
import com.rcszh.activiti.service.*;
import com.rcszh.activiti.util.ActFlowElementUtil;
import com.rcszh.base.common.core.domain.entity.SysDept;
import com.rcszh.base.common.core.domain.entity.SysUser;
import com.rcszh.base.common.core.page.TableDataInfo;
import com.rcszh.base.common.core.text.Convert;
import com.rcszh.base.common.exception.ServiceException;
import com.rcszh.base.common.utils.SecurityUtils;
import com.rcszh.base.common.utils.StringUtils;
import com.rcszh.base.common.utils.bean.BeanUtils;
import com.rcszh.base.common.utils.spring.SpringUtils;
import com.rcszh.basic.service.ISysCommonService;
import com.rcszh.cache.BaseDataTranslateUtils;
import com.rcszh.cache.CacheUtil;
import com.rcszh.cache.SimpleObjCacheDto;
import com.rcszh.common.annotation.LogExecutionTime;
import com.rcszh.common.constant.Constants;
import com.rcszh.common.util.PageUtil;
import com.rcszh.system.service.ISysDeptService;
import com.rcszh.system.service.ISysUserService;
import com.rcszh.util.SimpleObjCacheUtil;
import lombok.RequiredArgsConstructor;
import org.activiti.bpmn.model.*;
import org.activiti.engine.HistoryService;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.TaskService;
import org.activiti.engine.history.HistoricIdentityLink;
import org.activiti.engine.history.HistoricProcessInstance;
import org.activiti.engine.history.HistoricTaskInstance;
import org.activiti.engine.history.HistoricVariableInstance;
import org.activiti.engine.impl.identity.Authentication;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.task.IdentityLink;
import org.activiti.engine.task.Task;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.script.Bindings;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import java.io.Serializable;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class WfRuntimeService implements IWfRuntimeService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    private static final ThreadLocal<Set<String>> AUTO_COMPLETE_TASK = ThreadLocal.withInitial(HashSet::new);

    private final WfWorkflowMapper wfWorkflowMapper;

    private final IWfApplyOrgService wfApplyOrgService;

    private final IWfWorkflowService wfWorkflowService;

    private final IWfTemplateService wfTemplateService;

    private final ISysCommonService sysCommonService;

    private final ISysUserService sysUserService;

    private final ISysDeptService sysDeptService;

    private final IWfExtService wfExtService;

    private final RepositoryService repositoryService;

    private final TaskService taskService;

    private final RuntimeService runtimeService;

    private final HistoryService historyService;

    private final CacheUtil cacheUtil;

    private final IWfApprovalAgentService wfApprovalAgentService;

    private final BaseDataTranslateUtils baseDataTranslateUtils;

    private void handleElement(List<WfElementDto> wfElementDtos, FlowElement element) {
        if (element instanceof SequenceFlow) {
            return;
        }
        if (element instanceof ServiceTask) {
            return;
        }
        String elementId = element.getId();
        WfElementDto wfElementDto = new WfElementDto();
        wfElementDto.setElement(element);
        wfElementDto.setKey(elementId);
        wfElementDtos.add(wfElementDto);
        if (element instanceof UserTask) {
            Map<String, List<ExtensionAttribute>> attributesMap = element.getAttributes();
            Map<String, Object> nodeProperties = new HashMap<>();
            for (List<ExtensionAttribute> list : attributesMap.values()) {
                list.forEach(entity -> nodeProperties.put(entity.getName(), entity.getValue()));
            }
            if (Objects.nonNull(nodeProperties.get("approver"))) {
                wfElementDto.setApprover((String) nodeProperties.get("approver"));
            }
            if (Objects.nonNull(nodeProperties.get("applicantChoice"))) {
                wfElementDto.setApplicantChoice((String) nodeProperties.get("applicantChoice"));
            }
            if (Objects.nonNull(nodeProperties.get("userScope"))) {
                wfElementDto.setUserScope((String) nodeProperties.get("userScope"));
            }
            if (Objects.nonNull(nodeProperties.get("selectMethod"))) {
                wfElementDto.setSelectMethod((String) nodeProperties.get("selectMethod"));
            }
            // 扩展属性
            Map<String, Object> nodeExtensionProperties = ActFlowElementUtil.getNodeExtensionProperties(element.getExtensionElements());
            if (Objects.nonNull(nodeExtensionProperties.get("cc"))) {
                wfElementDto.setCcUser((String) nodeExtensionProperties.get("cc"));
            }
            return;
        }
        if (element instanceof EndEvent) {
            // 扩展属性
            Map<String, Object> nodeExtensionProperties = ActFlowElementUtil.getNodeExtensionProperties(element.getExtensionElements());
            if (Objects.nonNull(nodeExtensionProperties.get("cc"))) {
                wfElementDto.setCcUser((String) nodeExtensionProperties.get("cc"));
            }
        }
    }

    private List<WfElementDto> getDefinitionVariables(String actDefinitionId) {
        List<WfElementDto> wfElementDtos = new ArrayList<>();
        List<FlowElement> flowElements = (List<FlowElement>) repositoryService.getBpmnModel(actDefinitionId).getMainProcess().getFlowElements();
        for (FlowElement element : flowElements) {
            this.handleElement(wfElementDtos, element);

            if (element instanceof SubProcess) {
                for (FlowElement flowElement : ((SubProcess) element).getFlowElements()) {
                    this.handleElement(wfElementDtos, flowElement);
                }
            }
        }
        return wfElementDtos;
    }

    private void pushUserIdSet(Set<String> userIdSet, Object entity, String fieldName) {
        Object fieldValue = BeanUtil.getProperty(entity, fieldName);
        if (Objects.nonNull(fieldValue)) {
            if (fieldValue instanceof Long) {
                userIdSet.add(fieldValue.toString());
            } else {
                userIdSet.add(String.valueOf(fieldValue));
            }
        }

    }

    private Map<String, List<String>> findApprovers(List<WfElementDto> wfElementDtos, Map<String, Object> variables,
                                                    Map<String, List<String>> selfSelectVariables) {
        Map<String, List<String>> result = new HashMap<>();

        ScriptEngineManager factory = new ScriptEngineManager();
        ScriptEngine engine = factory.getEngineByName("juel");
        Bindings bindings = engine.createBindings();
        if (!CollectionUtils.isEmpty(variables)) {
            bindings.putAll(variables);
        }

        for (WfElementDto wfElementDto : wfElementDtos) {
            // 审批人处理：1、申请人自选  2、流程设置
            if (Constants.Y.equals(wfElementDto.getApplicantChoice())) {
                // 校验用户是否传递值
                String candidateUsersKey = wfElementDto.getKey() + "_candidateUsers";
                List<String> selfSelectUserIds = selfSelectVariables.get(candidateUsersKey);
                if (CollUtil.isEmpty(selfSelectUserIds)) {
//                    String message = StrUtil.format("节点[{}]的审批人未设置: ", wfElementDto.getElement().getName());
//                    throw new ServiceException(message);
//                    result.put(wfElementDto.getKey(), new ArrayList<>());
                    continue;
                }
                result.put(candidateUsersKey, selfSelectUserIds);
            } else if (StrUtil.isNotBlank(wfElementDto.getApprover())) {
                Set<String> userIdSet = new HashSet<>();
                JSONArray approverObjects = JSONUtil.parseArray(wfElementDto.getApprover());
                for (int i = 0; i < approverObjects.size(); i++) {
                    WfApproverDto approverDTO = JSONUtil.toBean(approverObjects.getJSONObject(i), WfApproverDto.class);
                    userIdSet.addAll(this.parseWfApproverDto(wfElementDto, approverDTO, variables, engine, bindings));
                }
//            if (userIdSet.isEmpty()) {
//                String message = "流程配置错误，审批节点[" + wfElementDto.getElement().getName() + "]找不到审批人";
//                throw new ServiceException(message);
//            }
                result.put(wfElementDto.getKey() + "_candidateUsers", new ArrayList<>(userIdSet));
            }
            // 抄送人处理
            if (StrUtil.isNotBlank(wfElementDto.getCcUser())) {
                Set<String> userIdSet = new HashSet<>();
                JSONArray approverObjects = JSONUtil.parseArray(wfElementDto.getCcUser());
                for (int i = 0; i < approverObjects.size(); i++) {
                    WfApproverDto approverDTO = JSONUtil.toBean(approverObjects.getJSONObject(i), WfApproverDto.class);
                    userIdSet.addAll(this.parseWfApproverDto(wfElementDto, approverDTO, variables, engine, bindings));
                }
                result.put(wfElementDto.getKey() + "_ccUsers", new ArrayList<>(userIdSet));
            }
        }
        return result;
    }

    private Set<String> parseWfApproverDto(WfElementDto wfElementDto, WfApproverDto approverDTO, Map<String, Object> variables,
                                           ScriptEngine engine, Bindings bindings) {
        Set<String> userIdSet = new HashSet<>();
        if (StrUtil.isNotBlank(approverDTO.getConditionExpression())) {
            try {
                if (!(Boolean) engine.eval(approverDTO.getConditionExpression(), bindings)) {
                    return userIdSet;
                }
            } catch (Exception e) {
                String message = StrUtil.format("流程配置错误，节点[{}] 表达式：{}， 错误: {}", wfElementDto.getElement().getName(), approverDTO.getConditionExpression(), e.getMessage());
                throw new ServiceException(message);
            }
        }
        if (CollUtil.isNotEmpty(approverDTO.getRoles())) {
            for (String role : approverDTO.getRoles()) {
                List<String> userIds = (List<String>) variables.get(role);
                if (CollUtil.isNotEmpty(userIds)) {
                    userIdSet.addAll(userIds);
                }
            }
        }
        if (CollUtil.isNotEmpty(approverDTO.getUserIds())) {
            userIdSet.addAll(approverDTO.getUserIds());
        }
        if (CollUtil.isNotEmpty(approverDTO.getRelatedUsers())) {
            for (WfApproverDto.RelatedUser relatedUser : approverDTO.getRelatedUsers()) {
                if (Objects.isNull(relatedUser.getType())) {
                    throw new RuntimeException(StrUtil.format("流程配置错误，[{}]关联人员的类型不能为空", wfElementDto.getElement().getName()));
                }
                if (Objects.isNull(relatedUser.getSourceType())) {
                    throw new RuntimeException(StrUtil.format("流程配置错误，[{}]关联人员的来源类型不能为空", wfElementDto.getElement().getName()));
                }
                if (Objects.isNull(relatedUser.getValue())) {
                    throw new RuntimeException(StrUtil.format("流程配置错误，[{}]关联人员的值不能为空", wfElementDto.getElement().getName()));
                }
                if ("variable".equals(relatedUser.getSourceType())) {
                    List<String> fieldPath = (List<String>) relatedUser.getValue();
                    String filedPath = String.join(".", fieldPath);
                    Long userId = Convert.toLong(BeanUtil.getProperty(variables, filedPath));
                    if (Objects.nonNull(userId)) {
                        SimpleObjCacheDto userCache = cacheUtil.findUserById(userId);
                        if (SimpleObjCacheUtil.isExist(userCache)) {
                            if ("leader".equals(relatedUser.getType())) {
                                String leader = findSupervisor(userCache.getObj(SysUser.class).getDeptId(), userId);
                                if (StrUtil.isNotBlank(leader)) {
                                    userIdSet.add(leader);
                                }
                            } else {
                                // 上级部门领导superiorDeptLeader
                                SysDept dept = userCache.getObj(SysUser.class).getDept();
                                if (Objects.nonNull(dept) && Objects.nonNull(dept.getParentId())) {
                                    SimpleObjCacheDto parentDept = cacheUtil.findDeptById(dept.getParentId());
                                    if (SimpleObjCacheUtil.isExist(parentDept) && StrUtil.isNotBlank(parentDept.getObj(SysDept.class).getLeader())) {
                                        userIdSet.add(parentDept.getObj(SysDept.class).getLeader());
                                    }
                                }
                            }
                        }
                    }

                } else {
                    // TODO 暂时不支持
                }
            }
        }
        if (CollUtil.isNotEmpty(approverDTO.getLeaderDeptIds())) {
            List<SysDept> sysDepts = sysDeptService.selectDeptByIds(approverDTO.getLeaderDeptIds());
            for (SysDept sysDept : sysDepts) {
                if (Objects.nonNull(sysDept.getLeader())) {
                    userIdSet.add(sysDept.getLeader());
                }
            }
        }
        if (Objects.nonNull(approverDTO.getProjectUser())) {
            WfApproverDto.ProjectUser projectUser = approverDTO.getProjectUser();
            if (CollUtil.isNotEmpty(projectUser.getProjectId())) {
                String filedPath = String.join(".", projectUser.getProjectId());
                Long projectId = Convert.toLong(BeanUtil.getProperty(variables, filedPath));
                if (Objects.nonNull(projectId)) {
                    Object bean = SpringUtils.getBean("pmsProjectExtServiceImpl");
                    try {
                        Method method = bean.getClass().getMethod("findById", Long.class);
                        Object invokeRes = method.invoke(bean, projectId);
                        if (Objects.nonNull(invokeRes)) {
                            if (projectUser.isProjectManagerId()) {
                                this.pushUserIdSet(userIdSet, invokeRes, "projectManagerId");
                            }
                            if (projectUser.isProjectSupervisorId()) {
                                this.pushUserIdSet(userIdSet, invokeRes, "projectSupervisorId");
                            }
                            if (projectUser.isProjectAssistantId()) {
                                this.pushUserIdSet(userIdSet, invokeRes, "projectAssistantId");
                            }
                            if (projectUser.isProjectAssistantId2()) {
                                this.pushUserIdSet(userIdSet, invokeRes, "projectAssistantId2");
                            }
                            if (projectUser.isProjectAssistantId3()) {
                                this.pushUserIdSet(userIdSet, invokeRes, "projectAssistantId3");
                            }
                            if (projectUser.isProjectAssistantId4()) {
                                this.pushUserIdSet(userIdSet, invokeRes, "projectAssistantId4");
                            }
                            if (projectUser.isProjectAssistantId5()) {
                                this.pushUserIdSet(userIdSet, invokeRes, "projectAssistantId5");
                            }
                            if (projectUser.isSalespersonId()) {
                                this.pushUserIdSet(userIdSet, invokeRes, "salespersonId");
                            }
                            if (projectUser.isProjectMember() || projectUser.isProjectMemberLeader()) {
                                Object pmsProjectUserServiceImpl = SpringUtils.getBean("pmsProjectUserServiceImpl");
                                Method findMethod = pmsProjectUserServiceImpl.getClass().getMethod("findProjectUserByProjectId", Long.class);
                                List<?> projectUsers = (List<?>) findMethod.invoke(pmsProjectUserServiceImpl, projectId);
                                List<String> userIds = projectUsers.stream().map(d -> Convert.toStr(BeanUtil.getProperty(d, "userId"))).toList();
                                if (projectUser.isProjectMember()) {
                                    userIdSet.addAll(userIds);
                                }
                                if (projectUser.isProjectMemberLeader()) {
                                    for (String userId : userIds) {
                                        Long longUserId = Convert.toLong(userId);
                                        SimpleObjCacheDto userCache = cacheUtil.findUserById(longUserId);
                                        if (SimpleObjCacheUtil.isExist(userCache)) {
                                            String leader = findSupervisor(userCache.getObj(SysUser.class).getDeptId(), longUserId);
                                            if (StrUtil.isNotBlank(leader)) {
                                                userIdSet.add(leader);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        String message = StrUtil.format("流程配置错误，[{}]项目人员--错误: {}", wfElementDto.getElement().getName(), e.getMessage());
                        throw new ServiceException(message);
                    }
                }

            }
        }

        if (Objects.nonNull(approverDTO.getDeptUser())) {
            WfApproverDto.DeptUser deptUser = approverDTO.getDeptUser();
            if (CollUtil.isNotEmpty(deptUser.getDeptIds())) {
                Set<Long> allDeptIds = new HashSet<>(deptUser.getDeptIds());
                if (Constants.Y.equals(deptUser.getIncludeSubDept())) {
                    Set<Long> childDeptIdSet = sysDeptService.selectAllChildrenDeptByIds(deptUser.getDeptIds());
                    allDeptIds.addAll(childDeptIdSet);
                }
                List<SysUser> sysUsers = sysUserService.findUserByDeptIds(new ArrayList<>(allDeptIds));
                for (SysUser sysUser : sysUsers) {
                    userIdSet.add(Convert.toStr(sysUser.getUserId()));
                }
            }
        }

        if (Objects.nonNull(approverDTO.getTaskUser())) {
            WfApproverDto.TaskUser taskUser = approverDTO.getTaskUser();
            if (CollUtil.isNotEmpty(taskUser.getTaskId())) {
                String filedPath = String.join(".", taskUser.getTaskId());
                Long taskId = Convert.toLong(BeanUtil.getProperty(variables, filedPath));
                if (Objects.nonNull(taskId)) {
                    Object bean = SpringUtils.getBean("pmsProjectTaskServiceImpl");
                    try {
                        Method method = bean.getClass().getMethod("getById", Serializable.class);
                        Object invokeRes = method.invoke(bean, taskId);
                        if (Objects.nonNull(invokeRes)) {
                            if (taskUser.isOwner()) {
                                this.pushUserIdSet(userIdSet, invokeRes, "owner");
                            }
                            Long parentId = BeanUtil.getProperty(invokeRes, "parentId");
                            if (taskUser.isParentOwner() && Objects.nonNull(parentId)) {
                                Object parent = method.invoke(bean, parentId);
                                this.pushUserIdSet(userIdSet, parent, "owner");
                            }
                        }
                    } catch (Exception e) {
                        String message = StrUtil.format("流程配置错误，[{}]任务人员--错误: {}", wfElementDto.getElement().getName(), e.getMessage());
                        throw new ServiceException(message);
                    }
                }

            }
        }

        if (CollUtil.isNotEmpty(approverDTO.getFormFields())) {
            for (List<String> formFieldList : approverDTO.getFormFields()) {
                String filedPath = String.join(".", formFieldList);
                String value = BeanUtil.getProperty(variables, filedPath);
                Object valueObj = JSONUtil.isTypeJSON(value) ? JSONUtil.parse(value) : value;
                if (valueObj instanceof List<?>) {
                    List<Object> vList = (List<Object>) valueObj;
                    for (Object v : vList) {
                        if (ObjectUtil.isEmpty(v)) {
                            continue;
                        }
                        if (v instanceof Long || v instanceof String) {
                            userIdSet.add(Convert.toStr(v));
                        } else {
                            // 项目成员特殊处理，取项目成员上级
                            Long userId = Convert.toLong(BeanUtil.getProperty(v, "userId"));
                            if (Objects.nonNull(userId)) {
                                SimpleObjCacheDto userCache = cacheUtil.findUserById(userId);
                                if (SimpleObjCacheUtil.isExist(userCache)) {
                                    String leader = findSupervisor(userCache.getObj(SysUser.class).getDeptId(), userId);
                                    if (StrUtil.isNotBlank(leader)) {
                                        userIdSet.add(leader);
                                    }
                                }
                            }
                        }
                    }
                } else {
                    if (StrUtil.isNotBlank(value)) {
                        if (value.contains(",")) {
                            userIdSet.addAll(Arrays.asList(value.split(",")));
                        } else {
                            userIdSet.add(value);
                        }
                    }
                }
            }
        }
        if (StrUtil.isNotBlank(approverDTO.getExpression())) {
            try {
                Object eval = engine.eval(approverDTO.getExpression(), bindings);
                if (Objects.nonNull(eval)) {
                    if (eval instanceof List list) {
                        userIdSet.addAll(list);
                    } else {
                        userIdSet.add((String) eval);
                    }

                }
            } catch (Exception e) {
                String message = StrUtil.format("流程配置错误，[{}]表达式：{} 错误：{}", wfElementDto.getElement().getName(), approverDTO.getExpression(), e.getMessage());
                throw new ServiceException(message);
            }
        }
        return userIdSet;
    }

    /**
     * 递归用户部门找上级的领导
     *
     * @param userId
     * @return
     */
    public String findSupervisor(Long deptId, Long userId) {
        SimpleObjCacheDto deptCache = cacheUtil.findDeptById(deptId);
        if (SimpleObjCacheUtil.isExist(deptCache)) {
            SysDept dept = deptCache.getObj(SysDept.class);
            String leader = dept.getLeader();
            if (StrUtil.isNotBlank(leader) && !StrUtil.equals(Convert.toStr(userId), leader)) {
                return leader;
            }
            if (Objects.nonNull(dept.getParentId())) {
                return findSupervisor(dept.getParentId(), userId);
            }
        }
        return null;
    }

    @Override
    public WfTemplate getCurrentFlowTemplate(String objDataType, boolean required) {
        List<WfApplyOrg> applyOrgList = wfApplyOrgService.list(new LambdaUpdateWrapper<WfApplyOrg>()
                .eq(WfApplyOrg::getOwnerType, OwnerType.WORKFLOW.getValue())
                .eq(WfApplyOrg::getObjDataType, objDataType)
        );
        if (CollUtil.isEmpty(applyOrgList)) {
            if (!required) {
                return null;
            }
            throw new ServiceException("找不到对应的流程");
        }
        List<String> workflowIds = applyOrgList.stream().map(WfApplyOrg::getOwnerId).collect(Collectors.toList());
        List<WfWorkflow> workflows = wfWorkflowService.list(new LambdaQueryWrapper<WfWorkflow>()
                .in(WfWorkflow::getWorkflowId, workflowIds).eq(WfWorkflow::getStatus, Constants.Y)
                .eq(WfWorkflow::getDeleted, Constants.N));
        if (CollUtil.isEmpty(workflows)) {
            if (!required) {
                return null;
            }
            throw new ServiceException("找不到对应的流程");
        }
        if (workflows.size() > 1) {
            if (!required) {
                return null;
            }
            throw new ServiceException("存在多个流程，请确认");
        }
        WfWorkflow workflow = workflows.get(0);
        WfTemplate template = wfTemplateService.getOne(new LambdaQueryWrapper<WfTemplate>()
                .eq(WfTemplate::getWorkflowId, workflow.getWorkflowId())
                .eq(WfTemplate::getPublishStatus, PublishStatus.PUBLISHED.getValue())
                .orderByDesc(WfTemplate::getVersion).last("limit 1"));
        if (Objects.isNull(template)) {
            if (!required) {
                return null;
            }
            throw new ServiceException("请确认是否部署流程");
        }
        return template;
    }

    /**
     * 更新流程节点变量
     * 注意：只更新未触发的节点
     */
    public void refreshFlowElementVariables(String processDefinitionId, String processInstanceId) {
        Map<String, Object> variables = runtimeService.getVariables(processInstanceId);
        List<HistoricTaskInstance> taskInstances = historyService.createHistoricTaskInstanceQuery().processInstanceId(processInstanceId).list();
        Set<String> nodeIdSet = taskInstances.stream().map(HistoricTaskInstance::getTaskDefinitionKey).collect(Collectors.toSet());
        List<WfElementDto> wfElementDtos = this.getDefinitionVariables(processDefinitionId);
        List<WfElementDto> undoElementDots = wfElementDtos.stream().filter(d -> !nodeIdSet.contains(d.getElement().getId())).toList();
        Map<String, List<String>> candidateUsers = this.findApprovers(undoElementDots, variables, new HashMap<>());
        variables.putAll(candidateUsers);
        runtimeService.setVariables(processInstanceId, variables);
    }

    @Override
    public String start(WfFlowStartDto startInstance) {
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceBusinessKey(startInstance.getBusinessKey()).singleResult();
        if (processInstance != null) {
            throw new ServiceException("该单据已发起流程");
        }

        WfTemplate template = this.getCurrentFlowTemplate(startInstance.getObjDataType(), true);
        // 单据的初始参数，TODO 通过task监听注入
        Map<String, Object> variables = startInstance.getVariables();

        List<WfElementDto> wfElementDtos = this.getDefinitionVariables(template.getActDefinitionId());
        Map<String, List<String>> candidateUsers = this.findApprovers(wfElementDtos, variables, startInstance.getSelfSelectVariables());
        variables.putAll(candidateUsers);
        // 指定发起人
        Authentication.setAuthenticatedUserId(String.valueOf(startInstance.getApplicantId()));
        processInstance = runtimeService.createProcessInstanceBuilder()
                .processDefinitionId(template.getActDefinitionId())
                .businessKey(startInstance.getBusinessKey())
                .name(startInstance.getTitle())
                .variables(variables).start();

        String operateId = Convert.toStr(startInstance.getApplicantId());
        // 1、审批人和发起人是同一个人，审批自动通过 2、审批人为空时，是否自动跳过
        this.autoCompleteAfterStart(processInstance.getProcessInstanceId(), processInstance.getProcessDefinitionId(), operateId);
        return processInstance.getProcessInstanceId();
    }

    private void autoCompleteAfterStart(String processInstanceId, String actDefinitionId, String operateId) {
        Map<String, Object> processExtensionProperties = wfExtService.getProcessExtensionProperties(actDefinitionId);
        this.autoComplete(processInstanceId, null, operateId, operateId, ApproveAction.agree, null, processExtensionProperties, true);
    }

    /**
     * 自动审批任务
     *
     * @param processInstanceId          流程实例ID
     * @param currTask                   当前任务，流程发起时为空
     * @param applicantUserId            申请人ID
     * @param operateId                  操作人ID
     * @param action                     skip: 自动跳过（目前只有这一种情况会自动跳过：审批人为空），agree：审批同意
     * @param opinion                    审批意见
     * @param processExtensionProperties 审批人和发起人是同一个人，审批自动通过
     * @param skipComplete               是否跳过完成任务，false: 先完成任务，再判断后续逻辑；true: 跳过完成任务，直接判断后续逻辑
     */
    private void autoComplete(String processInstanceId, Task currTask, String applicantUserId, String operateId, ApproveAction action, String opinion, Map<String, Object> processExtensionProperties, boolean skipComplete) {
        if (!skipComplete && Objects.nonNull(currTask)) {
            Map<String, Object> variables = new HashMap<>();
            variables.put(VariableConstants.OPINION, opinion);
            variables.put(VariableConstants.ACTION, action.name());
            if (action == ApproveAction.agree) {
                taskService.claim(currTask.getId(), operateId);
            }
            taskService.complete(currTask.getId(), variables, true);
        }
        // 以下逻辑是判断是否有以下情况
        // 1、审批人和发起人是同一个人，审批自动通过
        // 2、自动审批
        // 3、审批人为空时，是否自动跳过
        List<Task> nextTasks = taskService.createTaskQuery()
                .processInstanceId(processInstanceId)
                .list();
        // 没有下一个任务
        if (CollUtil.isEmpty(nextTasks)) {
            return;
        }
        Map<String, Map<String, Object>> nodePropertiesMap = new HashMap<>();
        Map<String, List<IdentityLink>> identityLinksMap = new HashMap<>();
        for (Task nextTask : nextTasks) {
            Map<String, Object> nextNodeProperties = nodePropertiesMap.get(nextTask.getTaskDefinitionKey());
            if (Objects.isNull(nextNodeProperties)) {
                nextNodeProperties = wfExtService.getNodeAllProperties(nextTask.getProcessDefinitionId(), nextTask.getTaskDefinitionKey());
                nodePropertiesMap.put(nextTask.getTaskDefinitionKey(), nextNodeProperties);
            }
            List<IdentityLink> identityLinks = taskService.getIdentityLinksForTask(nextTask.getId());
            identityLinksMap.put(nextTask.getId(), identityLinks);
            if (CollUtil.isEmpty(identityLinks)) {
                String emptyApproverType = (String) nextNodeProperties.get("emptyApproverType");
                if (StrUtil.equals(emptyApproverType, "autoSkip")) {
                    this.autoComplete(processInstanceId, nextTask, applicantUserId, operateId, ApproveAction.skip, "审批人为空，自动跳过", processExtensionProperties, false);
                    break;
                }
            }
        }
        String repeatApplicant = (String) processExtensionProperties.get("repeatApplicant");
        String repeatApprover = (String) processExtensionProperties.get("repeatApprover");
        // 有开启自动审批
        if (!"all".equals(repeatApprover) || Constants.Y.equals(repeatApplicant)) {
            // 当前节点已全部审批，并且存在下一个节点任务
            boolean nodeFinished = true;
            if (Objects.nonNull(currTask)) {
                wfExtService.checkNodeFinished(currTask.getProcessInstanceId(), currTask.getTaskDefinitionKey(), currTask.getId());
            }
            if (!nodeFinished) {
                return;
            }
            // TODO 好像不需要（nodeFinished好像能卡主逻辑）；会签时，有多个任务；第一个任务递归审批后，还会查待办任务，会有重复，所以用set去重
            Set<String> taskIdSet = AUTO_COMPLETE_TASK.get();
            for (Task nextTask : nextTasks) {
                if (taskIdSet.contains(nextTask.getId())) {
                    continue;
                }
                taskIdSet.add(nextTask.getId());
                // 判断是否有必须编辑的字段权限(可编辑、必填，但是没有值的字段)，如有：不自动跳过
                boolean hasEditField = false;
                Map<String, Object> nextNodeProperties = nodePropertiesMap.get(nextTask.getTaskDefinitionKey());
                if (Constants.Y.equals(nextNodeProperties.get("enabledDataPerm"))) {
                    String dataPerm = (String) nextNodeProperties.get("dataPerm");
                    if (Objects.nonNull(dataPerm)) {
                        Object currentRecord = taskService.getVariable(nextTask.getId(), "currentRecord");
                        List<Object> dataPermList = JSONUtil.parseArray(dataPerm);
                        for (Object d : dataPermList) {
                            WfDataPermItemVo dataPermItemVo = BeanUtil.toBean(d, WfDataPermItemVo.class);
                            Object value = BeanUtil.getProperty(currentRecord, dataPermItemVo.getFiledCode());
                            if (Objects.nonNull(dataPermItemVo.getEditable()) && dataPermItemVo.getEditable()
                                    && Objects.nonNull(dataPermItemVo.getRequired()) && dataPermItemVo.getRequired()
                                    && ObjectUtil.isEmpty(value)
                            ) {
                                hasEditField = true;
                                break;
                            }
                        }
                    }
                }
                if (hasEditField) {
                    continue;
                }

                List<IdentityLink> identityLinks = identityLinksMap.get(nextTask.getId());
                if (CollUtil.isEmpty(identityLinks)) {
                    continue;
                }
                List<String> userIds = identityLinks.stream().map(IdentityLink::getUserId).collect(Collectors.toList());

                List<HistoricTaskInstance> finishedTasks = historyService.createHistoricTaskInstanceQuery()
                        .processInstanceId(nextTask.getProcessInstanceId())
                        .taskAssigneeIds(userIds)
                        .finished()
                        .list();
                // 同一个任务多人审批的话，是抢占：只需要一个人审批，所以这里调用autoComplete后，直接break
                for (String userId : userIds) {
                    if (Constants.Y.equals(repeatApplicant) && applicantUserId.equals(userId)) {
                        this.autoComplete(processInstanceId, nextTask, applicantUserId, userId, ApproveAction.agree, "发起人与审批人是同一个人，自动跳过", processExtensionProperties, false);
                        break;
                    }
                    // 存在历史审批，以下逻辑才需求判断
                    if (CollUtil.isNotEmpty(finishedTasks)) {
                        Set<String> assignessIdSet = finishedTasks.stream().map(HistoricTaskInstance::getAssignee).collect(Collectors.toSet());
                        if (!assignessIdSet.contains(userId)) {
                            continue;
                        }
                        // 仅首个节点需审批，其余自动同意
                        if ("first".equals(repeatApprover)) {
                            this.autoComplete(processInstanceId, nextTask, applicantUserId, userId, ApproveAction.agree, "重复审批人，自动跳过", processExtensionProperties, false);
                            break;
                        }
                        // 仅连续审批时自动同意
                        if ("continue".equals(repeatApprover)) {
                            boolean breakFlag = false;
                            // 取上一个用户审批节点的定义
                            List<UserTask> prevUserTasks = wfExtService.findPrevUserTasks(nextTask.getProcessDefinitionId(), nextTask.getTaskDefinitionKey());
                            if (CollUtil.isNotEmpty(prevUserTasks)) {
                                // 上一个用户节点，存在当前人的审批记录；则判断为连续审批
                                Set<String> taskDefinitionKeySet = finishedTasks.stream().filter(d -> d.getAssignee().equals(userId)).map(HistoricTaskInstance::getTaskDefinitionKey).collect(Collectors.toSet());
                                for (UserTask prevUserTask : prevUserTasks) {
                                    if (taskDefinitionKeySet.contains(prevUserTask.getId())) {
                                        this.autoComplete(processInstanceId, nextTask, applicantUserId, userId, ApproveAction.agree, "重复审批人，自动跳过", processExtensionProperties, false);
                                        breakFlag = true;
                                        break;
                                    }
                                }
                            }
                            if (breakFlag) {
                                break;
                            }
                        }
                    }
                }
            }
        }
    }

    private List<String> getApproverIds(String operationId, String businessType) {
        List<Long> principals = wfApprovalAgentService.findApprovalAgentByUserIdAndType(Convert.toLong(operationId), businessType);
        List<String> approverIds = principals.stream().map(Convert::toStr).collect(Collectors.toList());
        approverIds.add(operationId);
        return approverIds;
    }

    @Override
    public void agree(WfFlowAgreeDto agreeDto) {
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceBusinessKey(agreeDto.getBusinessKey()).singleResult();
        if (processInstance == null) {
            throw new ServiceException("流程已完成或未启动，无法同意审批");
        }
        Map<String, Object> processExtensionProperties = wfExtService.getProcessExtensionProperties(processInstance.getProcessDefinitionId());
        // 动态变量，如果开启：每次审批时，更新后续节点（包含当前节点）的变量（审批人、抄送人等）
        String dynamicVariable = (String) processExtensionProperties.get("dynamicVariable");
        if (Constants.Y.equals(dynamicVariable)) {
            // 更新全局变量
            this.refreshFlowElementVariables(processInstance.getProcessDefinitionId(), processInstance.getProcessInstanceId());
        }

        Task currTask = this.getCurrTaskAndCheck(agreeDto.getActTaskId(), processInstance.getProcessInstanceId(), agreeDto.getOperateId());
        this.autoComplete(currTask.getProcessInstanceId(), currTask, processInstance.getStartUserId(), agreeDto.getOperateId(), ApproveAction.agree, agreeDto.getOpinion(), processExtensionProperties, false);
    }

    @NotNull
    private Task getCurrTaskAndCheck(String actTaskId, String processInstanceId, String operateId) {
        Task currTask = taskService.createTaskQuery()
                .processInstanceId(processInstanceId)
                .taskId(actTaskId)
                .singleResult();
        if (Objects.isNull(currTask)) {
            throw new ServiceException("审批任务已发生变化，请刷新后重试");
        }
        String formId = (String) taskService.getVariable(currTask.getId(), "formId");
        List<String> approverIds = this.getApproverIds(operateId, formId);
        List<IdentityLink> identityLinks = taskService.getIdentityLinksForTask(currTask.getId());
        if (!CollUtil.containsAny(identityLinks.stream().map(IdentityLink::getUserId).collect(Collectors.toList()), approverIds)) {
            throw new RuntimeException("审批任务已发生变化，没有审批权限，请刷新后重试");
        }
        return currTask;
    }

    @Override
    public void refuse(WfFlowRefuseDto refuseDto) {
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceBusinessKey(refuseDto.getBusinessKey()).singleResult();
        if (processInstance == null) {
            throw new ServiceException("流程已完成或未启动，无法退回");
        }
        Task currTask = this.getCurrTaskAndCheck(refuseDto.getActTaskId(), processInstance.getProcessInstanceId(), refuseDto.getOperateId());
        Map<String, Object> variables = new HashMap<>();
        variables.put(VariableConstants.ACTION, ApproveAction.refuse.name());
        variables.put(VariableConstants.OPINION, refuseDto.getOpinion());
        taskService.claim(currTask.getId(), refuseDto.getOperateId());
        wfExtService.completeFinished(currTask, variables);
    }

    @Override
    public void cancel(WfFlowCancelDto cancelDto) {
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceBusinessKey(cancelDto.getBusinessKey()).singleResult();
        if (processInstance == null) {
            throw new ServiceException("流程不存在或已完成");
        }
        if (!processInstance.getStartUserId().equals(cancelDto.getOperateId())) {
            throw new ServiceException("只有流程发起人才能撤回流程");
        }
        Map<String, Object> processProperties = this.getProcessProperties(processInstance.getProcessDefinitionId());
        // 配置了不允许撤回
        if (Objects.nonNull(processProperties.get("allowCancel")) && Constants.N.equals(processProperties.get("allowCancel"))) {
            throw new ServiceException("流程不允许撤回");
        }
        List<Task> currTasks = taskService.createTaskQuery()
                .processInstanceId(processInstance.getProcessInstanceId())
                .list();
        if (CollUtil.isEmpty(currTasks)) {
            throw new ServiceException("任务不存在或已完成");
        }
        for (int i = 0; i < currTasks.size(); i++) {
            Task currTask = currTasks.get(i);
            // 第一个任务记录处理人
            if (i == 0) {
                taskService.claim(currTask.getId(), cancelDto.getOperateId());
            }
            Map<String, Object> variables = new HashMap<>();
            variables.put(VariableConstants.ACTION, ApproveAction.cancel.name());
            variables.put(VariableConstants.OPINION, "申请人撤回");
            wfExtService.completeFinished(currTask, variables);
        }
    }

    private List<WfFlowHiTaskVo> findWorkflowTasks(String processDefinitionId, String processInstanceId) {
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);
        Collection<FlowElement> flowElements = bpmnModel.getMainProcess().getFlowElements();
        List<WfFlowHiTaskVo> result = new ArrayList<>();

        StartEvent startEvent = (StartEvent) flowElements.stream().filter(e -> e instanceof StartEvent).findFirst().orElse(null);
        if (Objects.isNull(startEvent)) {
            throw new RuntimeException("流程配置错误，未找到开始节点");
        }
        Map<String, Object> variables = runtimeService.getVariables(processInstanceId);
        FlowElement currElement = startEvent;

        while (!Objects.isNull(currElement)) {
            if (currElement instanceof SequenceFlow) {
                // 流向
                currElement = ((SequenceFlow) currElement).getTargetFlowElement();
                continue;
            } else if (currElement instanceof ExclusiveGateway) {
                // 网关分支节点
                currElement = this.getGatewayFlow((ExclusiveGateway) currElement, variables);
                continue;
            } else if (currElement instanceof FlowNode flowNode) {
                if (CollUtil.isEmpty(flowNode.getOutgoingFlows())) {
                    // 结束节点
                    break;
                }
                if (currElement instanceof UserTask userTask) {
                    // 用户任务节点
                    List<String> candidateUsers = userTask.getCandidateUsers();
                    if (Objects.nonNull(userTask.getLoopCharacteristics())) {
                        candidateUsers = Collections.singletonList(userTask.getLoopCharacteristics().getInputDataItem());
                    }
                    List<String> candidates = new ArrayList<>();
                    for (String variable : candidateUsers) {
                        if (variable.startsWith("${") && variable.endsWith("}")) {
                            String key = variable.substring(2, variable.length() - 1);
                            if (variables.get(key) instanceof String) {
                                String candidate = (String) variables.get(key);
                                candidates.addAll(Arrays.asList(candidate.split(",")));
                                continue;
                            }
                            if (variables.get(key) instanceof List) {
                                List<String> candidate = (List<String>) variables.get(key);
                                candidates.addAll(candidate);
                                continue;
                            }
                        } else {
                            candidates.add(variable);
                        }
                    }

                    WfFlowHiTaskVo flowHiTaskVo = new WfFlowHiTaskVo();
                    flowHiTaskVo.setName(currElement.getName());
                    flowHiTaskVo.setTaskDefinitionKey(currElement.getId());

                    List<WfTaskIdentityVo> taskIdentityVos = new ArrayList<>();
                    for (String candidate : candidates) {
                        WfTaskIdentityVo wfTaskIdentityVo = new WfTaskIdentityVo();
                        wfTaskIdentityVo.setUserId(candidate);
                        taskIdentityVos.add(wfTaskIdentityVo);
                    }
                    flowHiTaskVo.setIdentitys(taskIdentityVos);

                    result.add(flowHiTaskVo);
                }

                currElement = flowNode.getOutgoingFlows().get(0);
                continue;
            } else {
                logger.debug("未处理的节点类型：{}", currElement.getClass().getName());
            }
            currElement = null;
        }
        return result;
    }

    /**
     * TODO 后续如果需要推演完到达的流程，需要处理 dynamicVariable 为Y的情况(更新变量后再推演)
     */
    @Override
    public WfFlowHistoryVo findFlowHistory(String businessKey) {
        List<HistoricProcessInstance> historicProcessInstances = historyService.createHistoricProcessInstanceQuery()
                .processInstanceBusinessKey(businessKey)
                .list();
        if (CollUtil.isEmpty(historicProcessInstances)) {
            throw new ServiceException("流程不存在");
        }
        WfFlowHistoryVo result = new WfFlowHistoryVo();
        List<WfFlowNodeVo> wfFlowNodeVos = new ArrayList<>();
        result.setBusinessKey(businessKey);
        result.setFlowName(historicProcessInstances.getLast().getName());
        result.setNodes(wfFlowNodeVos);
        LinkedHashMap<String, WfFlowNodeVo> flowNodeVoMap = new LinkedHashMap<>();

        List<HistoricTaskInstance> taskInstances = historyService.createHistoricTaskInstanceQuery()
                .processInstanceBusinessKey(businessKey)
                .orderByTaskCreateTime()
                .asc()
                .list();

        Set<String> userIdSet = new HashSet<>();
        for (HistoricTaskInstance taskInstance : taskInstances) {
            String key = StrUtil.format("{}_{}", taskInstance.getProcessInstanceId(), taskInstance.getTaskDefinitionKey());
            WfFlowNodeVo wfFlowNodeVo = flowNodeVoMap.get(key);
            if (wfFlowNodeVo == null) {
                wfFlowNodeVo = new WfFlowNodeVo();
                wfFlowNodeVo.setName(taskInstance.getName());
                wfFlowNodeVo.setTasks(new ArrayList<>());
                flowNodeVoMap.put(key, wfFlowNodeVo);
                wfFlowNodeVos.add(wfFlowNodeVo);
            }
            List<WfFlowHiTaskVo> taskVos = wfFlowNodeVo.getTasks();
            WfFlowHiTaskVo wfFlowTaskVo = new WfFlowHiTaskVo();
            wfFlowTaskVo.setId(taskInstance.getId());
            wfFlowTaskVo.setTaskDefinitionKey(taskInstance.getTaskDefinitionKey());
            wfFlowTaskVo.setName(taskInstance.getName());
            wfFlowTaskVo.setStartTime(taskInstance.getStartTime());
            wfFlowTaskVo.setEndTime(taskInstance.getEndTime());
            taskVos.add(wfFlowTaskVo);

            if (Objects.isNull(taskInstance.getAssignee())) {
                List<HistoricIdentityLink> identityLinks = historyService.getHistoricIdentityLinksForTask(taskInstance.getId());
                if (CollUtil.isNotEmpty(identityLinks)) {
                    List<WfTaskIdentityVo> wfTaskIdentityVos = new ArrayList<>();
                    for (HistoricIdentityLink identityLink : identityLinks) {
                        WfTaskIdentityVo wfTaskIdentityVo = new WfTaskIdentityVo();
                        wfTaskIdentityVo.setUserId(identityLink.getUserId());
                        wfTaskIdentityVos.add(wfTaskIdentityVo);

                        userIdSet.add(identityLink.getUserId());
                    }
                    wfFlowTaskVo.setIdentitys(wfTaskIdentityVos);
                }
            } else {
                wfFlowTaskVo.setAssignee(taskInstance.getAssignee());

                userIdSet.add(taskInstance.getAssignee());
            }
            List<HistoricVariableInstance> variables = historyService.createHistoricVariableInstanceQuery().taskId(taskInstance.getId()).list();
            for (HistoricVariableInstance variable : variables) {
                if (variable.getVariableName().equals(VariableConstants.OPINION) && Objects.nonNull(variable.getValue())) {
                    wfFlowTaskVo.setOpinion(String.valueOf(variable.getValue()));
                }
                if (variable.getVariableName().equals(VariableConstants.ACTION) && Objects.nonNull(variable.getValue())) {
                    wfFlowTaskVo.setAction(String.valueOf(variable.getValue()));
                }
            }
        }
        HistoricTaskInstance lastInstance = taskInstances.getLast();
        if (Objects.isNull(lastInstance.getEndTime())) {
            // 补充未开始的节点信息
            List<WfFlowHiTaskVo> workflowTasks = this.findWorkflowTasks(lastInstance.getProcessDefinitionId(), lastInstance.getProcessInstanceId());
            boolean currFlag = false;
            for (WfFlowHiTaskVo workflowTask : workflowTasks) {
                if (StrUtil.equals(workflowTask.getTaskDefinitionKey(), lastInstance.getTaskDefinitionKey())) {
                    currFlag = true;
                    continue;
                }
                if (!currFlag) {
                    continue;
                }
                if (CollUtil.isNotEmpty(workflowTask.getIdentitys())) {
                    for (WfTaskIdentityVo identity : workflowTask.getIdentitys()) {
                        userIdSet.add(identity.getUserId());
                    }
                }
                String key = StrUtil.format("{}_{}", lastInstance.getProcessInstanceId(), workflowTask.getTaskDefinitionKey());
                WfFlowNodeVo wfFlowNodeVo = flowNodeVoMap.get(key);
                if (wfFlowNodeVo == null) {
                    wfFlowNodeVo = new WfFlowNodeVo();
                    wfFlowNodeVo.setName(workflowTask.getName());
                    wfFlowNodeVo.setTasks(new ArrayList<>());
                    flowNodeVoMap.put(key, wfFlowNodeVo);
                    wfFlowNodeVos.add(wfFlowNodeVo);
                }
                wfFlowNodeVo.getTasks().add(workflowTask);
            }
        }
        List<Long> userIds = userIdSet.stream().filter(StrUtil::isNotBlank).map(Long::valueOf).toList();
        // 补充用户名
        if (!userIds.isEmpty()) {
            List<SysUser> sysUsers = sysCommonService.findUserByIds(userIds);
            Map<String, SysUser> userMap = sysUsers.stream().collect(Collectors.toMap(u -> String.valueOf(u.getUserId()), t -> t, (t1, t2) -> t1));
            for (WfFlowNodeVo wfFlowNodeVo : wfFlowNodeVos) {
                for (WfFlowHiTaskVo task : wfFlowNodeVo.getTasks()) {
                    if (Objects.isNull(task.getAssignee())) {
                        if (CollUtil.isNotEmpty(task.getIdentitys())) {
                            for (WfTaskIdentityVo identity : task.getIdentitys()) {
                                SysUser sysUser = userMap.get(identity.getUserId());
                                if (Objects.nonNull(sysUser)) {
                                    identity.setUserName(sysUser.getNickName());
                                }
                            }
                        }
                    } else {
                        SysUser sysUser = userMap.get(task.getAssignee());
                        if (Objects.nonNull(sysUser)) {
                            task.setAssigneeName(sysUser.getNickName());
                        }
                    }
                }
            }
        }

        // 补充node的状态
        for (WfFlowNodeVo wfFlowNodeVo : wfFlowNodeVos) {
            // 没有taskId，是还未开始的任务
            boolean isWaiting = wfFlowNodeVo.getTasks().stream().anyMatch(t -> Objects.isNull(t.getId()));
            if (isWaiting) {
                wfFlowNodeVo.setStatus(ActTaskStatus.waiting.name());
                continue;
            }
            List<WfFlowHiTaskVo> approvedTasks = wfFlowNodeVo.getTasks().stream().filter(t -> Objects.nonNull(t.getAction())).toList();
            Date startTime = approvedTasks.stream().map(WfFlowHiTaskVo::getStartTime).min(Comparator.naturalOrder()).orElse(null);
            Date finishTime = approvedTasks.stream().map(WfFlowHiTaskVo::getEndTime).max(Comparator.naturalOrder()).orElse(null);
            wfFlowNodeVo.setStartTime(startTime);
            wfFlowNodeVo.setFinishTime(finishTime);
            // 没有审批记录（action有值），是审批中的任务
            if (CollUtil.isEmpty(approvedTasks)) {
                wfFlowNodeVo.setStatus(ActTaskStatus.running.name());
                continue;
            }
            List<WfFlowHiTaskVo> nonAgreeTasks = approvedTasks.stream().filter(t -> !ApproveAction.agree.name().equals(t.getAction())).toList();
            if (CollUtil.isEmpty(nonAgreeTasks)) {
                wfFlowNodeVo.setStatus(ActTaskStatus.agreed.name());
                continue;
            }
            for (WfFlowHiTaskVo nonAgreeTask : nonAgreeTasks) {
                if (ApproveAction.skip.name().equals(nonAgreeTask.getAction())) {
                    wfFlowNodeVo.setStatus(ActTaskStatus.skipped.name());
                    break;
                }
                if (ApproveAction.refuse.name().equals(nonAgreeTask.getAction())) {
                    wfFlowNodeVo.setStatus(ActTaskStatus.refused.name());
                    break;
                }
                if (ApproveAction.cancel.name().equals(nonAgreeTask.getAction())) {
                    wfFlowNodeVo.setStatus(ActTaskStatus.canceled.name());
                    break;
                }
            }
            // 任务排序
            wfFlowNodeVo.getTasks().sort((o1, o2) -> {
                if (o1.getEndTime() != null && o2.getEndTime() != null) {
                    return o1.getEndTime().compareTo(o2.getEndTime());
                }
                if (o1.getEndTime() != null) {
                    return 1;
                }
                if (o2.getEndTime() != null) {
                    return -1;
                }
                return 0;
            });
        }
        return result;
    }

    private List<WfFlowTaskVo> transformWaitingTasksVos(List<ActTaskDto> tasks) {
        List<WfFlowTaskVo> result = new ArrayList<>();
        if (CollUtil.isEmpty(tasks)) {
            return result;
        }
        Set<String> procInstIdSet = tasks.stream().map(ActTaskDto::getProcessInstanceId).collect(Collectors.toSet());
        List<ProcessInstance> processInstances = runtimeService.createProcessInstanceQuery().processInstanceIds(procInstIdSet).list();
        Map<String, ProcessInstance> processInstanceMap = processInstances.stream().collect(Collectors.toMap(ProcessInstance::getId, t -> t, (t1, t2) -> t1));
        Set<Long> userIdSet = new HashSet<>();
        for (ActTaskDto task : tasks) {
            WfFlowTaskVo waitingTaskVo = new WfFlowTaskVo();
            BeanUtils.copyProperties(task, waitingTaskVo);
            ProcessInstance processInstance = processInstanceMap.get(task.getProcessInstanceId());
            if (Objects.nonNull(processInstance)) {
                waitingTaskVo.setBusinessKey(processInstance.getBusinessKey());
                waitingTaskVo.setApplicationTime(processInstance.getStartTime());
                if (Objects.nonNull(processInstance.getStartUserId())) {
                    Long userId = Long.valueOf(processInstance.getStartUserId());
                    waitingTaskVo.setApplicantId(userId);
                    userIdSet.add(userId);
                }
            }
            result.add(waitingTaskVo);
        }
        if (!userIdSet.isEmpty()) {
            List<SysUser> sysUsers = sysCommonService.findUserByIds(userIdSet.stream().toList());
            Map<String, SysUser> userMap = sysUsers.stream().collect(Collectors.toMap(u -> String.valueOf(u.getUserId()), t -> t, (t1, t2) -> t1));
            for (WfFlowTaskVo taskVo : result) {
                SysUser sysUser = userMap.get(String.valueOf(taskVo.getApplicantId()));
                if (Objects.nonNull(sysUser)) {
                    taskVo.setApplicantName(sysUser.getNickName());
                }
            }
        }
        baseDataTranslateUtils.baseDataTranslate(result);
        return result;
    }

    @Override
    public TableDataInfo findWaitingTasks(WfFlowTaskQueryDto query) {
        List<ActTaskDto> waitingTasks = wfWorkflowMapper.findWaitingTasks(query);
        return PageUtil.getDataTable(waitingTasks, this.transformWaitingTasksVos(waitingTasks));
    }

    @LogExecutionTime("countWaitingTasks")
    @Override
    public Long countWaitingTasks(WfFlowTaskQueryDto query) {
        return wfWorkflowMapper.countWaitingTasks(query);
    }

    private List<WfFlowHiTaskVo> transformWfFlowHiTaskVos(List<ActHiTaskDto> tasks) {
        List<WfFlowHiTaskVo> result = new ArrayList<>();
        if (CollUtil.isEmpty(tasks)) {
            return result;
        }
        Set<String> procInstIdSet = tasks.stream().map(ActHiTaskDto::getProcessInstanceId).collect(Collectors.toSet());
        List<HistoricProcessInstance> processInstances = historyService.createHistoricProcessInstanceQuery().processInstanceIds(procInstIdSet).list();
        Map<String, HistoricProcessInstance> processInstanceMap = processInstances.stream().collect(Collectors.toMap(HistoricProcessInstance::getId, t -> t, (t1, t2) -> t1));
        Set<Long> userIdSet = new HashSet<>();
        for (ActHiTaskDto task : tasks) {
            WfFlowHiTaskVo flowHiTaskVo = new WfFlowHiTaskVo();
            BeanUtils.copyProperties(task, flowHiTaskVo);
            HistoricProcessInstance processInstance = processInstanceMap.get(task.getProcessInstanceId());
            if (Objects.nonNull(processInstance)) {
                flowHiTaskVo.setBusinessKey(processInstance.getBusinessKey());
                flowHiTaskVo.setApplicationTime(processInstance.getStartTime());
                if (Objects.nonNull(processInstance.getStartUserId())) {
                    Long userId = Long.valueOf(processInstance.getStartUserId());
                    flowHiTaskVo.setApplicantId(userId);
                    userIdSet.add(userId);
                }
            }
            result.add(flowHiTaskVo);
        }
        if (!userIdSet.isEmpty()) {
            List<SysUser> sysUsers = sysCommonService.findUserByIds(userIdSet.stream().toList());
            Map<String, SysUser> userMap = sysUsers.stream().collect(Collectors.toMap(u -> String.valueOf(u.getUserId()), t -> t, (t1, t2) -> t1));
            for (WfFlowHiTaskVo taskVo : result) {
                SysUser sysUser = userMap.get(String.valueOf(taskVo.getApplicantId()));
                if (Objects.nonNull(sysUser)) {
                    taskVo.setApplicantName(sysUser.getNickName());
                }
            }
        }
        return result;
    }


    @Override
    public TableDataInfo findFinishedTasks(WfFlowTaskQueryDto query) {
        List<ActHiTaskDto> finishedTasks = wfWorkflowMapper.findFinishedTasks(query);
        return PageUtil.getDataTable(finishedTasks, this.transformWfFlowHiTaskVos(finishedTasks));
    }

    @LogExecutionTime("countFinishedTasks")
    @Override
    public Long countFinishedTasks(WfFlowTaskQueryDto queryDto) {
        return wfWorkflowMapper.countFinishedTasks(queryDto);
    }

    private List<WfFlowTaskCcVo> transformWfFlowTaskCcVos(List<WfFlowTaskCcVo> historicProcessInstances) {
        List<WfFlowTaskCcVo> result = new ArrayList<>();
        if (CollUtil.isEmpty(historicProcessInstances)) {
            return result;
        }
        Set<Long> userIdSet = new HashSet<>();
        for (WfFlowTaskCcVo historicProcessInstance : historicProcessInstances) {
            Long userId = Long.valueOf(historicProcessInstance.getStartUserId());
            userIdSet.add(userId);
        }
        List<SysUser> sysUsers = sysCommonService.findUserByIds(userIdSet.stream().toList());
        Map<String, SysUser> userMap = sysUsers.stream().collect(Collectors.toMap(u -> String.valueOf(u.getUserId()), t -> t, (t1, t2) -> t1));

        for (HistoricProcessInstance historicProcessInstance : historicProcessInstances) {
            WfFlowTaskCcVo wfHiProcessInstanceVo = new WfFlowTaskCcVo();
            BeanUtils.copyProperties(historicProcessInstance, wfHiProcessInstanceVo);
            SysUser sysUser = userMap.get(historicProcessInstance.getStartUserId());
            if (Objects.nonNull(sysUser)) {
                wfHiProcessInstanceVo.setApplicantId(Long.valueOf(historicProcessInstance.getStartUserId()));
                wfHiProcessInstanceVo.setApplicantName(sysUser.getNickName());
            }
            result.add(wfHiProcessInstanceVo);
        }
        return result;
    }

    @Override
    public TableDataInfo findCcTasks(WfFlowTaskQueryDto query) {
        List<WfFlowTaskCcVo> taskCcs = wfWorkflowMapper.findCcTasks(query);
        return PageUtil.getDataTable(taskCcs, this.transformWfFlowTaskCcVos(taskCcs));
    }

    @LogExecutionTime("countCcTasks")
    @Override
    public Long countCcTasks(WfFlowTaskQueryDto queryDto) {
        return wfWorkflowMapper.countCcTasks(queryDto);
    }

    private List<WfHiProcessInstanceVo> transformWfMyApplyVos(List<HistoricProcessInstance> historicProcessInstances) {
        List<WfHiProcessInstanceVo> result = new ArrayList<>();
        if (CollUtil.isEmpty(historicProcessInstances)) {
            return result;
        }
        Set<Long> userIdSet = new HashSet<>();
        for (HistoricProcessInstance historicProcessInstance : historicProcessInstances) {
            Long userId = Long.valueOf(historicProcessInstance.getStartUserId());
            userIdSet.add(userId);
        }
        List<SysUser> sysUsers = sysCommonService.findUserByIds(userIdSet.stream().toList());
        Map<String, SysUser> userMap = sysUsers.stream().collect(Collectors.toMap(u -> String.valueOf(u.getUserId()), t -> t, (t1, t2) -> t1));

        for (HistoricProcessInstance historicProcessInstance : historicProcessInstances) {
            WfHiProcessInstanceVo wfHiProcessInstanceVo = new WfHiProcessInstanceVo();
            BeanUtils.copyProperties(historicProcessInstance, wfHiProcessInstanceVo);
            SysUser sysUser = userMap.get(historicProcessInstance.getStartUserId());
            if (Objects.nonNull(sysUser)) {
                wfHiProcessInstanceVo.setApplicantId(Long.valueOf(historicProcessInstance.getStartUserId()));
                wfHiProcessInstanceVo.setApplicantName(sysUser.getNickName());
            }
            result.add(wfHiProcessInstanceVo);
        }
        return result;
    }

    @Override
    public TableDataInfo findMyApplyTasks(WfFlowTaskQueryDto query) {
        List<HistoricProcessInstance> myApplyList = wfWorkflowMapper.findMyApply(query);
        return PageUtil.getDataTable(myApplyList, this.transformWfMyApplyVos(myApplyList));
    }

    @LogExecutionTime("countMyApplyTasks")
    @Override
    public Long countMyApplyTasks(WfFlowTaskQueryDto queryDto) {
        return wfWorkflowMapper.countMyApply(queryDto);
    }

    private List<WfFlowTaskVo> transformWfFlowTaskVos(List<Task> tasks) {
        List<WfFlowTaskVo> result = new ArrayList<>();
        if (CollUtil.isEmpty(tasks)) {
            return result;
        }
        Set<String> userIdSet = new HashSet<>();
        for (Task task : tasks) {
            WfFlowTaskVo wfFlowTaskVo = new WfFlowTaskVo();
            BeanUtils.copyProperties(task, wfFlowTaskVo);
            if (Objects.isNull(task.getAssignee())) {
                List<IdentityLink> identityLinks = taskService.getIdentityLinksForTask(task.getId());
                for (IdentityLink identityLink : identityLinks) {
                    WfTaskIdentityVo wfTaskIdentityVo = new WfTaskIdentityVo();
                    wfTaskIdentityVo.setUserId(identityLink.getUserId());
                    wfFlowTaskVo.setIdentitys(Collections.singletonList(wfTaskIdentityVo));

                    userIdSet.add(identityLink.getUserId());
                }
            } else {
                wfFlowTaskVo.setAssignee(task.getAssignee());

                userIdSet.add(task.getAssignee());
            }
            result.add(wfFlowTaskVo);
        }
        // 补充用户名
        if (!userIdSet.isEmpty()) {
            List<SysUser> sysUsers = sysCommonService.findUserByIds(userIdSet.stream().map(Long::valueOf).toList());
            Map<String, SysUser> userMap = sysUsers.stream().collect(Collectors.toMap(u -> String.valueOf(u.getUserId()), t -> t, (t1, t2) -> t1));
            for (WfFlowTaskVo wfFlowTaskVo : result) {
                if (Objects.isNull(wfFlowTaskVo.getAssignee())) {
                    if (CollUtil.isNotEmpty(wfFlowTaskVo.getIdentitys())) {
                        for (WfTaskIdentityVo identity : wfFlowTaskVo.getIdentitys()) {
                            SysUser sysUser = userMap.get(identity.getUserId());
                            if (Objects.nonNull(sysUser)) {
                                identity.setUserName(sysUser.getNickName());
                            }
                        }
                    }
                } else {
                    SysUser sysUser = userMap.get(wfFlowTaskVo.getAssignee());
                    if (Objects.nonNull(sysUser)) {
                        wfFlowTaskVo.setAssigneeName(sysUser.getNickName());
                    }
                }
            }
        }
        return result;
    }

    @Override
    public List<WfFlowTaskVo> findRunningByInstanceIds(List<String> processInstanceIds) {
        List<Task> tasks = taskService.createTaskQuery()
                .processInstanceIdIn(processInstanceIds)
                .list();
        return this.transformWfFlowTaskVos(tasks);
    }

    @Override
    public List<WfFlowTaskVo> findRunningByBusinessKeys(List<String> businessKeys) {
        if (CollUtil.isEmpty(businessKeys)) {
            return Collections.emptyList();
        }
        String inSql = businessKeys.stream().map(t -> StrUtil.format("'{}'", t)).collect(Collectors.joining(",", "(", ")"));
        List<Task> tasks = taskService.createNativeTaskQuery()
                .sql("select * from act_ru_task where business_key_ in " + inSql)
                .list();
        return this.transformWfFlowTaskVos(tasks);
    }

    @Override
    public WfApprovalPermDto getApprovalPermission(String businessKey, String actTaskId, String userId) {
        HistoricProcessInstance instance = this.getLatestActInstanceByBusinessKey(businessKey);
        if (instance == null) {
            throw new RuntimeException("业务id错误");
        }
        WfApprovalPermDto result = new WfApprovalPermDto();
        if (Objects.nonNull(actTaskId)) {
            Task currentTask = taskService.createTaskQuery().taskId(actTaskId).singleResult();
            if (Objects.nonNull(currentTask)) {
                String formId = (String) taskService.getVariable(actTaskId, "formId");
                // 被代理人ID列表 + 自己 = 审批人列表
                List<String> approverIds = this.getApproverIds(userId, formId);
                List<IdentityLink> identityLinks = taskService.getIdentityLinksForTask(actTaskId);
                for (IdentityLink identityLink : identityLinks) {
                    if (approverIds.contains(identityLink.getUserId())) {
                        result.setAgreePerm(true);
                        result.setRefusePerm(true);
                        break;
                    }
                }
            }
        }

        if (instance.getStartUserId().equals(userId) && Objects.isNull(instance.getEndTime())) {
            Map<String, Object> processProperties = this.getProcessProperties(instance.getProcessDefinitionId());
            // 配置了不允许撤回
            if (Objects.nonNull(processProperties.get("allowCancel")) && Constants.N.equals(processProperties.get("allowCancel"))) {
                result.setCancelPerm(false);
            } else {
                result.setCancelPerm(true);
            }
        }
        return result;
    }

    private WfHiProcessInstanceVo buildWfHiProcessInstanceVo(HistoricProcessInstance historicProcessInstance) {
        if (historicProcessInstance == null) {
            return null;
        }
//        List<HistoricVariableInstance> variables = historyService.createHistoricVariableInstanceQuery().processInstanceId(historicProcessInstance.getId()).list();
//        HashSet<String> actionSet = new HashSet<>();
//        for (HistoricVariableInstance variable : variables) {
//            if (variable.getVariableName().equals(VariableConstants.ACTION) && Objects.nonNull(variable.getValue())) {
//                actionSet.add(variable.getValue().toString());
//            }
//        }
        WfHiProcessInstanceVo wfHiProcessInstanceVo = new WfHiProcessInstanceVo();
        BeanUtils.copyProperties(historicProcessInstance, wfHiProcessInstanceVo);
//        wfHiProcessInstanceVo.setId(historicProcessInstance.getId());
//        wfHiProcessInstanceVo.setBusinessKey(historicProcessInstance.getBusinessKey());
//        wfHiProcessInstanceVo.setStartTime(historicProcessInstance.getStartTime());
//        wfHiProcessInstanceVo.setEndTime(historicProcessInstance.getEndTime());

        return wfHiProcessInstanceVo;
    }

    private HistoricProcessInstance getLatestActInstanceByBusinessKey(String orderNo) {
        List<HistoricProcessInstance> historicProcessInstances = historyService.createHistoricProcessInstanceQuery()
                .processInstanceBusinessKey(orderNo)
                .orderByProcessInstanceStartTime()
                .desc()
                .listPage(0, 1);
        if (CollUtil.isEmpty(historicProcessInstances)) {
            return null;
        }
        return historicProcessInstances.get(0);
    }

    @Override
    public WfHiProcessInstanceVo getLatestInstanceByBusinessKey(String orderNo) {
        HistoricProcessInstance historicProcessInstance = this.getLatestActInstanceByBusinessKey(orderNo);
        return this.buildWfHiProcessInstanceVo(historicProcessInstance);
    }

    @Override
    public List<WfHiProcessInstanceVo> findLatestInstanceByBusinessKeys(List<String> businessKeys) {
        return businessKeys.stream().map(this::getLatestInstanceByBusinessKey).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public WfHiProcessInstanceVo getInstanceById(String processInstanceId) {
        HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
        return this.buildWfHiProcessInstanceVo(historicProcessInstance);
    }

    @Override
    public Map<String, Object> getProcessProperties(String processDefinitionId) {
        return wfExtService.getProcessExtensionProperties(processDefinitionId);
    }

    @Override
    public String getObjDataTypeByBusinessBy(String businessKey) {
        HistoricProcessInstance historicProcessInstance = this.getLatestActInstanceByBusinessKey(businessKey);
        if (historicProcessInstance == null) {
            return null;
        }
        WfTemplate wfTemplate = wfTemplateService.getOne(new LambdaQueryWrapper<WfTemplate>()
                .eq(WfTemplate::getActDefinitionId, historicProcessInstance.getProcessDefinitionId()));

        WfApplyOrg wfApplyOrg = wfApplyOrgService.getOne(new LambdaQueryWrapper<WfApplyOrg>()
                .eq(WfApplyOrg::getOwnerId, wfTemplate.getTemplateId())
                .eq(WfApplyOrg::getOwnerType, OwnerType.TEMPLATE.getValue())
        );
        return wfApplyOrg.getObjDataType();
    }

    @Override
    public List<String> getApproverIdsByBusinessKey(String businessKey) {
        List<Task> tasks = taskService.createTaskQuery()
                .processInstanceBusinessKey(businessKey)
                .list();
        if (CollUtil.isEmpty(tasks)) {
            return new ArrayList<>();
        }
        Set<String> userIdSet = new HashSet<>();
        for (Task task : tasks) {
            if (Objects.isNull(task.getAssignee())) {
                List<IdentityLink> identityLinks = taskService.getIdentityLinksForTask(task.getId());
                for (IdentityLink identityLink : identityLinks) {
                    userIdSet.add(identityLink.getUserId());
                }
            } else {
                userIdSet.add(task.getAssignee());
            }
        }
        return new ArrayList<>(userIdSet);
    }

    @Override
    public void deleteProcessInstanceByBusinessKeys(List<String> rowIds) {
        for (String rowId : rowIds) {
            List<HistoricProcessInstance> processInstances = historyService.createHistoricProcessInstanceQuery().processInstanceBusinessKey(rowId).list();
            for (HistoricProcessInstance processInstance : processInstances) {
                historyService.deleteHistoricProcessInstance(processInstance.getId());
            }
        }
    }

    @Override
    public void deleteInvalidProcessInstance(String actDefinitionId, List<String> rowIds) {
        String sql = StrUtil.format("select * from act_hi_procinst where proc_def_id_ = '{}'", actDefinitionId);
        if (CollUtil.isNotEmpty(rowIds)) {
            sql = sql + " and business_key_ not in (" + rowIds.stream().map(t -> StrUtil.format("'{}'", t)).collect(Collectors.joining(",")) + ")";
        }
        List<HistoricProcessInstance> processInstances = historyService.createNativeHistoricProcessInstanceQuery()
                .sql(sql)
                .list();
        for (HistoricProcessInstance processInstance : processInstances) {
            if (processInstance.getEndTime() == null) {
                runtimeService.deleteProcessInstance(processInstance.getId(), "删除无效流程");
            }
            historyService.deleteHistoricProcessInstance(processInstance.getId());
        }
    }

    @Override
    public List<Map<String, Object>> getPendingApprovals(String[] formIds) {
        List<Map<String, Object>> pendingApprovals = wfWorkflowMapper.getPendingApprovals(formIds, SecurityUtils.getUserId());
        return pendingApprovals;
    }

    @Override
    public Map<String, Object> getCurrentFlowNodePropertiesByUserId(String processInstanceId, String userId) {
        Task task = taskService.createTaskQuery().processInstanceId(processInstanceId).taskCandidateUser(userId).singleResult();
        if (task == null) {
            return new HashMap<>();
        }
        BpmnModel bpmnModel = repositoryService.getBpmnModel(task.getProcessDefinitionId());
        FlowElement flowElement = bpmnModel.getMainProcess().getFlowElement(task.getTaskDefinitionKey());
        return ActFlowElementUtil.getNodeAllProperties(flowElement);
    }

    private FlowElement getGatewayFlow(ExclusiveGateway exclusiveGateway, Map<String, Object> variables) {
        SequenceFlow outgoingSequenceFlow = null;
        SequenceFlow defaultSequenceFlow = null;
        String defaultSequenceFlowId = exclusiveGateway.getDefaultFlow();

        ScriptEngineManager factory = new ScriptEngineManager();
        ScriptEngine engine = factory.getEngineByName("juel");
        Bindings bindings = engine.createBindings();
        if (!CollectionUtils.isEmpty(variables)) {
            bindings.putAll(variables);
        }

        Iterator<SequenceFlow> sequenceFlowIterator = exclusiveGateway.getOutgoingFlows().iterator();
        while (outgoingSequenceFlow == null && sequenceFlowIterator.hasNext()) {
            SequenceFlow sequenceFlow = sequenceFlowIterator.next();

            // 默认分支
            if (defaultSequenceFlowId != null && defaultSequenceFlowId.equals(sequenceFlow.getId())) {
                defaultSequenceFlow = sequenceFlow;
            }

            // 没有条件的跳过
            if (StringUtils.isEmpty(sequenceFlow.getConditionExpression())) {
                continue;
            }

            try {
                // 判断条件
                if ((Boolean) engine.eval(sequenceFlow.getConditionExpression(), bindings)) {
                    outgoingSequenceFlow = sequenceFlow;
                }
            } catch (Exception e) {
                String message = "流程配置错误，表达式：" + sequenceFlow.getConditionExpression() + " 错误: " + e.getMessage();
//                throw new ServiceException(message);
                logger.debug(message);
            }
        }
        return Objects.nonNull(outgoingSequenceFlow) ? outgoingSequenceFlow : defaultSequenceFlow;
    }

    @Override
    public WfFlowSelfSelectUserVo getSelfSelectUserConfig(String processDefinitionId, Map<String, Object> variables) {
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);
        Collection<FlowElement> flowElements = bpmnModel.getMainProcess().getFlowElements();
        WfFlowSelfSelectUserVo result = new WfFlowSelfSelectUserVo();
        List<WfFlowSelfSelectUserVo.WfFlowNodeSelfSelectConfig> nodes = new ArrayList<>();
        result.setNodes(nodes);

        StartEvent startEvent = (StartEvent) flowElements.stream().filter(e -> e instanceof StartEvent).findFirst().orElse(null);
        if (Objects.isNull(startEvent)) {
            throw new RuntimeException("流程配置错误，未找到开始节点");
        }
        FlowElement currElement = startEvent;

        while (!Objects.isNull(currElement)) {
            if (currElement instanceof SequenceFlow) {
                // 流向
                currElement = ((SequenceFlow) currElement).getTargetFlowElement();
                continue;
            } else if (currElement instanceof ExclusiveGateway) {
                // 网关分支节点
                currElement = this.getGatewayFlow((ExclusiveGateway) currElement, variables);
                continue;
            } else if (currElement instanceof FlowNode flowNode) {
                if (CollUtil.isEmpty(flowNode.getOutgoingFlows())) {
                    // 结束节点
                    break;
                }
                if (currElement instanceof UserTask userTask) {
                    // 用户任务节点
                    Map<String, Object> nodeProperties = ActFlowElementUtil.getNodeAllProperties(userTask);
                    if (Constants.Y.equals(nodeProperties.get("applicantChoice"))) {
                        WfFlowSelfSelectUserVo.WfFlowNodeSelfSelectConfig config = new WfFlowSelfSelectUserVo.WfFlowNodeSelfSelectConfig();
                        config.setNodeId(currElement.getId());
                        config.setNodeName(currElement.getName());
                        config.setApproverRule((String) nodeProperties.get("approverRule"));
                        config.setRequired(true);
                        config.setVariableKey(currElement.getId() + "_candidateUsers");
                        String selectMethod = (String) nodeProperties.get("selectMethod");
                        if (Objects.isNull(selectMethod)) {
                            selectMethod = "single";
                        }
                        config.setSelectMethod(selectMethod);
                        nodes.add(config);
                    }
                }

                currElement = flowNode.getOutgoingFlows().get(0);
                continue;
            } else {
                logger.debug("未处理的节点类型：{}", currElement.getClass().getName());
            }
            currElement = null;
        }

        result.setEnabledSelfSelect(CollUtil.isNotEmpty(nodes));
        return result;
    }

    @Override
    public void updateCurrentRecord(String processInstanceId, Map<String, Object> formData) {
        runtimeService.setVariable(processInstanceId, "currentRecord", formData);
    }
}
