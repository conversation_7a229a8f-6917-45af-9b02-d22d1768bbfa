alter table form comment '表单';

alter table form_action
    comment '作废';
alter table form_action_condition
    comment '作废';

alter table form_history
    comment '作废';
alter table form_show_rule
    comment '作废';
alter table form_table
    comment '表单数据库表';
alter table form_table_field
    comment '表单数据库表字段';

alter table form
    modify name varchar(100) null comment '表单名称';

alter table form
    modify code varchar(100) null comment '编码';

alter table form
    modify type varchar(100) null comment '类型';

alter table form
    modify view_form_id varchar(100) null comment '视图id';

alter table form
    modify view_list_form_id varchar(100) null comment '列表视图id';

alter table form
    modify form_status varchar(100) null comment '表单状态';

alter table form
    modify form_design_config text null comment '表单前端配置';

alter table form
    modify create_by varchar(32) null comment '创建人';

alter table form
    modify create_time datetime null comment '创建时间';

alter table form
    modify update_by varchar(32) null comment '更新人';

alter table form
    modify update_time datetime null comment '更新时间';

alter table form
    modify group_name varchar(255) default '默认' null comment '分组';

alter table form_init_rule
    modify form_id varchar(32) null comment '表单id';

alter table form_init_rule
    modify code varchar(32) null comment '编码';

alter table form_init_rule
    modify name varchar(64) null comment '名称';

alter table form_init_rule
    modify type varchar(32) null comment '类型';

alter table form_init_rule
    modify options text null comment '配置信息';

alter table form_init_rule
    modify output_field varchar(32) null comment '输出字段';

alter table form_init_rule_field
    modify init_rule_id varchar(32) null comment '初始化规则id';

alter table form_init_rule_field
    modify form_id varchar(32) null comment '表单id';

alter table form_init_rule_field
    modify source_code varchar(32) null comment '源字段';

alter table form_init_rule_field
    modify target_code varchar(32) null comment '目标字段';

alter table form_init_rule_field
    modify type varchar(32) null comment '类型';

alter table form_table
    modify form_id varchar(100) null comment '表单id';

alter table form_table
    modify name varchar(100) null comment '名称';

alter table form_table
    modify table_name varchar(100) null comment '真实表名';

alter table form_table
    modify type varchar(100) null comment '类型';

alter table form_table
    modify create_by varchar(32) null comment '创建人';

alter table form_table
    modify create_time datetime null comment '创建时间';

alter table form_table
    modify update_by varchar(32) null comment '更新人';

alter table form_table
    modify update_time datetime null comment '更新时间';

alter table form_table
    modify status varchar(10) default 'created' not null comment '状态';
alter table form_table_field
    modify form_id varchar(100) null comment '表单id';

alter table form_table_field
    modify form_table_id varchar(100) null comment '表单数据库表id';

alter table form_table_field
    modify name varchar(100) null comment '字段名称';

alter table form_table_field
    modify label varchar(100) null comment '字段标签';

alter table form_table_field
    modify parent_id varchar(32) null comment '父字段';

alter table form_table_field
    modify jdbc_type varchar(100) null comment 'jdbc数据库类型';

alter table form_table_field
    modify props longtext collate utf8mb4_bin null comment '前端配置';

alter table form_table_field
    modify component varchar(100) null comment '组件';

alter table form_table_field
    modify component_order int null comment '组件排序';

alter table form_table_field
    modify field_order int null comment '字段排序';

alter table form_table_field
    modify length int null comment '长度';

alter table form_table_field
    modify digit int null comment '小数点';

alter table form_table_field
    modify status varchar(100) null comment '状态';

alter table form_table_field
    modify ftf_is_system varchar(32) null comment '系统自带';

alter table form_table_field
    modify ftf_display varchar(32) null comment '是否显示';

alter table form_table_field
    modify options longtext collate utf8mb4_bin null comment '字段配置';

alter table form_table_field
    modify description varchar(100) null comment '描述';

alter table form_table_field
    modify create_by varchar(255) null comment '创建人';

alter table form_table_field
    modify update_by varchar(255) null comment '更新人';

alter table form_table_field
    modify create_time datetime null comment '创建时间';

alter table form_table_field
    modify update_time datetime null comment '更新时间';

alter table view_form
    modify form_id varchar(100) null comment '表单id';

alter table view_form
    modify type varchar(100) null comment '类型';

alter table view_form
    modify name varchar(100) null comment '名称';

alter table view_form
    modify system_type varchar(100) null comment '系统类型';

alter table view_form
    modify status varchar(10) null comment '状态';

alter table view_form
    comment '表单视图';



alter table view_form_config
    modify view_form_type varchar(32) not null comment '表单视图类型';

alter table view_form_config
    modify type varchar(32) null comment '配置类型';

alter table view_form_config
    modify name varchar(100) null comment '名称';

alter table view_form_config
    modify system_type varchar(32) null comment '系统类型';

alter table view_form_config
    modify enabled varchar(32) null comment '是否启用';

alter table view_form_config
    modify options longtext collate utf8mb4_bin null comment '配置信息';

alter table view_form_config
    modify view_form_id varchar(32) null comment '视图id';

