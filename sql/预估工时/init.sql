CREATE TABLE `sys_expect_project_work_time` (
                                                `id` varchar(32) NOT NULL,
                                                `project_id` bigint(20) NOT NULL,
                                                `expect_date` datetime DEFAULT NULL COMMENT '预估月份',
                                                `expect_work_time` bigint(20) DEFAULT NULL,
                                                `create_time` datetime DEFAULT NULL,
                                                `update_time` datetime DEFAULT NULL,
                                                `create_by` varchar(255) DEFAULT NULL,
                                                `update_by` varchar(255) DEFAULT NULL,
                                                PRIMARY KEY (`id`,`project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `sys_expect_user_work_time` (
                                             `id` varchar(32) NOT NULL,
                                             `expect_project_id` varchar(32) DEFAULT NULL,
                                             `expect_work_time` bigint(20) DEFAULT NULL COMMENT '预估工时',
                                             `user_id` bigint(20) DEFAULT NULL COMMENT '用户',
                                             `create_time` datetime DEFAULT NULL,
                                             `update_time` datetime DEFAULT NULL,
                                             `create_by` varchar(32) DEFAULT NULL,
                                             `update_by` varchar(32) DEFAULT NULL,
                                             PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `pms_config` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `config_key`, `config_name`,
                          `config_value`, `tip`, `suffix`, `remark`, `sort`, `default_value`, `required`, `visible`,
                          `control_type`, `control_config`, `tag`, `tag_sort`, `block`)
VALUES (314, '1', '2025-05-30 10:05:13', '1', '2025-06-11 16:56:29', 'expectTimeUnit', '预估工时单位', 'hour', NULL,
        NULL, NULL, 1, 'HOUR', 'Y', 'Y', 'select',
        '{\"options\":[{\"key\":\"hour\",\"label\":\"小时\",\"value\":\"hour\"},{\"key\":\"minute\",\"label\":\"分钟\",\"value\":\"minute\"}]}',
        '预估工时', '8', '1');
INSERT INTO `pms_config` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `config_key`, `config_name`,
                          `config_value`, `tip`, `suffix`, `remark`, `sort`, `default_value`, `required`, `visible`,
                          `control_type`, `control_config`, `tag`, `tag_sort`, `block`)
VALUES (315, '1', '2025-05-30 10:06:35', '1', '2025-06-11 16:56:29', 'expectDiffThreshold', '预估工时操作类型',
        'PROJECT', NULL, NULL, NULL, 2, 'PROJECT', 'Y', 'Y', 'select',
        '{\"options\":[{\"key\":\"PROJECT\",\"label\":\"项目\",\"value\":\"PROJECT\"},{\"key\":\"USER\",\"label\":\"用户\",\"value\":\"USER\"}]}',
        '预估工时', '8', '1');
INSERT INTO `pms_config` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `config_key`, `config_name`,
                          `config_value`, `tip`, `suffix`, `remark`, `sort`, `default_value`, `required`, `visible`,
                          `control_type`, `control_config`, `tag`, `tag_sort`, `block`)
VALUES (316, '1', '2025-05-30 10:09:27', '1', '2025-06-11 16:56:29', 'expectTimeActionTye', '工时差异阈值', '20', NULL,
        NULL, NULL, 3, NULL, 'Y', 'Y', 'number', NULL, '预估工时', '8', '1');
INSERT INTO `pms_config` (`id`, `create_by`, `create_time`, `update_by`, `update_time`, `config_key`, `config_name`,
                          `config_value`, `tip`, `suffix`, `remark`, `sort`, `default_value`, `required`, `visible`,
                          `control_type`, `control_config`, `tag`, `tag_sort`, `block`)
VALUES (317, '1', '2025-05-30 10:09:37', '1', '2025-06-11 16:56:29', 'diffRange', '项目工时差异分布饼图范围',
        '[{\"rangeTitle\":\"严重低估\",\"min\":0.3},{\"rangeTitle\":\"轻微低估\",\"min\":0.1,\"max\":0.3},{\"rangeTitle\":\"正常范围\",\"min\":-0.1,\"max\":0.1},{\"rangeTitle\":\"轻微高估\",\"min\":-0.3,\"max\":-0.1},{\"rangeTitle\":\"严重高估\",\"max\":-0.3}]',
        NULL, NULL, NULL, 4, NULL, 'Y', 'Y', 'array',
        '{\"rangeTitle\":\"input\",\"min\":\"number\",\"max\":\"number\"}', '预估工时', '8', '2');