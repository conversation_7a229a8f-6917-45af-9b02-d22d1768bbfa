#
uat 升级sql
# 2025年5月20日
  # form 表单添加分组
alter table form
    add group_name varchar(255) default '默认' not null comment '分组';
#
创建项目文件操作日志表
CREATE TABLE `pms_project_task_file_log`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT,
    `project_id`  bigint(20) DEFAULT NULL COMMENT '项目',
    `name`        varchar(255) DEFAULT NULL COMMENT '文件名',
    `path`        varchar(255) DEFAULT NULL COMMENT '文件地址',
    `type`        varchar(255) DEFAULT NULL COMMENT '操作类型',
    `create_by`   varchar(255) DEFAULT NULL COMMENT '创建人',
    `update_by`   varchar(255) DEFAULT NULL,
    `create_time` datetime     DEFAULT NULL,
    `update_time` datetime     DEFAULT NULL,
    `user_id`     bigint(20) DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

alter table pms_project_task
    add is_used varchar(1) default 'N' not null comment '是否被使用';

alter table pms_project_task_file
    add doc_link varchar(500) null comment '文档链接';

alter table pms_project_task_file
    add doc_type varchar(1) default '0' not null comment '文档类型（0.文件，1.链接）';


alter table pms_project_task_file_his
    add doc_link varchar(500) null comment '文档链接';

alter table pms_project_task_file_his
    add doc_type varchar(1) default '0' not null comment '文档类型（0.文件，1.链接）';

CREATE TABLE `view_form_connect`
(
    `id`           varchar(32)  NOT NULL,
    `name`         varchar(255) NOT NULL COMMENT '名称',
    `code`         varchar(255) NOT NULL COMMENT '编码',
    `view_form_id` varchar(32)  NOT NULL COMMENT '视图',
    `create_time`  datetime    DEFAULT NULL COMMENT '创建时间',
    `update_time`  datetime    DEFAULT NULL COMMENT '更新时间',
    `create_by`    varchar(32) DEFAULT NULL COMMENT '创建人',
    `update_by`    varchar(32) DEFAULT NULL COMMENT '更新人',
    `form_id`      varchar(32)  NOT NULL COMMENT '表单id',
    `init_rule_id` varchar(32) DEFAULT NULL COMMENT '表单初始化规则id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

alter table form_table
    add status varchar(10) default 'created' not null comment '状态';
#
确保状态正确
update form_table
set status = 'publish'
where form_id in (select id from form where form_status = 'publish') # 客户的逻辑删除
alter table pms_customer
    add is_deleted varchar(1) default 'N' not null comment '逻辑删除';


update sys_menu
set component = 'workHourManage/fillInDay/index'
where menu_name = '工时填报'
  and component = 'fillInDay/index';
update sys_menu
set component = 'workHourManage/workRecordReset/index'
where menu_name = '工时重填'
  and component = 'pms/workRecordReset/index';
update sys_menu
set component = 'workHourManage/attendanceNew/index'
where menu_name = '考勤导入'
  and component = 'attendanceNew/index';
update sys_menu
set component = 'workHourManage/query/personnalDetail/index'
where menu_name = '日填报详情'
  and component = 'query/personnalDetail/index';
update sys_menu
set component = 'workHourManage/query/userSubmissionStatus/index'
where menu_name = '用户填报情况'
  and component = 'query/userSubmissionStatus/index';
update sys_menu
set component = 'workHourManage/query/fillStatusReport/index'
where menu_name = '填报预警'
  and component = 'query/fillStatusReport/index';
update sys_menu
set component = 'workHourManage/query/userReportAnalysis/index'
where menu_name = '用户填报分析'
  and component = 'query/userReportAnalysis/index';
update sys_menu
set component = 'reportManage/workHour/projectHourDetail/index'
where menu_name = '员工工时月报'
  and component = 'report/projectHourDetail/index';
update sys_menu
set component = 'reportManage/workHour/userWorkTimeDetail/index'
where menu_name = '用户工时明细'
  and component = 'report/userWorkTimeDetail/index';
update sys_menu
set component = 'reportManage/workHour/userWorkTimeDailyDetail/index'
where menu_name = '日工时明细表'
  and component = 'report/userWorkTimeDailyDetail/index';
update sys_menu
set component = 'reportManage/workHour/projectYearReport/index'
where menu_name = '项目工时年报汇总'
  and component = 'query/projectYearReport/index';
update sys_menu
set component = 'reportManage/workHour/deptHoursDetails/index'
where menu_name = '部门日工时明细'
  and component = 'statements/deptHoursDetails/index';
update sys_menu
set component = 'reportManage/workHour/projectHoursOverview/index'
where menu_name = '项目工时总览'
  and component = 'statements/projectHoursOverview/index';

alter table pms_project_config
    add param varchar(255) null;

alter table form_history
    add is_deleted varchar(1) default 'N' not null;

alter table pms_public_user
    add deleted varchar(1) default 'N' null;