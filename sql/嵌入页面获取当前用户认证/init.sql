CREATE TABLE `app_auth_security` (
                                     `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                     `name` varchar(255) DEFAULT NULL,
                                     `app_key` varchar(255) DEFAULT NULL,
                                     `app_secret` longtext DEFAULT NULL,
                                     `create_by` varchar(255) DEFAULT NULL,
                                     `update_by` varchar(255) DEFAULT NULL,
                                     `create_time` datetime DEFAULT NULL,
                                     `update_time` datetime DEFAULT NULL,
                                     `is_enable` varchar(1) DEFAULT 'Y',
                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;