ALTER TABLE `pms_public_user`
    ADD COLUMN `deleted` varchar(32) NULL DEFAULT 'N' COMMENT '是否删除' AFTER `status`;

ALTER TABLE `pms_public_user_project`
    ADD COLUMN `public_user_line_id` bigint(20) NULL COMMENT '公共人员明细ID' AFTER `public_user_id`;

CREATE TABLE `pms_public_user_line`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT,
    `create_by`       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
    `create_time`     datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
    `update_by`       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
    `update_time`     datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
    `public_user_id`  bigint(20) NULL DEFAULT NULL COMMENT '公共人员ID',
    `user_id`         bigint(20) NULL DEFAULT NULL COMMENT '人员ID',
    `allocation_type` varchar(32) NULL DEFAULT NULL COMMENT '分摊类型：全部、部分',
    `start_time`      datetime(0) NULL DEFAULT NULL COMMENT '开始时间',
    `end_time`        datetime(0) NULL DEFAULT NULL COMMENT '结束时间',

    PRIMARY KEY (`id`) USING BTREE,
    INDEX             `pms_public_user_line_public_user_id`(`public_user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '公共人员明细' ROW_FORMAT = Dynamic;

-- 更新数据
insert into pms_public_user_line(public_user_id, user_id, allocation_type, start_time, end_time, create_by, update_by,
                                 create_time, update_time)
select t.id,
       t.user_id,
       t.allocation_type,
       '2024-01-01',
       '2025-05-31',
       '1',
       '1',
       now(),
       now()
from pms_public_user t;

update pms_public_user_project t,pms_public_user_line tt
set t.public_user_line_id=tt.id
where t.public_user_id=tt.public_user_id;

INSERT INTO `sys_menu`(`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`,
                       `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`,
                       `update_by`, `update_time`, `remark`)
VALUES (null, '公共人员明细', 2010, 3, 'publicUserLine', 'basic/publicUser/line', NULL, 1, 0, 'C', '1', '0', '',
        'swagger', 'admin', '2025-03-24 14:20:36', 'admin', '2025-03-24 14:22:02', '');

ALTER TABLE `pms_public_user`
DROP
COLUMN `allocation_type`,
DROP
COLUMN `status`;

ALTER TABLE `pms_public_user_project`
DROP
COLUMN `public_user_id`,
DROP
COLUMN `user_id`,
DROP
COLUMN `status`;