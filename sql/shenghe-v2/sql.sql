ALTER TABLE `sys_work_record`
    ADD COLUMN `work_duration_percent` decimal(13, 4) NULL COMMENT '填报工时（百分比）' AFTER `work_duration`;

CREATE TABLE `sys_work_entry`
(
    `id`                     bigint(20) NOT NULL AUTO_INCREMENT,
    `create_by`              varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
    `create_time`            datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
    `update_by`              varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
    `update_time`            datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
    `user_id`                bigint(20) NULL DEFAULT NULL COMMENT '用户ID',
    `work_time`              datetime(0) NULL DEFAULT NULL COMMENT '日期',

    PRIMARY KEY (`id`) USING BTREE,
    INDEX                    `sys_work_entry_user_id`(`user_id`) USING BTREE,
    INDEX                    `sys_work_entry_work_time`(`work_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '工时填报逾期可填记录' ROW_FORMAT = Dynamic;

INSERT INTO `pms_config`(`id`, `create_by`, `create_time`, `update_by`, `update_time`, `config_key`, `config_name`, `config_value`, `tip`, `suffix`, `remark`, `sort`, `default_value`, `required`, `visible`, `control_type`, `control_config`, `tag`, `tag_sort`, `block`) VALUES (26, '1', '2024-07-09 14:16:45', '1', '2024-10-14 15:37:36', 'workLogEntryMode', '工作日志填报方式', 'select', NULL, NULL, NULL, 1, 'input', 'Y', 'Y', 'select', '{\"options\":[{\"key\":\"input\",\"label\":\"文本输入\",\"value\":\"input\"},{\"key\":\"select\",\"label\":\"工作日志下拉\",\"value\":\"select\"}]}', '工时填报', '1', '5');

ALTER TABLE `sys_project`
    ADD COLUMN `activity_type` varchar(32) NULL COMMENT '活动类型' AFTER `type`;