package com.rcszh.external.server.weixin;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import com.rcszh.base.common.exception.ServiceException;
import com.rcszh.cache.service.CacheService;
import com.rcszh.common.util.OkHttpUtils;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.common.util.json.GsonHelper;
import me.chanjar.weixin.common.util.json.GsonParser;
import me.chanjar.weixin.cp.bean.WxCpDepart;
import me.chanjar.weixin.cp.bean.WxCpOauth2UserInfo;
import me.chanjar.weixin.cp.bean.WxCpUser;
import me.chanjar.weixin.cp.bean.WxCpUserDetail;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import me.chanjar.weixin.cp.bean.oa.WxCpCheckinDayData;
import me.chanjar.weixin.cp.util.json.WxCpGsonBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class WeiXinCpService {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private CacheService cacheService;

    /**
     * 获取授权链接
     *
     * @param redirectUri
     * @param state
     * @param corpId
     * @param agentId
     * @return
     */
    public String getAuthUrl(String redirectUri, String state, String corpId, String agentId) {
        return StrUtil.format("https://open.weixin.qq.com/connect/oauth2/authorize?appid={}&redirect_uri={}&response_type=code&scope=snsapi_privateinfo&state={}&agentid={}#wechat_redirect", corpId, redirectUri, state, agentId);
    }

    /**
     * 登录企微
     *
     * @param corpid
     * @param appKey
     * @param corpsecret
     * @param code
     * @return
     */
    public WxCpOauth2UserInfo loginByCpCode(String corpid, Integer appKey, String corpsecret, String code) {
        try {
            String accessToken = this.getAccessToken(corpid, appKey, corpsecret);
            String result = OkHttpUtils.builder().url(StrUtil.format("https://qyapi.weixin.qq.com/cgi-bin/auth/getuserinfo?access_token={}&code={}", accessToken, code)).get().sync();
            JsonObject jo = GsonParser.parse(result);
            return WxCpOauth2UserInfo.builder().userId(GsonHelper.getString(jo, "UserId")).deviceId(GsonHelper.getString(jo, "DeviceId")).openId(GsonHelper.getString(jo, "OpenId")).userTicket(GsonHelper.getString(jo, "user_ticket")).expiresIn(GsonHelper.getString(jo, "expires_in")).externalUserId(GsonHelper.getString(jo, "external_userid")).parentUserId(GsonHelper.getString(jo, "parent_userid")).studentUserId(GsonHelper.getString(jo, "student_userid")).build();
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 根据userid获取用户信息
     *
     * @param corpid
     * @param appKey
     * @param corpsecret
     * @param userid
     * @return
     */
    public WxCpUser getUser(String corpid, Integer appKey, String corpsecret, String userid) {
        try {
            String accessToken = this.getAccessToken(corpid, appKey, corpsecret);
            String result = OkHttpUtils.builder().url(StrUtil.format("https://qyapi.weixin.qq.com/cgi-bin/user/get?access_token={}&userid={}", accessToken, userid)).get().sync();
            return WxCpGsonBuilder.create().fromJson(result, WxCpUser.class);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 获取用户详情信息
     *
     * @param corpid
     * @param appKey
     * @param corpsecret
     * @param userTicket
     * @return
     */
    public WxCpUserDetail getUserDetail(String corpid, Integer appKey, String corpsecret, String userTicket) {
        try {
            String accessToken = this.getAccessToken(corpid, appKey, corpsecret);
            String result = OkHttpUtils.builder().url(StrUtil.format("https://qyapi.weixin.qq.com/cgi-bin/auth/getuserdetail?access_token={}", accessToken))
                    .addBodyParam("user_ticket", userTicket).post(true).sync();
            return WxCpGsonBuilder.create().fromJson(result, WxCpUserDetail.class);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 批量发送卡片信息
     *
     * @param corpid
     * @param appKey
     * @param corpsecret
     * @param msg
     * @param toUserIds
     * @param title
     * @param url
     */
    public void sendTextCardMsg(String corpid, Integer appKey, String corpsecret, String msg, List<String> toUserIds, String title, String url) {
        for (int i = 0; i < toUserIds.size(); i += 1000) {
            List<String> userList = null;
            if (i + 1000 > toUserIds.size()) {
                userList = toUserIds.subList(i, toUserIds.size());
            } else {
                userList = toUserIds.subList(i, i + 1000);
            }
            this.sendTextCardMsg(corpid, appKey, corpsecret, msg, userList.stream().collect(Collectors.joining("|")), title, url);
        }
    }

    /**
     * 发送卡片信息
     *
     * @param corpid
     * @param appKey
     * @param corpsecret
     * @param msg
     * @param toUserId
     * @param title
     * @param url
     */
    public void sendTextCardMsg(String corpid, Integer appKey, String corpsecret, String msg, String toUserId, String title, String url) {
        log.debug("sendTextCardMsg toUserId: {}, msg: {}", toUserId, msg);
        String accessToken = this.getAccessToken(corpid, appKey, corpsecret);
        WxCpMessage message = WxCpMessage.TEXTCARD()
                .agentId(appKey)
                .toUser(toUserId)
                .description(msg)
                .title(title)
                .url(url)
                .build();
        String result = OkHttpUtils.builder().url(StrUtil.format("https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token={}", accessToken)).addBodyData(message.toJson()).post(true).sync();
        if (new JSONObject(result).getInt("errcode") != 0) {
            throw new ServiceException("推送企微卡片信息错误:错误信息:{}", new JSONObject(result).getStr("errmsg"));
        }
    }

    /**
     * 发送文本消信
     *
     * @param msg
     * @param toUserId
     * @throws WxErrorException
     */
    public void sendTextMsg(String corpid, Integer appKey, String corpsecret, String msg, String toUserId) {
        log.debug("sendTextMsg toUserId: {}, msg: {}", toUserId, msg);
        try {
            String accessToken = this.getAccessToken(corpid, appKey, corpsecret);
            WxCpMessage message = WxCpMessage.TEXT()
                    .agentId(appKey)
                    .toUser(toUserId)
                    .content(msg)
                    .build();
            String result = OkHttpUtils.builder().url(StrUtil.format("https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token={}", accessToken)).addBodyData(message.toJson()).post(true).sync();
            if (new JSONObject(result).getInt("errcode") != 0) {
                throw new ServiceException("推送企微文本信息错误:错误信息:{}", new JSONObject(result).getStr("errmsg"));
            }
        } catch (Exception e) {
            log.error("推送企微文本信息错误,错误信息:{}", e.getMessage());
        }

    }

    /**
     * 同步企微考勤
     *
     * @param corpid
     * @param appKey
     * @param corpsecret
     * @param startTime
     * @param endTime
     * @param userIdList
     * @return
     */
    public List<WxCpCheckinDayData> getCheckinDataDay(String corpid, Integer appKey, String corpsecret, Date startTime, Date endTime, List<String> userIdList) {
        try {
            List<WxCpCheckinDayData> result = new ArrayList<>();
            if (CollUtil.isEmpty(userIdList)) {
                return result;
            }
            String accessToken = this.getAccessToken(corpid, appKey, corpsecret);
            for (int i = 0; i < userIdList.size(); i += 100) {
                int endNum = i + 100;
                if (endNum > userIdList.size()) {
                    endNum = userIdList.size();
                }

                String responseContent = OkHttpUtils.builder().url(StrUtil.format("https://qyapi.weixin.qq.com/cgi-bin/checkin/getcheckin_daydata?access_token={}", accessToken))
                        .addBodyParam("starttime", startTime.getTime() / 1000L)
                        .addBodyParam("endtime", endTime.getTime() / 1000L)
                        .addBodyParam("useridlist", userIdList.subList(i, endNum)).post(true).sync();
                JsonObject tmpJson = GsonParser.parse(responseContent);
                List<WxCpCheckinDayData> checkinDayData = WxCpGsonBuilder.create().fromJson(tmpJson.get("datas"), (new TypeToken<List<WxCpCheckinDayData>>() {
                }).getType());
                result.addAll(checkinDayData);
            }
            return result;
        } catch (Exception e) {
            log.error("获取打卡日报报错:{}", e.getMessage());
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 获取企业微信部门列表
     *
     * @param corpid
     * @param appKey
     * @param corpsecret
     * @return
     * @throws WxErrorException
     */
    public List<WxCpDepart> getDepartList(String corpid, Integer appKey, String corpsecret) throws WxErrorException {
        // ID为null时获取所有的列表
        String accessToken = this.getAccessToken(corpid, appKey, corpsecret);
        String result = OkHttpUtils.builder().url(StrUtil.format("https://qyapi.weixin.qq.com/cgi-bin/department/list?access_token={}", accessToken)).get().sync();
        JsonObject tmpJsonObject = GsonParser.parse(result);
        return WxCpGsonBuilder.create().fromJson(tmpJsonObject.get("department"), (new TypeToken<List<WxCpDepart>>() {
        }).getType());
    }

    /**
     * 获取企业微信部门列表
     * @param corpid
     * @param appKey
     * @param corpsecret
     * @return
     * @throws WxErrorException
     */
    public List<WxCpUser> getUserListByDeptId(String corpid, Integer appKey, String corpsecret, Long deptId) throws WxErrorException {
        // ID为null时获取所有的列表
        String accessToken = this.getAccessToken(corpid, appKey, corpsecret);
        String result = OkHttpUtils.builder().url(StrUtil.format("https://qyapi.weixin.qq.com/cgi-bin/user/list?access_token={}&department_id={}", accessToken, deptId)).get().sync();
        JsonObject jsonObject = GsonParser.parse(result);
        return WxCpGsonBuilder.create().fromJson(jsonObject.get("userlist"), (new TypeToken<List<WxCpUser>>() {
        }).getType());
    }

    /**
     * 获取token
     *
     * @param corpid
     * @param corpsecret
     * @return
     */
    private String getAccessToken(String corpid, Integer appKey, String corpsecret) {
        String key = StrUtil.format("wx_cp:wechat_cp_access_token_key:{}:{}", corpid, appKey);
        String token = cacheService.get(key);
        if (StrUtil.isNotBlank(token)) {
            return token;
        }
        String sync = OkHttpUtils.builder().url("https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={}&corpsecret={}", corpid, corpsecret).get().sync();
        cn.hutool.json.JSONObject entries = new cn.hutool.json.JSONObject(sync);
        if (entries.getInt("errcode") != 0) {
            throw new ServiceException("获取企微token失败:{}", entries.getStr("errmsg"));
        }
        String accessToken = entries.getStr("access_token");
        cacheService.set(key, accessToken, entries.getInt("expires_in") - 100);
        return accessToken;
    }
}
