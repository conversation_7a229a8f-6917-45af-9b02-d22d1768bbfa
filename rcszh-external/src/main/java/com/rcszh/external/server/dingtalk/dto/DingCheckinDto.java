package com.rcszh.external.server.dingtalk.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rcszh.common.constant.Constants;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.Objects;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class DingCheckinDto {
    private static final long serialVersionUID = 1L;

    private String userid;

    /**
     * 统计日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date statDay;

    /**
     * 当日打卡次数
     */
    private Integer checkinCount;

    /**
     * 当日实际工作时长，分钟 或 小时
     */
    private Integer regularWorkDuration;


    /**
     * 当日标准工作时长，单位：分钟
     */
    private Integer standardWorkDuration;

    /**
     * 出差时长：天
     */
    private Integer businessTripSec;

    /**
     * 当日最早打卡时间
     */
    private Date earliestTime;

    /**
     * 当日最晚打卡时间
     */
    private Date lastestTime;

    private String isTravel;

    private String relationOrder;

    public String getIsTravel() {
        if (Objects.nonNull(this.businessTripSec) && this.businessTripSec > 0) {
            return Constants.Y;
        }
        return Constants.N;
    }



}
