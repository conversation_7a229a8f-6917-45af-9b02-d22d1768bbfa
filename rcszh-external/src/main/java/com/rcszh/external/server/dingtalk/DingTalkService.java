package com.rcszh.external.server.dingtalk;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.net.URLEncodeUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.aliyun.dingtalkcontact_1_0.models.GetUserHeaders;
import com.aliyun.dingtalkcontact_1_0.models.GetUserResponse;
import com.aliyun.dingtalkcontact_1_0.models.GetUserResponseBody;
import com.aliyun.dingtalkoauth2_1_0.models.*;
import com.aliyun.teautil.models.RuntimeOptions;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.*;
import com.dingtalk.api.response.*;
import com.rcszh.base.common.exception.ServiceException;
import com.rcszh.base.common.utils.StringUtils;
import com.rcszh.cache.service.CacheService;
import com.rcszh.external.server.dingtalk.config.DingTalkConfiguration;
import com.rcszh.external.server.dingtalk.dto.DingCheckinDto;
import com.rcszh.external.server.dingtalk.dto.DingLeaveTimeDto;
import com.rcszh.external.server.dingtalk.dto.DingTalkInfo;
import com.taobao.api.ApiException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class DingTalkService {

    private static final Logger log = LoggerFactory.getLogger(DingTalkService.class);

    @Autowired
    private CacheService cacheService;

    public String getAuthUrl(String redirectUri, String state, String appKey) {
        redirectUri = URLEncodeUtil.encode(redirectUri);
        return StrUtil.format("https://login.dingtalk.com/oauth2/auth?redirect_uri={}&response_type=code&client_id={}&state={}&scope=openid&prompt=consent", redirectUri, appKey, state);
    }

    private String getOauthAccessToken(String appKey, String appSecret) {
        String key = StrUtil.format("dingTalk:accessToken:{}", appKey);
        String cacheToken = cacheService.get(key);
        if (StrUtil.isNotBlank(cacheToken)) {
            return cacheToken;
        }
        try {
            com.aliyun.dingtalkoauth2_1_0.Client client = DingTalkConfiguration.getOauth2Client();
            GetAccessTokenRequest req = new GetAccessTokenRequest();
            req.setAppKey(appKey);
            req.setAppSecret(appSecret);
            GetAccessTokenResponse token = client.getAccessToken(req);
            GetAccessTokenResponseBody body = token.getBody();
            String accessToken = body.getAccessToken();
            cacheService.set(key, accessToken, body.getExpireIn().intValue() / 2, TimeUnit.SECONDS);
            return accessToken;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }


    public GetUserResponseBody loginByCpCode(String code, String appKey, String appSecret) {
        try {
            System.out.println("code = " + code);
            com.aliyun.dingtalkoauth2_1_0.Client client = DingTalkConfiguration.getOauth2Client();
            GetUserTokenRequest req = new GetUserTokenRequest();
            req.setClientId(appKey);
            req.setClientSecret(appSecret);
            req.setGrantType("authorization_code");
            req.setCode(code);
            GetUserTokenResponse rsp = client.getUserToken(req);
            GetUserTokenResponseBody userTokenRes = rsp.getBody();
            String accessToken = userTokenRes.getAccessToken();
            GetUserResponseBody userInfo = this.getUserInfo(accessToken);
            System.out.println("result = " + rsp.getBody());
            return userInfo;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }

    private GetUserResponseBody getUserInfo(String accessToken) throws Exception {
        com.aliyun.dingtalkcontact_1_0.Client client = DingTalkConfiguration.getContactClient();
        GetUserHeaders getUserHeaders = new GetUserHeaders();
        getUserHeaders.xAcsDingtalkAccessToken = accessToken;
        //获取用户个人信息，如需获取当前授权人的信息，unionId参数必须传me
        GetUserResponse res = client.getUserWithOptions("me", getUserHeaders, new RuntimeOptions());
        GetUserResponseBody userResponseBody = res.getBody();
        System.out.println(JSONUtil.toJsonStr(userResponseBody));
        return userResponseBody;
    }

    public String getByUnionid(String appKey, String appSecret, String unionid) {
        try {
            String accessToken = this.getOauthAccessToken(appKey, appSecret);
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/user/getbyunionid");
            OapiUserGetbyunionidRequest req = new OapiUserGetbyunionidRequest();
            req.setUnionid(unionid);
            OapiUserGetbyunionidResponse rsp = client.execute(req, accessToken);
            if (rsp.getErrcode() != 0) {
                throw new ServiceException(rsp.getErrmsg());
            }
            return rsp.getResult().getUserid();
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }

    public List<DingCheckinDto> getCheckinDataDay(String appKey, String appSecret, Date startTime, Date endTime, List<String> useridList, Map<Long, String> checkinColumns) {
        try {
            List<DingCheckinDto> result = new ArrayList<>();
            Map<String, Integer> shiftMap = new HashMap<>();
            for (String userid : useridList) {
                List<DingCheckinDto> dingCheckinDtos = this.getCheckinDataDayUserid(appKey, appSecret, startTime, endTime, userid, checkinColumns, shiftMap);
                result.addAll(dingCheckinDtos);
            }
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }


    private OapiAttendanceShiftQueryResponse.TopShiftVo getShiftDetail(String accessToken, Long shiftId, String opUserId) throws ApiException {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/attendance/shift/query");
        OapiAttendanceShiftQueryRequest req = new OapiAttendanceShiftQueryRequest();
        req.setOpUserId(opUserId);
        req.setShiftId(shiftId);
        OapiAttendanceShiftQueryResponse rsp = client.execute(req, accessToken);
        System.out.println(rsp.getBody());
        return rsp.getResult();
    }

    private Integer getStandardWorkSecByShiftName(String appKey, String appSecret, String shiftName, String opUserId, Map<String, Integer> shiftMap) throws Exception {
        Integer cache = shiftMap.get(shiftName);
        if (Objects.nonNull(cache)) {
            return cache;
        }

        String accessToken = this.getOauthAccessToken(appKey, appSecret);
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/attendance/shift/search");
        OapiAttendanceShiftSearchRequest req = new OapiAttendanceShiftSearchRequest();
        req.setOpUserId(opUserId);
        req.setShiftName(shiftName);
        OapiAttendanceShiftSearchResponse rsp = client.execute(req, accessToken);
        if (rsp.getErrcode() != 0) {
            throw new ServiceException(rsp.getErrmsg());
        }
        int workTimeMinutes = 0;
        if (CollUtil.isNotEmpty(rsp.getResult())) {
            OapiAttendanceShiftSearchResponse.TopMinimalismShiftVO topMinimalismShiftVO = rsp.getResult().get(0);

            OapiAttendanceShiftQueryResponse.TopShiftVo shiftDetail = this.getShiftDetail(accessToken, topMinimalismShiftVO.getId(), opUserId);

            workTimeMinutes = shiftDetail.getShiftSetting().getWorkTimeMinutes().intValue();
            if (workTimeMinutes == -1) {
                workTimeMinutes = 0;
            }
        }
        shiftMap.put(shiftName, workTimeMinutes);
        return workTimeMinutes;
    }

    private List<DingCheckinDto> getCheckinDataDayUserid(String appKey, String appSecret, Date startTime, Date endTime, String userid, Map<Long, String> columnMapping, Map<String, Integer> shiftMap) throws Exception {
        String accessToken = this.getOauthAccessToken(appKey, appSecret);
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/attendance/getcolumnval");
        OapiAttendanceGetcolumnvalRequest req = new OapiAttendanceGetcolumnvalRequest();
        req.setUserid(userid);
        // 最大长度20
        req.setColumnIdList(columnMapping.keySet().stream().map(String::valueOf).collect(Collectors.joining(",")));
        req.setFromDate(DateUtil.beginOfDay(startTime));
        req.setToDate(DateUtil.endOfDay(endTime));
        OapiAttendanceGetcolumnvalResponse rsp = client.execute(req, accessToken);
        if (rsp.getErrcode() != 0) {
            throw new ServiceException(rsp.getErrmsg());
        }
        OapiAttendanceGetcolumnvalResponse.ColumnValListForTopVo result = rsp.getResult();

        Map<Date, DingCheckinDto> dataMap = new LinkedHashMap<>();
        for (OapiAttendanceGetcolumnvalResponse.ColumnValForTopVo columnVal : result.getColumnVals()) {
            String field = columnMapping.get(columnVal.getColumnVo().getId());
            for (OapiAttendanceGetcolumnvalResponse.ColumnDayAndVal val : columnVal.getColumnVals()) {
                DingCheckinDto dingCheckInDto = dataMap.get(val.getDate());
                if (Objects.isNull(dingCheckInDto)) {
                    dingCheckInDto = new DingCheckinDto();
                    dingCheckInDto.setStatDay(val.getDate());
                    dingCheckInDto.setUserid(userid);
                    dataMap.put(val.getDate(), dingCheckInDto);
                }
                // “班次”：1、休息 2、A 09:00-18:00 3、[空]
                if ("planDetail".equals(field)) {
                    // 没有班次
                    if (StrUtil.isBlank(val.getValue())) {
                        dingCheckInDto.setStandardWorkDuration(null);
                    } else if ("休息".equals(val.getValue())) {
                        dingCheckInDto.setStandardWorkDuration(0);
                    } else {
                        String[] split = val.getValue().split(" ");
                        dingCheckInDto.setStandardWorkDuration(this.getStandardWorkSecByShiftName(appKey, appSecret, split[0], userid, shiftMap));
                    }
                } else {
                    BeanUtil.setProperty(dingCheckInDto, field, val.getValue());
                }
            }
        }
        // 过滤掉没有班次的数据（入职前的考勤记录）
        return dataMap.values().stream().filter(t -> Objects.nonNull(t.getStandardWorkDuration())).collect(Collectors.toList());
    }


    public void sendTextCardMsg(String corpId, Long agentId, String appKey, String appSecret,
                                String content, List<String> userids, String title, String url) {
        // 同样的消息，钉钉会有去重逻辑；所以这里加上随机数，避免重复
        if (url.contains("?")) {
            url = url + "&randomNum=" + new Date().getTime();
        } else {
            url = url + "?randomNum=" + new Date().getTime();
        }
        log.info("发送卡片消息, url：{}", url);
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2");
        url = StringUtils.format("dingtalk://dingtalkclient/action/openapp?corpid={}&container_type=work_platform&app_id={}&redirect_type=jump&redirect_url={}", corpId, agentId, URLEncoder.encode(url, StandardCharsets.UTF_8));
        for (int i = 0; i < userids.size(); i += 100) {
            int endNum = i + 100;
            if (endNum > userids.size()) {
                endNum = userids.size();
            }
            OapiMessageCorpconversationAsyncsendV2Request request = new OapiMessageCorpconversationAsyncsendV2Request();
            request.setAgentId(agentId);
            request.setUseridList(String.join(",", userids.subList(i, endNum)));
            request.setToAllUser(false);

            OapiMessageCorpconversationAsyncsendV2Request.Msg msg = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
            msg.setActionCard(new OapiMessageCorpconversationAsyncsendV2Request.ActionCard());
            msg.getActionCard().setTitle(title);
            msg.getActionCard().setMarkdown(content);
            msg.getActionCard().setSingleTitle(title);
            msg.getActionCard().setSingleUrl(url);
            msg.setMsgtype("action_card");
            request.setMsg(msg);
            try {
                OapiMessageCorpconversationAsyncsendV2Response rsp = client.execute(request, this.getOauthAccessToken(appKey, appSecret));
                if (rsp.getErrcode() != 0) {
                    throw new ServiceException(rsp.getErrmsg());
                }
                System.out.println(rsp.getBody());
            } catch (ApiException e) {
                e.printStackTrace();
                throw new ServiceException(e.getMessage());
            }
        }
    }


    public void sendTextMarkdownMsg(Long agentId, String appKey, String appSecret,
                                String content, List<String> userids,String title) {
        log.info("发送Markdown普通消息");
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2");
        for (int i = 0; i < userids.size(); i += 100) {
            int endNum = i + 100;
            if (endNum > userids.size()) {
                endNum = userids.size();
            }
            OapiMessageCorpconversationAsyncsendV2Request request = new OapiMessageCorpconversationAsyncsendV2Request();
            request.setAgentId(agentId);
            request.setUseridList(String.join(",", userids.subList(i, endNum)));
            request.setToAllUser(false);

            OapiMessageCorpconversationAsyncsendV2Request.Msg msg = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
            msg.setMarkdown(new OapiMessageCorpconversationAsyncsendV2Request.Markdown());
            msg.getMarkdown().setText(content);
            msg.getMarkdown().setTitle(title);
            msg.setMsgtype("markdown");
            request.setMsg(msg);
            try {
                OapiMessageCorpconversationAsyncsendV2Response rsp = client.execute(request, this.getOauthAccessToken(appKey, appSecret));
                if (rsp.getErrcode() != 0) {
                    throw new ServiceException(rsp.getErrmsg());
                }
                System.out.println(rsp.getBody());
            } catch (ApiException e) {
                e.printStackTrace();
                throw new ServiceException(e.getMessage());
            }
        }
    }

    public List<OapiAttendanceVacationTypeListResponse.Result> getVacationTypeList(String appKey, String appSecret, String opUserId) {
        try {
            String accessToken = this.getOauthAccessToken(appKey, appSecret);
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/attendance/vacation/type/list");
            OapiAttendanceVacationTypeListRequest request = new OapiAttendanceVacationTypeListRequest();
            request.setOpUserid(opUserId);
            request.setVacationSource("all");
            OapiAttendanceVacationTypeListResponse response = client.execute(request, accessToken);
            if (!response.isSuccess()) {
                throw new ServiceException(response.getErrmsg());
            }
            return response.getResult();
        } catch (ApiException e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }

    public <T> List<DingLeaveTimeDto> getLeaveTimeDay(String appKey, String appSecret, Date startTime, Date endTime, List<String> useridList, List<String> leaveNames) {
        ArrayList<DingLeaveTimeDto> result = new ArrayList<>();
        for (String userid : useridList) {
            for (List<String> subLeaveNames : ListUtil.split(leaveNames, 20)) {
                List<DingLeaveTimeDto> leaveTimeDtos = this.getLeaveTimeByUserid(appKey, appSecret, startTime, endTime, userid, String.join(",", subLeaveNames));
                result.addAll(leaveTimeDtos);
            }
        }
        return result;
    }

    private List<DingLeaveTimeDto> getLeaveTimeByUserid(String appKey, String appSecret, Date startTime, Date endTime, String userid, String leaveNames) {
        ArrayList<DingLeaveTimeDto> dtos = new ArrayList<>();
        try {
            String accessToken = this.getOauthAccessToken(appKey, appSecret);
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/attendance/getleavetimebynames");
            OapiAttendanceGetleavetimebynamesRequest req = new OapiAttendanceGetleavetimebynamesRequest();
            req.setUserid(userid);
            req.setLeaveNames(leaveNames);
            req.setFromDate(DateUtil.beginOfDay(startTime));
            req.setToDate(DateUtil.endOfDay(endTime));
            OapiAttendanceGetleavetimebynamesResponse rsp = client.execute(req, accessToken);
            OapiAttendanceGetleavetimebynamesResponse.ColumnValListForTopVo result = rsp.getResult();
            if (Objects.isNull(result) || CollUtil.isEmpty(result.getColumns())) {
                return dtos;
            }
            for (OapiAttendanceGetleavetimebynamesResponse.ColumnValForTopVo column : result.getColumns()) {
                OapiAttendanceGetleavetimebynamesResponse.ColumnForTopVo columnvo = column.getColumnvo();
                if (Objects.isNull(columnvo) || CollUtil.isEmpty(column.getColumnvals())) {
                    continue;
                }
                for (OapiAttendanceGetleavetimebynamesResponse.ColumnDayAndVal columnval : column.getColumnvals()) {
                    if (Objects.nonNull(columnval.getValue()) && !columnval.getValue().equals("null") && NumberUtil.parseDouble(columnval.getValue()) > 0) {
                        DingLeaveTimeDto dingLeaveTimeDto = new DingLeaveTimeDto();
                        dingLeaveTimeDto.setStatDay(columnval.getDate());
                        dingLeaveTimeDto.setUserid(userid);
                        dingLeaveTimeDto.setName(columnvo.getName());
                        dingLeaveTimeDto.setDuration(new BigDecimal(columnval.getValue()));
                        dingLeaveTimeDto.setType(String.valueOf(columnvo.getType()));
                        dtos.add(dingLeaveTimeDto);
                    }
                }
            }
            return dtos;
        } catch (ApiException e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 同步用户
     * @param deptIds
     */
    public List<OapiV2UserGetResponse.UserGetResponse> findAllUser(List<Long> deptIds, String appKey, String appSecret) throws ApiException {
        String accessToken = getOauthAccessToken(appKey, appSecret);
        List<OapiV2UserGetResponse.UserGetResponse> dataList = new ArrayList<>();
        for (Long deptId : deptIds) {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/user/listid");
            OapiUserListidRequest req = new OapiUserListidRequest();
            req.setDeptId(deptId);
            OapiUserListidResponse rsp = client.execute(req, accessToken);
            if (!rsp.isSuccess()) {
                throw new ServiceException(rsp.getErrmsg());
            }
            OapiUserListidResponse.ListUserByDeptResponse result = rsp.getResult();
            List<String> useridList = result.getUseridList();
            for (String userid : useridList) {
                DingTalkClient dingTalkClient = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/get");
                OapiV2UserGetRequest getRequest = new OapiV2UserGetRequest();
                getRequest.setUserid(userid);
                getRequest.setLanguage("zh_CN");
                OapiV2UserGetResponse getResponse = dingTalkClient.execute(getRequest, accessToken);
                if (!getResponse.isSuccess()) {
                    throw new ServiceException(getResponse.getErrmsg());
                }
                OapiV2UserGetResponse.UserGetResponse userGetResponse = getResponse.getResult();
                dataList.add(userGetResponse);
            }
        }
        return dataList;
    }

    /**
     * 获取部门信息
     */
    public List<OapiV2DepartmentListsubResponse.DeptBaseResponse> findAllDept(String appKey, String appSecret) throws ApiException {
        List<OapiV2DepartmentListsubResponse.DeptBaseResponse> depths = new ArrayList<>();
        Long parentId = 1L;
        OapiV2DepartmentGetResponse.DeptGetResponse result = getDeptById(parentId,appKey, appSecret);
        OapiV2DepartmentListsubResponse.DeptBaseResponse parentDept = new OapiV2DepartmentListsubResponse.DeptBaseResponse();
        parentDept.setDeptId(result.getDeptId());
        parentDept.setParentId(null);
        parentDept.setName(result.getName());
        depths.add(parentDept);
        log.error("顶层部门：{}", parentDept.getName());
        findAllDeptId(parentId, depths, appKey, appSecret);
        return depths;
    }

    /**
     * 查询部门
     */
    public void findAllDeptId(Long deptId, List<OapiV2DepartmentListsubResponse.DeptBaseResponse> dataList, String appKey, String appSecret) throws ApiException {
        String accessToken = getOauthAccessToken(appKey, appSecret);
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/department/listsub");
        OapiV2DepartmentListsubRequest req = new OapiV2DepartmentListsubRequest();
        req.setDeptId(deptId);
        OapiV2DepartmentListsubResponse rsp = client.execute(req, accessToken);
        if (!rsp.isSuccess()) {
            throw new ServiceException(rsp.getErrmsg());
        }
        if (Objects.nonNull(rsp.getResult())) {
            List<OapiV2DepartmentListsubResponse.DeptBaseResponse> result = rsp.getResult();
            dataList.addAll(result);
            log.error("父部门id：{}",deptId);
            for (OapiV2DepartmentListsubResponse.DeptBaseResponse response : result) {
                log.error("当前子部门：{}",response.getName());
                this.findAllDeptId(response.getDeptId(), dataList, appKey, appSecret);
            }
        }
    }

    public List<DingTalkInfo> findDingTalkUserAndDeptInfo(String appKey, String appSecret) throws ApiException {
        Long parentId = 1L;
        OapiV2DepartmentGetResponse.DeptGetResponse result = getDeptById(parentId,appKey, appSecret);
        OapiV2DepartmentListsubResponse.DeptBaseResponse parentDept = new OapiV2DepartmentListsubResponse.DeptBaseResponse();
        parentDept.setDeptId(result.getDeptId());
        parentDept.setParentId(null);
        parentDept.setName(result.getName());
        List<OapiV2UserListResponse.ListUserResponse> users = getUserByDeptId(result.getDeptId(), appKey, appSecret);
        List<DingTalkInfo> resultList = new ArrayList<>();
        resultList.add(new DingTalkInfo(parentDept,users));
        Queue<OapiV2DepartmentListsubResponse.DeptBaseResponse> queue = new LinkedList<>();
        queue.add(parentDept);
        while (!queue.isEmpty()) {
            OapiV2DepartmentListsubResponse.DeptBaseResponse dept = queue.poll();
            log.error("当前部门：{}",dept.getName());
            List<OapiV2DepartmentListsubResponse.DeptBaseResponse> childDept = getChildDept(dept.getDeptId(), appKey, appSecret);
            for (OapiV2DepartmentListsubResponse.DeptBaseResponse subDept : childDept) {
                List<OapiV2UserListResponse.ListUserResponse> userList = getUserByDeptId(subDept.getDeptId(), appKey, appSecret);
                resultList.add(new DingTalkInfo(subDept,userList));
                queue.add(subDept);
            }
        }
        return resultList;
    }

    private OapiV2DepartmentGetResponse.DeptGetResponse getDeptById(Long deptId,String appKey, String appSecret) throws ApiException {
        // 获取顶层部门
        String accessToken = getOauthAccessToken(appKey, appSecret);
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/department/get");
        OapiV2DepartmentGetRequest req = new OapiV2DepartmentGetRequest();
        req.setDeptId(deptId);
        OapiV2DepartmentGetResponse rsp = client.execute(req, accessToken);
        if (!rsp.isSuccess()) {
            throw new ServiceException(rsp.getErrmsg());
        }
        return rsp.getResult();
    }

    private List<OapiV2DepartmentListsubResponse.DeptBaseResponse> getChildDept(Long deptId, String appKey, String appSecret) throws ApiException {
        String accessToken = getOauthAccessToken(appKey, appSecret);
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/department/listsub");
        OapiV2DepartmentListsubRequest req = new OapiV2DepartmentListsubRequest();
        req.setDeptId(deptId);
        OapiV2DepartmentListsubResponse rsp = client.execute(req, accessToken);
        if (!rsp.isSuccess()) {
            throw new ServiceException(rsp.getErrmsg());
        }
        return rsp.getResult();
    }

    private List<OapiV2UserListResponse.ListUserResponse> getUserByDeptId(Long deptId, String appKey, String appSecret) throws ApiException {
        String accessToken = getOauthAccessToken(appKey, appSecret);
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/list");
        OapiV2UserListRequest req = new OapiV2UserListRequest();
        boolean hasMore = true;
        List<OapiV2UserListResponse.ListUserResponse> userResponseList = new ArrayList<>();
        Long size = 100L;
        Long current = 0L;
        while (hasMore) {
            req.setDeptId(deptId);
            req.setSize(size);
            req.setCursor(current);
            OapiV2UserListResponse rsp = client.execute(req, accessToken);
            if (!rsp.isSuccess()) {
                throw new ServiceException(rsp.getErrmsg());
            }
            OapiV2UserListResponse.PageResult result = rsp.getResult();
            List<OapiV2UserListResponse.ListUserResponse> list = result.getList();
            userResponseList.addAll(list);
            hasMore = result.getHasMore();
            current = result.getNextCursor();
        }
        return userResponseList;
    }

}
