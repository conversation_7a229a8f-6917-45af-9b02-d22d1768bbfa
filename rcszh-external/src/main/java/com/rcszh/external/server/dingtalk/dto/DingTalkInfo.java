package com.rcszh.external.server.dingtalk.dto;

import com.dingtalk.api.response.OapiV2DepartmentListsubResponse;
import com.dingtalk.api.response.OapiV2UserListResponse;
import lombok.Data;

import java.util.List;
@Data
public class DingTalkInfo {
    private OapiV2DepartmentListsubResponse.DeptBaseResponse dept;
    private List<OapiV2UserListResponse.ListUserResponse> users;
    public DingTalkInfo(OapiV2DepartmentListsubResponse.DeptBaseResponse dept, List<OapiV2UserListResponse.ListUserResponse> users) {
        this.dept = dept;
        this.users = users;
    }
}
