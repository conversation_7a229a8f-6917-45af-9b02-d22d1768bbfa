package com.rcszh.external.server.dingtalk.config;

import com.aliyun.teaopenapi.models.Config;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Service;

@Service
@Configuration
public class DingTalkConfiguration {

    public static Config getConfig() {
        Config config = new Config();
        config.setProtocol("https");
        config.setRegionId("central");
        return config;
    }

    public static com.aliyun.dingtalkoauth2_1_0.Client getOauth2Client() throws Exception {
        return new com.aliyun.dingtalkoauth2_1_0.Client(DingTalkConfiguration.getConfig());
    }

    public static com.aliyun.dingtalkcontact_1_0.Client getContactClient() throws Exception {
        return new com.aliyun.dingtalkcontact_1_0.Client(DingTalkConfiguration.getConfig());
    }

    public static com.aliyun.dingtalkcheck_in_1_0.Client getCheckInClient() throws Exception {
        return new com.aliyun.dingtalkcheck_in_1_0.Client(DingTalkConfiguration.getConfig());
    }

    public static com.aliyun.dingtalkworkflow_1_0.Client getWorkflowClient() throws Exception {
        return new com.aliyun.dingtalkworkflow_1_0.Client(DingTalkConfiguration.getConfig());
    }
}
