package com.rcszh.external.server.dingtalk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum OAComponentTypeEnum {
    TextField("TextField"),
    TextareaField("TextareaField"),
    NumberField("NumberField"),
    DDDateField("DDDateField"),
    MoneyField("MoneyField"),
    TableField("TableField"),
    InnerContactField("InnerContactField");
    private final String value;

    /**
     * 根据值找到枚举
     */
    public static OAComponentTypeEnum fromValue(String value) {
        return Arrays.stream(OAComponentTypeEnum.values()).filter(item -> item.value.equals(value)).findFirst().orElse(null);
    }
}
