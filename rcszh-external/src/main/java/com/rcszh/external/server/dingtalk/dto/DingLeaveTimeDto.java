package com.rcszh.external.server.dingtalk.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rcszh.common.constant.Constants;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class DingLeaveTimeDto {
    private static final long serialVersionUID = 1L;

    private String userid;

    /**
     * 统计日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date statDay;

    private BigDecimal duration;

    private String type;

    /**
     * 假期名称
     */
    private String name;

}
