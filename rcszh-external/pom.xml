<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.rcszh</groupId>
        <artifactId>pms</artifactId>
        <version>0.0.1</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>rcszh-external</artifactId>
    <version>0.0.1</version>
    <name>对外服务</name>
    <description>
        对外服务：企业微信调用
    </description>

    <dependencies>
<!--        <dependency>-->
<!--            <groupId>com.rcszh</groupId>-->
<!--            <artifactId>rcszh-common</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.rcszh</groupId>
            <artifactId>rcszh-base-common</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hchub</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-cp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>dingtalk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>alibaba-dingtalk-service-sdk</artifactId>
            <version>2.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.rcszh</groupId>
            <artifactId>rcszh-common</artifactId>
            <version>0.0.1</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>commons-dbutils</groupId>
            <artifactId>commons-dbutils</artifactId>
            <version>1.7</version>
        </dependency>
    </dependencies>
</project>
