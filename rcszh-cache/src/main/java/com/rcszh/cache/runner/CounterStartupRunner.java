package com.rcszh.cache.runner;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.rcszh.cache.domain.PersistentCounter;
import com.rcszh.cache.service.CacheService;
import com.rcszh.cache.service.IPersistentCounterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 项目启动初始化
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class CounterStartupRunner implements ApplicationRunner {

    @Autowired
    private IPersistentCounterService persistentCounterService;

    @Autowired
    private CacheService cacheService;

    @Override
    public void run(ApplicationArguments args) {
        //清理数据
        persistentCounterService.clearExpired();
        //预缓存
        List<PersistentCounter> list = persistentCounterService.list();
        LocalDateTime now = LocalDateTime.now();
        for (PersistentCounter counter : list) {
            if (Objects.isNull(counter.getExpireTime())) {
                cacheService.incrInternalInit(counter.getCounterKey(), counter.getCounterValue(), null);
            } else if (counter.getExpireTime().isAfter(LocalDateTime.now())) {
                long expireTime = DateUtil.between(Convert.toDate(now), Convert.toDate(counter.getExpireTime()), DateUnit.SECOND);
                cacheService.incrInternalInit(counter.getCounterKey(), counter.getCounterValue(), expireTime);
            }
        }
        log.info("本地计数器预加载完成，数量：{}", list.size());
    }
}