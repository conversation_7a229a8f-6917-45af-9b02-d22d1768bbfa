package com.rcszh.cache.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("persistent_counter")
public class PersistentCounter {

    @TableId
    private Long id;

    /**
     * 缓存键
     */
    private String counterKey;

    /**
     * 缓存值
     */
    private Long counterValue;

    /**
     * 缓存过期时间
     */
    private LocalDateTime expireTime;

    /**
     * 缓存更新时间
     */
    private LocalDateTime updatedTime;
}
