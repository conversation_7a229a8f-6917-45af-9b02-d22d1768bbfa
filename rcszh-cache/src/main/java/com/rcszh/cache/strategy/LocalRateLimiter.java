package com.rcszh.cache.strategy;

import com.google.common.util.concurrent.RateLimiter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;

/**
 * 本地限流策略（基于Guava RateLimiter）
 */
@Component
public class LocalRateLimiter implements RateLimiterStrategy {
    private static final Logger log = LoggerFactory.getLogger(LocalRateLimiter.class);

    // 存储不同key的RateLimiter实例
    private final ConcurrentHashMap<String, RateLimiter> rateLimiterMap = new ConcurrentHashMap<>();

    @Override
    public boolean tryAcquire(String key, int count, int time) {
        // 计算每秒允许的请求数（QPS = count / time）
        double qps = (double) count / time;

        // 获取或创建RateLimiter实例
        RateLimiter rateLimiter = rateLimiterMap.computeIfAbsent(key, k -> RateLimiter.create(qps));

        // 尝试获取许可
        boolean acquired = rateLimiter.tryAcquire();
        if (!acquired) {
            log.warn("本地限流触发，key: {}", key);
        }
        return acquired;
    }
}