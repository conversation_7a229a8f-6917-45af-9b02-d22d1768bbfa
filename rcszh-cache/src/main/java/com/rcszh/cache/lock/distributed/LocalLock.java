package com.rcszh.cache.lock.distributed;

import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 本地锁实现（基于 ReentrantLock）
 *
 * <AUTHOR>
 */
@Component
public class LocalLock implements DistributedLock {
    // 存储所有锁对象（线程安全）
    private final ConcurrentHashMap<String, ReentrantLock> lockMap = new ConcurrentHashMap<>();

    @Override
    public boolean tryLock(String key) {
        ReentrantLock lock = lockMap.computeIfAbsent(key, k -> new ReentrantLock());
        return lock.tryLock();
    }

    @Override
    public boolean tryLock(String key, long timeout) {
        ReentrantLock lock = lockMap.computeIfAbsent(key, k -> new ReentrantLock());
        try {
            return lock.tryLock(timeout, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt(); // 恢复中断状态
            return false;
        }
    }

    @Override
    public boolean tryLock(String key, long timeout, TimeUnit unit) {
        ReentrantLock lock = lockMap.computeIfAbsent(key, k -> new ReentrantLock());
        try {
            return lock.tryLock(timeout, unit);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt(); // 恢复中断状态
            return false;
        }
    }


    @Override
    public void unlock(String key) {
        ReentrantLock lock = lockMap.get(key);
        if (lock != null) {
            if (!lock.isHeldByCurrentThread()) {
                throw new IllegalMonitorStateException("当前线程未持有锁");
            }
            lock.unlock();
            lockMap.remove(key);
        }
    }

    @Override
    public boolean isLocked(String key) {
        ReentrantLock lock = lockMap.get(key);
        return lock != null && lock.isLocked();
    }

    @Override
    public boolean isHeldByCurrentThread(String key) {
        ReentrantLock lock = lockMap.get(key);
        return lock != null && lock.isHeldByCurrentThread();
    }
}