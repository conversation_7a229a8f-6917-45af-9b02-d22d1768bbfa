package com.rcszh.cache.lock.distributed;

import java.util.concurrent.TimeUnit;

/**
 * 分布式锁接口
 */
public interface DistributedLock {

    /**
     * 尝试获取锁--非阻塞
     *
     * @param key 锁的 key
     * @return 是否获取成功
     */
    boolean tryLock(String key);

    /**
     * 尝试获取锁--这里就是等待时间,单位（默认秒）,阻塞，时间到了还获取不到锁，就返回false
     *
     * @param key     锁的 key
     * @param timeout 超时时间（秒）
     * @return 是否获取成功
     */
    boolean tryLock(String key, long timeout);

    /**
     * 尝试获取锁--这里就是等待时间,单位,阻塞，时间到了还获取不到锁，就返回false
     *
     * @param key     锁的 key
     * @param timeout 超时时间
     * @param unit    时间单位
     * @return 是否获取成功
     */
    boolean tryLock(String key, long timeout, TimeUnit unit);

    /**
     * 释放锁
     *
     * @param key 锁的 key
     */
    void unlock(String key);

    /**
     * 检查锁的状态
     *
     * @param key
     * @return
     */
    boolean isLocked(String key);

    /**
     * 检查当前线程是否持有锁
     *
     * @param key
     * @return
     */
    boolean isHeldByCurrentThread(String key);
}