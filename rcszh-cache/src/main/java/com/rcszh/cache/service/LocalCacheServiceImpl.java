package com.rcszh.cache.service;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.rcszh.cache.util.JsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
public class LocalCacheServiceImpl implements CacheService {

    private final Map<String, Cache<String, String>> localCacheMap = new ConcurrentHashMap<>();
    private final Integer DEFAULT_MAX_SIZE = 10000;
    private final Cache<String, String> defaultCache = Caffeine.newBuilder()
            .expireAfterWrite(60, TimeUnit.MINUTES)
            .maximumSize(DEFAULT_MAX_SIZE)
            .build();
    @Autowired
    private IPersistentCounterService persistentCounterService;

    @Override
    public synchronized void set(String key, Object value) {
        Cache<String, String> tempCache = Caffeine.newBuilder()
                .maximumSize(DEFAULT_MAX_SIZE)
                .build();
        tempCache.put(key, JsonUtil.toJson(value));
        localCacheMap.put(key, tempCache);
    }

    @Override
    public synchronized void set(String key, Object value, long expire) {
        set(key, value, expire, TimeUnit.SECONDS);
    }

    @Override
    public synchronized void set(String key, Object value, long expire, TimeUnit unit) {
        Cache<String, String> tempCache = Caffeine.newBuilder()
                .expireAfterWrite(expire, unit)
                .maximumSize(DEFAULT_MAX_SIZE)
                .build();
        tempCache.put(key, JsonUtil.toJson(value));
        localCacheMap.put(key, tempCache);
    }

    @Override
    public synchronized void batchSet(Map<String, String> valueMap, long expire) {
        for (Map.Entry<String, String> entry : valueMap.entrySet()) {
            set(entry.getKey(), entry.getValue(), expire);
        }
    }

    @Override
    public synchronized Boolean hasKey(String key) {
        return get(key) != null;
    }

    @Override
    public synchronized String get(String key) {
        Cache<String, String> cache = localCacheMap.get(key);
        // 再次检查 cache 是否为 null，防止 getCache() 返回 null
        if (cache != null) {
            String value = cache.getIfPresent(key);
            if (value == null) {
                localCacheMap.remove(key);
            }
            return value;
        } else {
            // 处理 cache 为 null 的情况，比如返回 null 或抛出自定义异常
            return null;
        }
    }

    @Override
    public synchronized Integer getToInt(String key) {
        return Convert.toInt(get(key));
    }

    @Override
    public synchronized <T> T get(String key, Class<T> clazz) {
        String json = get(key);
        return JsonUtil.fromJson(json, clazz);
    }

    @Override
    public synchronized <T> Map<String, T> batchGet(List<String> keyList, Class<T> clazz) {
        Map<String, T> result = new LinkedHashMap<>();
        for (String key : keyList) {
            result.put(key, get(key, clazz));
        }
        return result;
    }

    @Override
    public synchronized void delete(String key) {
        Cache<String, String> cache = localCacheMap.get(key);
        if (Objects.isNull(cache)) {
            return;
        }
        cache.invalidate(key);
        localCacheMap.remove(key);
    }

    @Override
    public synchronized boolean delete(Set<String> keys) {
        boolean changed = false;
        for (String key : keys) {
            delete(key);
            changed = true;
        }
        return changed;
    }

    @Override
    public Long incr(String key) {
        return incrInternal(key, 1L, null);
    }

    @Override
    public Long incr(String key, Long expireTime) {
        return incrInternal(key, 1L, expireTime);
    }

    @Override
    public Long incr(String key, Long value, Long expireTime) {
        return incrInternal(key, value, expireTime);
    }

    private synchronized Long incrInternal(String key, Long delta, Long expireTimeSeconds) {
        // 1. 从缓存读取原始值
        String cacheValue = get(key);
        Long newVal = cacheValue != null ? Convert.toLong(cacheValue) + 1 : delta;

        // 3. 写回缓存
        if (expireTimeSeconds != null && expireTimeSeconds > 0) {
            set(key, newVal, expireTimeSeconds);
        } else {
            set(key, newVal);
        }
        persistentCounterService.saveCounter(key, newVal, expireTimeSeconds);
        return newVal;
    }

    @Override
    public synchronized Long incrInternalInit(String key, Long delta, Long expireTimeSeconds) {
        // 1. 从缓存读取原始值
        String cacheValue = get(key);
        Long newVal = cacheValue != null ? Convert.toLong(cacheValue) + 1 : delta;

        // 2. 写回缓存
        if (expireTimeSeconds != null && expireTimeSeconds > 0) {
            set(key, newVal, expireTimeSeconds);
        } else {
            set(key, newVal);
        }
        return newVal;
    }


    @Override
    //TODO 有问题
    public synchronized Set<String> keys(String pattern) {
        pattern = pattern.replace("*", "");
        if (StrUtil.isBlank(pattern)) {
            return localCacheMap.keySet();
        }
        String finalPattern = pattern;
        return localCacheMap.keySet().stream().filter(key -> key.contains(finalPattern)).collect(Collectors.toSet());
    }

    @Override
    public Map<String, Object> getCacheProperties() {
        throw new RuntimeException("获取缓存属性不支持本地缓存");
    }

}
