package com.rcszh.cache.service;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rcszh.cache.domain.PersistentCounter;
import com.rcszh.cache.mapper.PersistentCounterMapper;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class PersistentCounterServiceImpl extends ServiceImpl<PersistentCounterMapper, PersistentCounter> implements IPersistentCounterService {

    @Override
    public void clearExpired() {
        this.remove(
                new LambdaQueryWrapper<PersistentCounter>()
                        .le(PersistentCounter::getExpireTime, DateUtil.formatDateTime(DateUtil.date()))
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveCounter(String key, Long value, Long expireTimeSeconds) {
        // 4. 计算数据库过期时间
        DateTime expireAt = (expireTimeSeconds != null && expireTimeSeconds > 0)
                ? DateUtil.offsetSecond(new Date(), Convert.toInt(expireTimeSeconds))
                : null;

        // 5. 持久化更新数据库
        PersistentCounter entity = this.getOne(
                new LambdaQueryWrapper<PersistentCounter>()
                        .eq(PersistentCounter::getCounterKey, key)
                        .isNull(Objects.isNull(expireAt), PersistentCounter::getExpireTime)
                        .ge(Objects.nonNull(expireAt), PersistentCounter::getExpireTime, DateUtil.formatDateTime(expireAt))
        );

        if (entity == null) {
            entity = new PersistentCounter();
        }
        entity.setCounterKey(key);
        entity.setCounterValue(value);
        entity.setExpireTime(Convert.toLocalDateTime(expireAt));
        entity.setUpdatedTime(LocalDateTime.now());
        this.saveOrUpdate(entity);
    }
}
