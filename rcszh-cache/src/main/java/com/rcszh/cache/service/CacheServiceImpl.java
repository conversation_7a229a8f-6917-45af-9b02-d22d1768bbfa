//package com.rcszh.cache.service.impl;
//
//import com.rcszh.cache.service.CacheService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.context.ApplicationContext;
//import org.springframework.context.annotation.Primary;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//import java.util.Map;
//import java.util.Set;
//import java.util.concurrent.TimeUnit;
//
///**
// * 缓存实现类
// * <AUTHOR>
// */
//@Component
//@Primary
//public class CacheServiceImpl implements CacheService {
//
//    @Value("${cache.type:redis}")
//    private String cacheType;
//
//    @Autowired
//    private ApplicationContext applicationContext;
//
//    private CacheService cacheService;
//
//    /**
//     * 获取缓存实例
//     * @return
//     */
//    private CacheService getInstance() {
//        if (this.cacheService == null) {
//            this.cacheService = (CacheService) this.applicationContext.getBean(this.cacheType + getClass().getSimpleName());
//        }
//        return this.cacheService;
//    }
//
//    @Override
//    public void set(String key, Object value) {
//        getInstance().set(key, value);
//    }
//
//    @Override
//    public void set(String key, Object value, long expire) {
//        getInstance().set(key, value, expire);
//    }
//
//    @Override
//    public void set(String key, Object value, long expire, TimeUnit unit) {
//        getInstance().set(key, value, expire, unit);
//    }
//
//    @Override
//    public void batchSet(Map<String, String> valueMap, long expire) {
//        getInstance().batchSet(valueMap, expire);
//    }
//
//    @Override
//    public Boolean hasKey(String key) {
//        return getInstance().hasKey(key);
//    }
//
//    @Override
//    public String get(String key) {
//        return getInstance().get(key);
//    }
//
//    @Override
//    public Integer getToInt(String key) {
//        return getInstance().getToInt(key);
//    }
//
//    @Override
//    public <T> T get(String key, Class<T> clazz) {
//        return getInstance().get(key, clazz);
//    }
//
//    @Override
//    public <T> Map<String, T> batchGet(List<String> keyList, Class<T> clazz) {
//        return getInstance().batchGet(keyList, clazz);
//    }
//
//    @Override
//    public void delete(String key) {
//        getInstance().delete(key);
//    }
//
//    @Override
//    public boolean delete(Set<String> keys) {
//        return getInstance().delete(keys);
//    }
//
//    @Override
//    public Long incr(String key) {
//        return getInstance().incr(key);
//    }
//
//    @Override
//    public void incr(String key, Integer value, long expire) {
//        getInstance().incr(key, value, expire);
//    }
//
//    @Override
//    public Long incr(String key, Integer expireTime) {
//        return getInstance().incr(key, expireTime);
//    }
//
//    @Override
//    public Set<String> keys(String pattern) {
//        return getInstance().keys(pattern);
//    }
//
//    @Override
//    public Map<String, Object> getCacheProperties() {
//        return getInstance().getCacheProperties();
//    }
//}
