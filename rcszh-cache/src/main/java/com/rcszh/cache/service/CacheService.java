package com.rcszh.cache.service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;


public interface CacheService {

    /**
     * 缓存数据
     *
     * @param key
     * @param value
     */
    void set(String key, Object value);

    /**
     * 缓存数据--可设置有效时间
     *
     * @param key
     * @param value
     * @param expire
     */
    void set(String key, Object value, long expire);

    /**
     * 缓存数据--可设置有效时间和时间单位
     *
     * @param key
     * @param value
     * @param expire
     * @param unit
     */
    void set(String key, Object value, long expire, TimeUnit unit);

    /**
     * 批量缓存数据--可设置有效时间
     *
     * @param valueMap
     * @param expire
     */
    void batchSet(Map<String, String> valueMap, long expire);

    /**
     * 检查缓存数据是否存在
     *
     * @param key
     * @return
     */
    Boolean hasKey(String key);

    /**
     * 获取缓存数据
     *
     * @param key
     * @return
     */
    String get(String key);

    /**
     * 获取缓存数据
     *
     * @param key
     * @return
     */
    Integer getToInt(String key);

    /**
     * 获取缓存数据
     *
     * @param key
     * @param clazz
     * @param <T>
     * @return
     */
    <T> T get(String key, Class<T> clazz);

    /**
     * 批量获取缓存数据
     *
     * @param keyList
     * @param clazz
     * @param <T>
     * @return
     */
    <T> Map<String, T> batchGet(List<String> keyList, Class<T> clazz);

    /**
     * 删除缓存数据
     *
     * @param key
     */
    void delete(String key);

    /**
     * 批量删除缓存数据
     *
     * @param keys
     * @return
     */
    boolean delete(Set<String> keys);

    /**
     * 自增默认从1开始
     *
     * @param key
     * @return
     */
    Long incr(String key);

    /**
     * 自增,并设置值并设置值带有效时间
     *
     * @param key
     * @param value
     * @param expireTime
     */
    Long incr(String key, Long value, Long expireTime);

    /**
     * 自增并设置值带有效时间
     *
     * @param key
     * @param expireTime
     * @return
     */
    Long incr(String key, Long expireTime);

    /**
     * 自增,并设置值并设置值带有效时间
     *
     * @param key
     * @param delta
     * @param expireTimeSeconds
     */
    Long incrInternalInit(String key, Long delta, Long expireTimeSeconds);

    /**
     * 指定模式获取所有缓存数据
     *
     * @param pattern
     * @return
     */
    Set<String> keys(String pattern);

    /**
     * 获取缓存配置
     *
     * @return
     */
    Map<String, Object> getCacheProperties();
}
