package com.rcszh.cache.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.rcszh.cache.domain.PersistentCounter;

/**
 * <AUTHOR>
 */
public interface IPersistentCounterService extends IService<PersistentCounter> {

    /**
     * 删除过期的计数器
     */
    void clearExpired();

    /**
     * 保存计数器
     *
     * @param key
     * @param value
     * @param expireTimeSeconds
     */
    void saveCounter(String key, Long value, Long expireTimeSeconds);
}
