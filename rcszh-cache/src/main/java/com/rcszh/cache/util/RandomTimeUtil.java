package com.rcszh.cache.util;

import cn.hutool.core.util.RandomUtil;

import java.util.Calendar;

public class RandomTimeUtil {

    /**
     * 获取随机时间
     * 默认从当前时间后1天开始，随机时间范围为30天
     * 单位：秒
     *
     * @return
     */
    public static long getRandomTime() {
        return getRandomTime(1, 30);
    }

    /**
     * 获取随机时间
     *
     * @param afterDays 从当前时间后多少天开始
     * @param rangeDay  随机时间范围
     * @return
     */
    public static Long getRandomTime(int afterDays, int rangeDay) {
        Calendar calendar = Calendar.getInstance();
        long curTime = calendar.getTimeInMillis();
        calendar.add(Calendar.DAY_OF_MONTH, afterDays);
        long minTime = calendar.getTimeInMillis();
        calendar.add(Calendar.DAY_OF_MONTH, rangeDay);
        long maxTime = calendar.getTimeInMillis();
        long randomTime = RandomUtil.randomLong(minTime, maxTime);
        return (randomTime - curTime) / 1000L;
    }
}
