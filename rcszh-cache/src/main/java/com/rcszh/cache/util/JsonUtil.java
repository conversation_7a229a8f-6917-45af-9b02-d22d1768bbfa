package com.rcszh.cache.util;

import cn.hutool.core.convert.Convert;
import com.google.gson.Gson;

public class JsonUtil {

    /**
     * json转对象
     *
     * @param json
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> T from<PERSON>son(String json, Class<T> clazz) {
        if (clazz == String.class) {
            return (T) json;
        }
        return (new Gson()).fromJson(json, clazz);
    }

    /**
     * 对象转json
     *
     * @param object
     * @return
     */
    public static String toJson(Object object) {
        return !(object instanceof Integer) && !(object instanceof Long) && !(object instanceof Float) && !(object instanceof Double) && !(object instanceof Boolean) && !(object instanceof Character) && !(object instanceof String) ? (new Gson()).toJson(object) : Convert.toStr(object);
    }
}
