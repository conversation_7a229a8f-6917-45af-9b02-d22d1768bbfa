package com.rcszh.api;

import cn.hutool.json.JSONObject;
import com.rcszh.lowcode.core.annotations.ApiBean;
import com.rcszh.lowcode.feign_api.MessageTemplateInterface;
import com.rcszh.lowcode.feign_api.entity.message.FormMsgLog;
import com.rcszh.lowcode.feign_api.entity.message.SendMessageParam;
import com.rcszh.message.domain.MsgLog;
import com.rcszh.message.enums.MsgTypeEnum;
import com.rcszh.message.service.ISysMessageLogService;
import com.rcszh.message.service.ISysMessageService;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@ApiBean(value = "MsgLogFeign", registerInterfaces = {MessageTemplateInterface.class})
public class MsgLogFeign implements MessageTemplateInterface {
    @Resource
    private ISysMessageLogService sysMessageLogService;
    @Resource
    private ISysMessageService sysMessageService;

    @Override
    public FormMsgLog getFormMsgLogById(String id) {
        MsgLog msgLog = sysMessageLogService.getById(id);
        FormMsgLog formMsgLog = new FormMsgLog();
        BeanUtils.copyProperties(msgLog,formMsgLog);
        return formMsgLog;
    }

    @Override
    public void sendWaitMessage(SendMessageParam param) {
        List<Long> receiverUserIds = param.getReceiverUserIds();
        JSONObject params = param.getParams();
        sysMessageService.sendMessage(MsgTypeEnum.waiting.getCode(), "代办提醒", "审批待办提醒", receiverUserIds, params);
    }
}
