package com.rcszh.api;

import com.rcszh.lowcode.core.annotations.ApiBean;
import com.rcszh.lowcode.feign_api.entity.FormDept;
import com.rcszh.lowcode.feign_api.DeptTemplateInterface;
import com.rcszh.system.service.ISysDeptService;
import com.rcszh.base.common.core.domain.entity.SysDept;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Component
@ApiBean(value = "DeptFeign", registerInterfaces = {DeptTemplateInterface.class})
public class DeptFeign implements DeptTemplateInterface {
    @Resource
    private ISysDeptService sysDeptService;

    @Override
    public FormDept getDeptById(Long id) {
        SysDept sysDept = sysDeptService.selectDeptById(id);
        if (Objects.isNull(sysDept)) {
            return null;
        }
        FormDept formDept = new FormDept();
        BeanUtils.copyProperties(sysDept, formDept);
        return formDept;
    }

    @Override
    public List<FormDept> getDeptListByIds(List<Long> ids) {
        List<SysDept> sysDepts = sysDeptService.selectDeptByIds(ids);
        List<FormDept> formDepts = new ArrayList<>();
        for (SysDept sysDept : sysDepts) {
            FormDept formDept = new FormDept();
            BeanUtils.copyProperties(sysDept, formDept);
            formDepts.add(formDept);
        }
//        ListCheckUtil.checkSize(formDepts,ids,"机构信息不存在");
        return formDepts;
    }
}
