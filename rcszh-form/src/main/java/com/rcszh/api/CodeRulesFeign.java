package com.rcszh.api;

import com.rcszh.basic.service.ISysCodeRulesService;
import com.rcszh.lowcode.core.annotations.ApiBean;
import com.rcszh.lowcode.feign_api.CodeRulesTemplateInterface;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@ApiBean(value = "CodeRulesFeign", registerInterfaces = {CodeRulesTemplateInterface.class})
public class CodeRulesFeign implements CodeRulesTemplateInterface {

    @Autowired
    private ISysCodeRulesService sysCodeRulesService;

    @Override
    public String generateCode(String code) {
        return sysCodeRulesService.generateCode(code);
    }
}
