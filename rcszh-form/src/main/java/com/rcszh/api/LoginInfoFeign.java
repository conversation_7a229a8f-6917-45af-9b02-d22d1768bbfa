package com.rcszh.api;


import com.rcszh.base.common.utils.SecurityUtils;
import com.rcszh.lowcode.core.annotations.ApiBean;
import com.rcszh.lowcode.feign_api.LoginInfoTemplateInterface;
import org.springframework.stereotype.Component;

@Component
@ApiBean(value = "LoginInfoFeign", registerInterfaces = {LoginInfoTemplateInterface.class})
public class LoginInfoFeign implements LoginInfoTemplateInterface {
    @Override
    public String getLoginUserId() {
        String userId = "-1";
        try {
            userId = String.valueOf(SecurityUtils.getUserId());
        } catch (Exception ignored) {
        }
        return userId;
    }
}
