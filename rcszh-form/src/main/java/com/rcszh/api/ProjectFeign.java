package com.rcszh.api;

import com.rcszh.base.common.utils.MessageUtils;
import com.rcszh.common.annotation.ProjectConfig;
import com.rcszh.lowcode.core.annotations.ApiBean;
import com.rcszh.lowcode.feign_api.ProjectTemplateInterface;
import com.rcszh.lowcode.feign_api.entity.FormProject;
import com.rcszh.lowcode.feign_api.entity.FormProjectTask;
import com.rcszh.project.domain.PmsProject;
import com.rcszh.project.domain.PmsProjectTask;
import com.rcszh.project.service.impl.PmsProjectExtServiceImpl;
import com.rcszh.project.service.impl.PmsProjectServiceImpl;
import com.rcszh.project.service.impl.PmsProjectTaskServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

@Component
@ApiBean(value = "ProjectFeign", registerInterfaces = {ProjectTemplateInterface.class})
public class ProjectFeign implements ProjectTemplateInterface {

    @Resource
    private PmsProjectTaskServiceImpl pmsProjectTaskService;
    @Resource
    private PmsProjectServiceImpl pmsProjectService;

    @Resource
    private PmsProjectExtServiceImpl pmsProjectExtService;

    @Override
    public FormProject getProjectById(Long id) {
        PmsProject project = pmsProjectExtService.findById(id);
        if (Objects.isNull(project)) {
            return null;
        }
        FormProject formProject = new FormProject();
        BeanUtils.copyProperties(project, formProject);
        return formProject;
    }

    @Override
    public List<FormProject> getProjectListByIds(List<Long> ids) {
        List<PmsProject> pmsProjectTasks = pmsProjectService.findByIds(ids);
        List<FormProject> formProjects = new ArrayList<>();
        for (PmsProject pmsProjectTask : pmsProjectTasks) {
            FormProject formProject = new FormProject();
            BeanUtils.copyProperties(pmsProjectTask, formProject);
            formProjects.add(formProject);
        }
        return formProjects;
    }

    @Override
    public FormProjectTask getProjectTaskById(Long id) {
        PmsProjectTask pmsProjectTask = pmsProjectTaskService.getById(id);
        FormProjectTask formProjectTask = new FormProjectTask();
        BeanUtils.copyProperties(pmsProjectTask, formProjectTask);
        return formProjectTask;
    }

    @Override
    public List<FormProjectTask> getProjectTaskListByIds(List<Long> ids) {
        List<PmsProjectTask> pmsProjectTasks = pmsProjectTaskService.listByIds(ids);
        List<FormProjectTask> formProjectTasks = new ArrayList<>();
        for (PmsProjectTask pmsProjectTask : pmsProjectTasks) {
            FormProjectTask formProjectTask = new FormProjectTask();
            BeanUtils.copyProperties(pmsProjectTask, formProjectTask);
            formProjectTasks.add(formProjectTask);
        }
//        ListCheckUtil.checkSize(formProjectTasks,ids,"项目信息不存在");
        return formProjectTasks;
    }

    @Override
    public List<HashMap<String, Object>> getProjectConfig() {
        List<HashMap<String, Object>> result = new ArrayList<>();
        Class<PmsProject> pmsProjectClass = PmsProject.class;
        Field[] declaredFields = pmsProjectClass.getDeclaredFields();
        for (Field declaredField : declaredFields) {
            declaredField.setAccessible(true);
            if (declaredField.isAnnotationPresent(ProjectConfig.class)) {
                ProjectConfig projectConfig = declaredField.getAnnotation(ProjectConfig.class);
                HashMap<String, Object> map = new HashMap<>();
                map.put("label", MessageUtils.messageKey(projectConfig.name()));
                map.put("value", camelToSnakeOptimized(declaredField.getName()));
                map.put("type", projectConfig.type());
                result.add(map);
            }
        }
        return result;
    }

    public static String camelToSnakeOptimized(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }

        StringBuilder result = new StringBuilder();
        result.append(Character.toLowerCase(str.charAt(0)));

        for (int i = 1; i < str.length(); i++) {
            char ch = str.charAt(i);
            if (Character.isUpperCase(ch)) {
                // 检查前一个字符是否是小写（避免全大写单词重复加下划线）
                if (i > 0 && Character.isLowerCase(str.charAt(i - 1))) {
                    result.append('_');
                }
                result.append(Character.toLowerCase(ch));
            } else {
                result.append(ch);
            }
        }

        return result.toString();
    }

}
