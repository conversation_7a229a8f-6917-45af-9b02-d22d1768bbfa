package com.rcszh.api;

import com.rcszh.basic.service.IPmsCustomerService;
import com.rcszh.lowcode.core.annotations.ApiBean;
import com.rcszh.lowcode.feign_api.CustomerTemplateInterface;
import com.rcszh.lowcode.feign_api.entity.FormCustomer;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
@ApiBean(value = "CustomerFeign", registerInterfaces = {CustomerTemplateInterface.class})
public class CustomerFeign implements CustomerTemplateInterface {
    @Resource
    private IPmsCustomerService pmsCustomerService;


    @Override
    public List<FormCustomer> getFormCustomersById(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            return new ArrayList<>();
        }
        List<FormCustomer> result = pmsCustomerService.listByIds(ids).stream().map(item -> {
            FormCustomer formCustomer = new FormCustomer();
            BeanUtils.copyProperties(item, formCustomer);
            return formCustomer;
        }).collect(Collectors.toList());
//        ListCheckUtil.checkSize(result,ids,"客户信息不存在");
        return result;
    }

}
