package com.rcszh.api.utils;

import java.util.List;

public class ListCheckUtil {
    /**
     * 校验两个列表的大小是否相等
     *
     * @param list1    列表1
     * @param list2    列表2
     * @param errorMsg 不匹配时的错误信息
     */
    public static void checkSize(List<?> list1, List<?> list2, String errorMsg) {
        if (list1 != null && list2 != null && list1.size() != list2.size()) {
            throw new RuntimeException(errorMsg);
        }
    }
}
