package com.rcszh.api;

import com.rcszh.lowcode.core.annotations.ApiBean;
import com.rcszh.lowcode.feign_api.entity.FormDict;
import com.rcszh.lowcode.feign_api.DictTemplateInterface;
import com.rcszh.system.service.ISysDictTypeService;
import com.rcszh.base.common.core.domain.entity.SysDictData;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@ApiBean(value = "DictFeign", registerInterfaces = {DictTemplateInterface.class})
public class DictFeign implements DictTemplateInterface {
    @Resource
    private ISysDictTypeService sysDictTypeService;

    @Override
    public List<FormDict> getDictListByType(String type) {
        List<SysDictData> dictData = sysDictTypeService.selectDictDataByType(type);
        List<FormDict> formDictList = new ArrayList<>();
        for (SysDictData sysDictData : dictData) {
            FormDict formDict = new FormDict();
            BeanUtils.copyProperties(sysDictData, formDict);
            formDictList.add(formDict);
        }
        return formDictList;
    }
}
