package com.rcszh.api;

import com.rcszh.cache.SimpleObjCacheDto;
import com.rcszh.lowcode.core.annotations.ApiBean;
import com.rcszh.lowcode.feign_api.entity.FormUser;
import com.rcszh.lowcode.feign_api.UserTemplateInterface;
import com.rcszh.basic.service.ISysCommonService;
import com.rcszh.base.common.core.domain.entity.SysUser;
import com.rcszh.base.common.utils.spring.SpringUtils;
import com.rcszh.system.service.ISysUserService;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@ApiBean(value = "UserFeign", registerInterfaces = {UserTemplateInterface.class})
public class UserFeign implements UserTemplateInterface {
    @Resource
    private ISysCommonService sysCommonService;
    @Resource
    private ISysUserService sysUserService;

    @Override
    public List<FormUser> getUserByIds(List<Long> userIdSet) {
        Object bean = SpringUtils.getBean("cacheUtil");
        Method method = null;
        try {
            method = bean.getClass().getMethod("findUserByIds", Collection.class);
            List<SimpleObjCacheDto> list = (List<SimpleObjCacheDto>) method.invoke(bean, userIdSet);
            List<FormUser> result = list.stream().map(item -> item.getObj().toBean(FormUser.class)).collect(Collectors.toList());
//            ListCheckUtil.checkSize(result,userIdSet,"用户信息不存在");
            return result;
        } catch (NoSuchMethodException | InvocationTargetException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public FormUser getUserById(Long id) {
        SysUser sysUser = sysUserService.selectUserById(id);
        if (Objects.isNull(sysUser)) {
            return null;
        }
        FormUser formUser = new FormUser();
        BeanUtils.copyProperties(sysUser, formUser);
        return formUser;
    }

}
