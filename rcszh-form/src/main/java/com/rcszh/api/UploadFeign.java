package com.rcszh.api;

import com.rcszh.lowcode.core.annotations.ApiBean;
import com.rcszh.lowcode.feign_api.entity.FormFile;
import com.rcszh.lowcode.feign_api.UploadTemplateInterface;
import com.rcszh.basic.domain.PublicAttachment;
import com.rcszh.basic.service.PublicAttachmentService;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
@ApiBean(value = "UploadFeign", registerInterfaces = {UploadTemplateInterface.class})
public class UploadFeign implements UploadTemplateInterface {
    @Resource
    private PublicAttachmentService publicAttachmentService;

    @Override
    public List<FormFile> getUploadFileList(List<String> fileIds) {
        List<PublicAttachment> publicAttachments = publicAttachmentService.listByIds(fileIds);
        List<FormFile> formFiles = new ArrayList<>();
        for (PublicAttachment publicAttachment : publicAttachments) {
            FormFile formFile = new FormFile();
            BeanUtils.copyProperties(publicAttachment, formFile);
            formFiles.add(formFile);
        }
//        ListCheckUtil.checkSize(formFiles,fileIds,"文件信息不存在");
        return formFiles;
    }

    @Override
    public FormFile getUploadFile(String fileId) {
        PublicAttachment publicAttachment = publicAttachmentService.getById(fileId);
        FormFile formFile = new FormFile();
        BeanUtils.copyProperties(publicAttachment, formFile);
        return formFile;
    }

    @Override
    public List<String> saveUploadFileList(List<FormFile> files) {
        List<PublicAttachment> publicAttachments = files.stream().map(item -> {
            PublicAttachment publicAttachment = new PublicAttachment();
            BeanUtils.copyProperties(item, publicAttachment);
            return publicAttachment;
        }).collect(Collectors.toList());
        publicAttachmentService.saveOrUpdateBatch(publicAttachments);
        return publicAttachments.stream().map(PublicAttachment::getId).collect(Collectors.toList());
    }

    @Override
    public String saveOrUpdateFile(FormFile file) {
        PublicAttachment publicAttachment = new PublicAttachment();
        BeanUtils.copyProperties(file, publicAttachment);
        publicAttachmentService.saveOrUpdate(publicAttachment);
        return publicAttachment.getId();
    }
}
