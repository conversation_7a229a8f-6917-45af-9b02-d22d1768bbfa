package com.rcszh.api.service;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.rcszh.basic.service.ISysCommonService;
import com.rcszh.system.service.ISysDeptService;
import com.rcszh.base.common.core.domain.entity.SysDept;
import com.rcszh.base.common.core.domain.entity.SysUser;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class MemberInOrOutService {
    @Resource
    private ISysDeptService sysDeptService;

    @Autowired
    private ISysCommonService sysCommonService;

    public boolean belongToCompany(String memberList, Long companyId) {
        JSONArray memberJSONList = JSONUtil.parseArray(memberList);
        List<Long> userIds = new ArrayList<>();
        for (Object item : memberJSONList) {
            JSONObject json = JSONUtil.parseObj(item);
            Long userId = json.getLong("userId");
            userIds.add(userId);
        }
        List<SysUser> sysUsers = sysCommonService.findUserByIds(userIds);
        Map<Long, SysDept> parentDept = sysDeptService.selectAllChildrenDeptById(companyId).stream().collect(Collectors.toMap(SysDept::getDeptId, item -> item));
        for (SysUser user : sysUsers) {
            if (Objects.isNull(user.getDeptId())) {
                continue;
            }
            if (parentDept.containsKey(user.getDeptId()) || user.getDeptId().equals(companyId)) {
                return true;
            }
        }
        return false;
    }

}
