package com.rcszh.api.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.rcszh.base.common.core.domain.entity.SysUser;
import com.rcszh.basic.domain.PmsConfig;
import com.rcszh.basic.enums.SysExpectConfigEnum;
import com.rcszh.message.domain.ExpectWorkTimeMsgParam;
import com.rcszh.message.enums.MsgTypeEnum;
import com.rcszh.message.service.ISysMessageService;
import com.rcszh.project.domain.PmsProject;
import com.rcszh.project.service.IPmsProjectExtService;
import com.rcszh.project.service.IPmsProjectService;
import com.rcszh.system.service.ISysUserService;
import com.rcszh.worktime.domain.SysWorkRecord;
import com.rcszh.worktime.domain.expect_work.SysExpectProjectWorkTime;
import com.rcszh.worktime.service.ISysExpectWorkTimeService;
import com.rcszh.worktime.service.ISysWorkRecordService;
import jakarta.annotation.Resource;
import org.activiti.engine.delegate.DelegateExecution;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;

@Service
public class FormExpectWorkTimeService {
    @Resource
    private ISysExpectWorkTimeService sysExpectWorkTimeService;
    @Resource
    private ISysWorkRecordService sysWorkRecordService;
    @Resource
    private ISysMessageService sysMessageService;
    @Resource
    private IPmsProjectService pmsProjectService;
    @Resource
    private IPmsProjectExtService pmsProjectExtService;
    @Resource
    private ISysUserService sysUserService;

    public void checkExpectWorkTime(DelegateExecution execution) {
        Object currentRecord = execution.getVariable("currentRecord");
        JSONObject currentRecordJson = JSONUtil.parseObj(currentRecord);
        JSONArray list = currentRecordJson.getJSONArray("work_time_approve_line");
        PmsConfig config = sysExpectWorkTimeService.getConfigByKey(SysExpectConfigEnum.EXPECT_DIFF_THRESHOLD.getValue());
        BigDecimal diffRange = new BigDecimal(config.getConfigValue());
        if (CollUtil.isNotEmpty(list)) {
            Object o = list.getFirst();
            JSONObject object = JSONUtil.parseObj(o);
            Long projectId = object.getLong("project_id");
            Date workTime = object.getDate("work_time");
            Long userId = object.getLong("user_id");
            DateTime start = DateUtil.beginOfMonth(workTime);
            DateTime end = DateUtil.endOfMonth(workTime);
            // 获取项目的预期工作时间
            SysExpectProjectWorkTime projectExpectTime = sysExpectWorkTimeService.getExpectProjectWorkTimeByProjectIdAndDate(projectId, start);
            if (projectExpectTime == null) {
                return;
            }
            Integer expectWorkTime = projectExpectTime.getExpectWorkTime();
            List<SysWorkRecord> sysWorkRecords = sysWorkRecordService.findByProjectAndUser(userId, projectId, start, end);
            Integer sum = sysWorkRecords.stream().map(SysWorkRecord::getWorkDuration).reduce(0, Integer::sum, (l, r) -> l);
            BigDecimal sumDecimal = BigDecimal.valueOf(sum);
            BigDecimal expectDecimal = BigDecimal.valueOf(expectWorkTime);
            BigDecimal diff = sumDecimal.subtract(expectDecimal).divide(expectDecimal, 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
            if (diff.compareTo(diffRange) > 0) {
                // 查询项目、用户
                PmsProject pmsProject = pmsProjectExtService.findById(projectId);
                SysUser sysUser = sysUserService.selectUserById(userId);
                // 超过阈值则发送企业微信消息
                sysMessageService.sendMessage(MsgTypeEnum.notifyExpectWorkTimeWorn.getCode(),
                        MsgTypeEnum.notifyExpectWorkTimeWorn.getTitle(),
                        MsgTypeEnum.notifyExpectWorkTimeWorn.getTitle(),
                        List.of(userId),
                        JSONUtil.parseObj(ExpectWorkTimeMsgParam.builder().userId(userId).projectName(pmsProject.getName()).projectMemberName(sysUser.getNickName()).diffRatio(diff).build()));
            }
        }
    }
}
