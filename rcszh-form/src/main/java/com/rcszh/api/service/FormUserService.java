package com.rcszh.api.service;

import com.rcszh.base.common.core.domain.entity.SysDept;
import com.rcszh.base.common.core.domain.entity.SysUser;
import com.rcszh.basic.service.ISysCommonService;
import com.rcszh.system.service.ISysDeptService;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
public class FormUserService {
    @Resource
    private ISysDeptService sysDeptService;

    @Autowired
    private ISysCommonService sysCommonService;

    /**
     * 判断当前申请人是否属于某个部门
     *
     * @param user
     * @param deptCode
     * @return
     */
    public boolean belongToDeptByCode(String user, String deptCode) {
        SysUser sysUser = sysCommonService.findUserByIds(List.of(Long.valueOf(user))).getFirst();
        Long deptId = sysUser.getDeptId();
        if (Objects.isNull(deptId)) {
            return false;
        }
        SysDept sysDept = sysDeptService.selectDeptById(deptId);
        String[] codes = deptCode.split(",");
        for (String code : codes) {
            if (sysDept.getDeptCode().equals(code)) {
                return true;
            }
        }
        return false;
    }
}
