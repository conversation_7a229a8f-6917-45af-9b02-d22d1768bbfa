package com.rcszh.api.service;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rcszh.equipment.domain.EqptEquipmentWorkRecord;
import com.rcszh.equipment.enums.EqptWorkRecordStatus;
import com.rcszh.equipment.service.IEqptEquipmentWorkRecordService;
import jakarta.annotation.Resource;
import org.activiti.engine.delegate.DelegateExecution;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Component
public class EqptWorkRecordActivityService {
    @Resource
    private IEqptEquipmentWorkRecordService eqptEquipmentWorkRecordService;
    // 驳回设备工时
    @Transactional(rollbackFor = Exception.class)
    public void rejectWorkRecord(DelegateExecution execution){
        changeStatus(execution,EqptWorkRecordStatus.rejected.getCode());
    }

    // 撤回设备工时
    @Transactional(rollbackFor = Exception.class)
    public void cancelWorkRecord(DelegateExecution execution){
        changeStatus(execution,EqptWorkRecordStatus.cancel.getCode());
    }
    // 同意设备工时
    @Transactional(rollbackFor = Exception.class)
    public void agreeWorkRecord(DelegateExecution execution){
        changeStatus(execution,EqptWorkRecordStatus.audited.getCode());
    }

    private void changeStatus(DelegateExecution execution,String status){
        Object currentRecord = execution.getVariable("currentRecord");
        JSONObject currentRecordJson = JSONUtil.parseObj(currentRecord);
        List<Object> line = currentRecordJson.getBeanList("table_eqpt_work_time_approval_line", Object.class);
        for (Object item : line) {
            JSONObject json = JSONUtil.parseObj(item);
            String projectId =  json.getStr("project_id");
            String equipmentId =  json.getStr("equipment_id");
            Date workTime =  json.getDate("work_time");
            EqptEquipmentWorkRecord record = eqptEquipmentWorkRecordService.getOne(
                    new LambdaQueryWrapper<EqptEquipmentWorkRecord>()
                            .eq(EqptEquipmentWorkRecord::getProjectId, projectId)
                            .eq(EqptEquipmentWorkRecord::getEquipmentId, equipmentId)
                            .eq(EqptEquipmentWorkRecord::getWorkTime, workTime)
            );
            record.setStatus(status);
            eqptEquipmentWorkRecordService.updateById(record);
        }
    }
}
