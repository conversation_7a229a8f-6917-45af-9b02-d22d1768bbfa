package com.rcszh.api.service;

import com.rcszh.base.common.core.domain.entity.SysDept;
import com.rcszh.system.service.ISysDeptService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
public class ProjectRequireService {
    @Resource
    private ISysDeptService sysDeptService;

    public boolean checkInDept(Long sourceDeptId, String targetDeptId, String financeDeptId) {
        Long longTargetDeptId = Long.valueOf(targetDeptId);
        SysDept financeDept = sysDeptService.selectDeptById(Long.valueOf(financeDeptId));
        if (Objects.equals(sourceDeptId, longTargetDeptId)) {
            return financeDept.getLeader() != null;
        }
        List<SysDept> list = sysDeptService.selectAllChildrenDeptById(longTargetDeptId);
        boolean match = list.stream().anyMatch(dept -> sourceDeptId.equals(dept.getDeptId()));
        if (match) {
            return financeDept.getLeader() != null;
        }
        return false;
    }

}
