<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rcszh.report.mapper.PmsWorkRecordStatMapper">
    <select id="userWorkDurationStat" resultType="PmsWorkRecordStat">
        select wr.user_id,
         sum(wr.work_duration) as work_duration
        from pms_work_record_stat wr
        left join pms_project p on wr.project_id = p.id
            where wr.work_time >= #{startTime}
            and wr.work_time &lt;= #{endTime}
            <if test="userId != null">
                and wr.user_id = #{userId}
            </if>
            <if test="projectName != null and projectName != ''">
                AND p.name like concat('%', #{projectName}, '%')
            </if>
            group by wr.user_id
            order by wr.user_id asc
    </select>

    <select id="projectDetailStat" resultType="PmsWorkRecordStat">
        select wr.user_id,
        wr.project_id,
        sum(wr.work_duration) as work_duration
        from pms_work_record_stat wr
        where wr.work_time >= #{startTime}
        and wr.work_time &lt;= #{endTime}
        <if test="userId != null">
            and wr.user_id = #{userId}
        </if>
        group by wr.user_id,wr.project_id
    </select>

    <select id="findUserWorkTimeStat" resultType="PmsWorkRecordStat">
        select wr.user_id,
        wr.work_time,
        sum(wr.work_duration) as work_duration
        from pms_work_record_stat wr
        where wr.work_time >= #{startTime}
        and wr.work_time &lt;= #{endTime}
        and wr.user_id in
        <foreach item="userId" collection="userIds" open="(" separator="," close=")">
            #{userId}
        </foreach>
        group by wr.user_id,wr.work_time
    </select>

    <select id="findWorkRecordUserIds" resultType="Long">
        select wr.user_id
        from pms_work_record_stat wr
            left join pms_project p on wr.project_id = p.id
        where wr.work_time >= #{startTime}
        and wr.work_time &lt;= #{endTime}
        <if test="userId != null">
            and wr.user_id = #{userId}
        </if>
        <if test="projectName!= null and projectName !=''">
            and p.name like concat('%', #{projectName}, '%')
        </if>
        group by wr.user_id
        order by wr.user_id asc
    </select>

    <select id="userProjectWorkDurationStat" resultType="userProjectWorkDurationDTO">
        select wr.user_id,
               wr.project_id,
               sum(wr.work_duration) as work_duration
        from pms_work_record_stat wr
        where wr.work_time >= #{startTime}
          and wr.work_time &lt;= #{endTime}
        group by wr.user_id, wr.project_id
    </select>

    <select id="projectWorkDurationStat" resultType="com.rcszh.report.domain.PmsWorkRecordStat">
        select wr.project_id,
        sum(wr.work_duration) as work_duration
        from pms_work_record_stat wr
        left join pms_project p on wr.project_id = p.id
        where wr.work_time >= #{startTime}
        and wr.work_time &lt;= #{endTime}
        <if test="projectName != null and projectName != ''">
            AND p.name like concat('%', #{projectName}, '%')
        </if>
        group by wr.project_id
        order by wr.project_id asc
    </select>
    <select id="findByProjectId" resultType="com.rcszh.report.domain.PmsWorkRecordStat">
        select wr.work_time,
               sum(wr.work_duration) as work_duration
        from pms_work_record_stat wr
                 left join pms_project p on wr.project_id = p.id
        where wr.work_time >= #{startTime}
          and wr.work_time &lt;= #{endTime}
          and wr.project_id = #{projectId}
        group by MONTH (wr.work_time)
        order by wr.work_time asc
    </select>
    <select id="findCountYearDetail" resultType="java.lang.Long">
        SELECT COUNT(*) FROM (
        SELECT w.work_time,w.user_id,w.project_id FROM pms_work_record_stat w
        LEFT JOIN pms_project p ON w.project_id = p.id
        where w.work_time >= #{startTime}
        and w.work_time &lt;= #{endTime}
        <if test="projectName!= null and projectName!= ''">
            AND p.name like concat('%', #{projectName}, '%')
        </if>
        <if test="userId!= null and userId!= ''">
            AND w.user_id = #{userId}
        </if>
        GROUP BY Month(w.work_time),w.user_id,w.project_id
        ) AS q
    </select>
    <select id="findYearDetail" resultType="PmsWorkRecordStatTemplate">
        SELECT w.work_time,w.user_id,w.project_id FROM pms_work_record_stat w
        LEFT JOIN pms_project p ON w.project_id = p.id
        where w.work_time >= #{startTime}
        and w.work_time &lt;= #{endTime}
        <if test="projectName!= null and projectName!= ''">
            AND p.name like concat('%', #{projectName}, '%')
        </if>
        <if test="userId!= null and userId!= ''">
            AND w.user_id = #{userId}
        </if>
        GROUP BY Month(w.work_time),w.user_id,w.project_id
        ORDER BY work_time
        <if test="skip!= null and size!= null">
            LIMIT #{skip},#{size}
        </if>
    </select>
    <select id="findAllProjectId" resultType="java.lang.Long">
        SELECT DISTINCT w.project_id
        FROM pms_work_record_stat w
        left join pms_project p on w.project_id = p.id
        where w.work_time >= #{startTime}
        and w.work_time &lt;= #{endTime}
        <if test="userId != null and userId != ''">
            AND w.user_id = #{userId}
        </if>
        <if test="projectName != null and projectName != ''">
            AND p.name like concat('%', #{projectName}, '%')
        </if>
    </select>
    <select id="findByProjectIdList" resultType="com.rcszh.report.domain.PmsWorkRecordStat">
        select wr.work_time,
        wr.project_id,
        p.id as project_id,
        p.name as project_name,
        sum(wr.work_duration) as work_duration
        from pms_work_record_stat wr
        left join pms_project p on wr.project_id = p.id
        where wr.work_time >= #{startTime}
        and wr.work_time &lt;= #{endTime}
        and wr.project_id in
        <foreach item="projectId" collection="projectList" open="(" separator="," close=")">
            #{projectId}
        </foreach>
        group by MONTH (wr.work_time),wr.project_id
        order by wr.project_id asc,wr.work_time asc
    </select>
</mapper>