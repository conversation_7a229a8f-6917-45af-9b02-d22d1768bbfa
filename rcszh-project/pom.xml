<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.rcszh</groupId>
        <artifactId>pms</artifactId>
        <version>0.0.1</version>
    </parent>

    <artifactId>rcszh-project</artifactId>
    <name>项目管理</name>
    <version>0.0.1</version>
    <description>
        项目管理
    </description>
    <dependencies>
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
        </dependency>
        <dependency>
            <groupId>com.rcszh</groupId>
            <artifactId>rcszh-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.rcszh</groupId>
            <artifactId>rcszh-base-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ruoyi</groupId>
            <artifactId>ruoyi-framework</artifactId>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-models</artifactId>
        </dependency>
        <dependency>
            <groupId>com.rcszh</groupId>
            <artifactId>rcszh-basic-data</artifactId>
        </dependency>
        <dependency>
            <groupId>com.rcszh</groupId>
            <artifactId>rcszh-message</artifactId>
        </dependency>
    </dependencies>
</project>
