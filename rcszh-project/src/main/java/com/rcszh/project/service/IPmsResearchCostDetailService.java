package com.rcszh.project.service;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.rcszh.project.domain.PmsResearchCostDetail;
import com.rcszh.project.domain.PmsResearchCostRecord;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Date;

public interface IPmsResearchCostDetailService extends IService<PmsResearchCostDetail> {
    /**
     * 根据对应月份生成研发费用记录
     *
     * @param month 月份
     * @param file  excel 数据
     */
    JSONObject create(Date month, PmsResearchCostRecord pmsResearchCostRecord, MultipartFile file) throws IOException;
}
