package com.rcszh.project.service;

import cn.hutool.core.lang.tree.Tree;
import com.rcszh.base.common.core.page.TableDataInfo;
import com.rcszh.project.domain.PmsProjectTask;
import com.rcszh.project.domain.vo.TaskGroupVo;

import java.util.Collection;
import java.util.List;

public interface IPmsProjectTaskExtService {

    /**
     * 保存任务
     *
     * @param pmsProjectTask
     */
    void saveTask(PmsProjectTask pmsProjectTask);

    /**
     * 删除任务
     *
     * @param ids
     */
    void delTask(Long[] ids);

    /**
     * 根据项目id删除任务
     *
     * @param projectIds
     */
    void delTaskByProjectIds(List<Long> projectIds);

    /**
     * 获取任务
     *
     * @param projectId
     * @return
     */
    List<Tree<Long>> tree(Long projectId);

    List<Tree<Long>> getProjectTaskTreeByProjectId(Long projectId);

    /**
     * 获取任务
     *
     * @param projectId
     * @return
     */
    List<PmsProjectTask> getProjectTaskByProjectId(Long projectId);

    /**
     * 获取没有被使用的
     *
     * @param projectId
     * @return
     */
    List<PmsProjectTask> getNotUsedProjectTaskByProjectId(Long projectId);

    List<PmsProjectTask> getHomeAllProjectMilestones();

    /**
     * 我参与的任务/所有的
     *
     * @param pmsProjectTask
     * @return
     */
    TableDataInfo getTaskMemberships(PmsProjectTask pmsProjectTask, boolean pageFlag);

    /**
     * 我参与的任务/完成日期
     *
     * @param pmsProjectTask
     * @return
     */
    Collection<TaskGroupVo> getTaskGroup(PmsProjectTask pmsProjectTask);

    /**
     * 查询项目和任务下面对应的任务
     *
     * @param projectId
     * @param taskId
     * @return
     */
    List<Tree<Long>> findTaskOwnerByProjectIdAndTaskId(Long projectId, Long taskId);

    /**
     * 查询项目和任务下面对应的任务用户信息
     *
     * @param projectId
     * @param taskId
     * @return
     */
    List<Tree<Long>> findTaskUserByProjectIdAndTaskId(Long projectId, Long taskId);
}
