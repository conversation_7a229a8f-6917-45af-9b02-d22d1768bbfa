package com.rcszh.project.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.rcszh.project.domain.PmsProjectTaskUser;
import com.rcszh.project.domain.PmsProjectTaskUserRecord;

/**
 * 个人任务进度更新记录Service接口
 *
 * <AUTHOR>
 * @date 2024-11-28
 */
public interface IPmsProjectTaskUserRecordService extends IService<PmsProjectTaskUserRecord> {

    /**
     * 更新个人任务进度
     *
     * @param pmsProjectTaskUser
     */
    void updateTaskUser(PmsProjectTaskUser pmsProjectTaskUser);
}
