package com.rcszh.project.service;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.rcszh.project.domain.PmsProject;
import com.rcszh.project.domain.PmsProjectTask;
import com.rcszh.project.domain.template.PmsProjectTaskTemplate;
import com.rcszh.project.domain.vo.PmsProjectTaskDetailVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 项目任务Service接口
 *
 * <AUTHOR>
 * @date 2024-11-20
 */
public interface IPmsProjectTaskService extends IService<PmsProjectTask> {

    /**
     * 生成任务
     *
     * @param pmsProjectTask
     */
    void generateCode(PmsProjectTask pmsProjectTask);

    /**
     * 生成序号
     *
     * @param pmsProjectTask
     */
    void generateSerialNumber(PmsProjectTask pmsProjectTask);

    void reloadParentTask(boolean isNewTask, PmsProjectTask pmsProjectTask);

    void updateParentTask(PmsProjectTask pmsProjectTask);

    void fullProjectTask(PmsProjectTask pmsProjectTask);

    /**
     * 根据编码和项目id获取数据
     *
     * @param code
     * @param projectId
     * @return
     */
    PmsProjectTask getCodeAndProjectId(String code, Long projectId);

    List<PmsProjectTask> findChildrenByParentId(Long parentId, Long projectId);

    void delChildrenTask(Long parentId, List<Long> useIds);

    /**
     * 同步全部缓存
     *
     * @param pageNo
     */
    public void initAllToCache(int pageNo);

    /**
     * 初始化项目任务
     *
     * @param pmsProjectTask
     * @return
     */
    PmsProjectTask init(PmsProjectTask pmsProjectTask);

    /**
     * 递归查询所有的任务
     *
     * @param id
     * @return
     */
    List<PmsProjectTask> findAllSubTasks(Long id, Long projectId, List<PmsProjectTask> projectTasks);

    /**
     * 根据项目id获取父级任务
     *
     * @param projectId
     * @return
     */
    List<PmsProjectTask> findParentTaskByProjectIds(List<Long> projectId);

    /**
     * 根据项目ID对任务进行分组
     *
     * @param list
     * @return
     */
    Map<Long, List<PmsProjectTask>> groupTasksByProjectId(List<PmsProject> list);

    /**
     * 获取任务里程碑
     *
     * @param taskList
     * @return
     */
    List<PmsProjectTask> getTaskMilestones(List<PmsProjectTask> taskList);

    PmsProjectTask getTaskByProjectIdAndTaskId(Long projectId, Long taskId);

    List<PmsProjectTask> findAllSubTasksByTaskId(Long taskId, Long projectId, PmsProjectTask task);

    /**
     * 查询所有最尾端任务（没有子任务的任务)
     *
     * @param pmsProjectTask
     * @return
     */
    List<PmsProjectTask> getLeafTasks(PmsProjectTask pmsProjectTask);

    /**
     * 查询所有最尾端任务（没有子任务的任务)
     *
     * @param projectId
     * @return
     */
    List<PmsProjectTask> getLeafTasksByProjectId(Long projectId);

    /**
     * 新任务负责人通知
     *
     * @param ids
     */
    void notifyNewTaskAssignee(Long[] ids);

    /**
     * 新任务派发通知
     *
     * @param userIds
     */
    void notifyTaskAssignment(Long taskId, List<Long> userIds);

    /**
     * 获取所有项目的里程碑
     *
     * @param pmsProjectTask
     * @return
     */
    JSONArray getAllProjectMilestones(PmsProjectTask pmsProjectTask);

    /**
     * 根据任务负责人查找对应的项目 ID。
     *
     * @return
     */
    List<PmsProjectTask> findProjectTaskForTaskOwner();

    /**
     * 查询项目对应的负责人的任务
     *
     * @param projectId
     * @return
     */
    List<PmsProjectTask> findTaskOwnerByProjectId(Long projectId);

    PmsProjectTaskDetailVo getTaskDetailByTaskId(Long taskId);

    void processTaskDates(PmsProjectTask task);

    void updateProjectProgress(Long projectId);

    JSONObject importProjectTask(MultipartFile file, List<PmsProjectTaskTemplate> taskList, Long projectId, String generateTaskFlag, String sendMsgFlag);
}
