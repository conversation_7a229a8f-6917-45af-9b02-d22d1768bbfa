package com.rcszh.project.service;

import cn.hutool.json.JSONObject;
import com.rcszh.project.domain.PmsProjectTaskUser;
import com.rcszh.project.domain.template.PmsProjectTaskTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface IPmsProjectTaskUserExtService {

    /**
     * 保存任务成员
     *
     * @param userList
     * @param taskId
     */
    void saveTaskUser(List<PmsProjectTaskUser> userList, Long taskId);

    /**
     * 导入项目任务
     *
     * @param multipartFile
     * @param taskList
     * @param projectId
     * @param generateTaskFlag
     * @param sendMsgFlag
     * @return
     */
    JSONObject importProjectTask(MultipartFile multipartFile, List<PmsProjectTaskTemplate> taskList, Long projectId, String generateTaskFlag, String sendMsgFlag) throws Exception;

}
