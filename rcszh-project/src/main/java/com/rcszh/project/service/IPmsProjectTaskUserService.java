package com.rcszh.project.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.rcszh.project.domain.PmsProjectTask;
import com.rcszh.project.domain.PmsProjectTaskUser;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 项目任务成员Service接口
 *
 * <AUTHOR>
 * @date 2024-11-20
 */
public interface IPmsProjectTaskUserService extends IService<PmsProjectTaskUser> {

    /**
     * 根据任务id获取任务成员
     * @param taskId
     * @return
     */
    List<PmsProjectTaskUser> getTaskUserByTaskId(Long taskId);

    /**
     * 根据批量任务id获取任务成员
     * @param taskId
     * @return
     */
    Map<Long,List<PmsProjectTaskUser>> findTaskUserByTaskIds(List<Long> taskId);

    /**
     * 删除任务成员
     * @param taskIds
     */
    void delTaskUser(List<Long> taskIds);

    /**
     * 查询userId参与的所有任务
     * @param userId
     * @return
     */
    List<PmsProjectTaskUser> getProjectTaskByUserId(Long userId);

    void updateEstimatedHours(BigDecimal estimatedHours, Long id);

    void setProgressAndStatus(PmsProjectTask task, List<PmsProjectTaskUser> taskUserList);

}
