package com.rcszh.project.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.rcszh.project.domain.PmsLaborCostRateHis;

import java.util.List;

/**
 * 人工成本--历史费率Service接口
 *
 * <AUTHOR>
 * @date 2024-12-11
 */
public interface IPmsLaborCostRateHisService extends IService<PmsLaborCostRateHis> {

    void saveHis(PmsLaborCostRateHis pmsLaborCostRateHis);

    void deleteById(Long id);

    List<PmsLaborCostRateHis> findAll(Long userId);
}
