package com.rcszh.project.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.rcszh.project.domain.PmsResearchCostRecord;

import java.util.Date;
import java.util.List;

/**
 * 研发费用记录Service接口
 */
public interface IPmsResearchCostRecordService extends IService<PmsResearchCostRecord> {

    List<PmsResearchCostRecord> selectPage(PmsResearchCostRecord pmsResearchCostRecord);

    List<PmsResearchCostRecord> export(PmsResearchCostRecord pmsResearchCostRecord);

    /**
     * 新增导入记录
     */
    PmsResearchCostRecord addRecord(Date month, String fileUrl, String fileName);
}
