package com.rcszh.project.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.rcszh.project.domain.PmsProjectConfig;

import java.util.List;

public interface IPmsProjectConfigService extends IService<PmsProjectConfig> {

    public List<PmsProjectConfig> listByCategoryCode(String categoryCode);

    public void saveConfigs(List<PmsProjectConfig> configs);

    /**
     * 根据标签查询配置信息
     * 这类标签是指的项目的分类
     *
     * @param categoryCode 项目类别编码
     * @param tag          标签
     */
    List<PmsProjectConfig> findByTag(String categoryCode, String tag);
}
