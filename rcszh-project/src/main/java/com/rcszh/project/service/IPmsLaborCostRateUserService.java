package com.rcszh.project.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.rcszh.project.domain.PmsLaborCostRateUser;

import java.util.List;
import java.util.Map;

/**
 * 人工成本--成员Service接口
 *
 * <AUTHOR>
 * @date 2024-12-11
 */
public interface IPmsLaborCostRateUserService extends IService<PmsLaborCostRateUser> {

    void saveUser(List<Long> userIds,Long rateId);

    void deleteByIds(List<Long> ids);

    List<PmsLaborCostRateUser> queryList(Map<String, Object> params);
}
