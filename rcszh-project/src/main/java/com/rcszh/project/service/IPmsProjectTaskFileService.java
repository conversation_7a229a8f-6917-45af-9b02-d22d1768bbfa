package com.rcszh.project.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.rcszh.project.domain.PmsProjectTaskFile;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * 项目文件-文档Service接口
 *
 * <AUTHOR>
 * @date 2024-11-22
 */
public interface IPmsProjectTaskFileService extends IService<PmsProjectTaskFile> {

    void saveFile(PmsProjectTaskFile pmsProjectTaskFile);

    void batchSaveFile(MultipartFile[] multipartFiles,Long projectId,Long taskId) throws IOException;

    List<PmsProjectTaskFile> findTaskFile(PmsProjectTaskFile pmsProjectTaskFile);

    void downloadFlatPackage(HttpServletResponse response, PmsProjectTaskFile pmsProjectTaskFile) throws IOException;

    void updateFile(PmsProjectTaskFile pmsProjectTaskFile);

    void deleteFile(String fileId);
}
