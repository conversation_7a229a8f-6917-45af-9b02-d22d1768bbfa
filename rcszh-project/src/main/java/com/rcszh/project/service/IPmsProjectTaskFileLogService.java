package com.rcszh.project.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.rcszh.project.domain.PmsProjectTaskFile;
import com.rcszh.project.domain.PmsProjectTaskFileLog;
import com.rcszh.project.enums.PmsProjectTaskFileLogType;

import java.util.List;

public interface IPmsProjectTaskFileLogService extends IService<PmsProjectTaskFileLog> {
    // 上传文件时生成日志
    void createLog(PmsProjectTaskFile pmsProjectTaskFile, PmsProjectTaskFileLogType type);

    // 查询日志
    List<PmsProjectTaskFileLog> getList(PmsProjectTaskFileLog pmsProjectTaskFile);
}
