<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rcszh.project.mapper.PmsProjectMapper">

    <resultMap type="PmsProject" id="PmsProjectResult">
        <result property="id"    column="id"    />
        <result property="code"    column="code"    />
        <result property="name"    column="name"    />
        <result property="associatedProjectId"    column="associated_project_id"    />
        <result property="projectAbbr"    column="project_abbr"    />
        <result property="categoryCode"    column="category_code"    />
        <result property="projectStatus"    column="project_status"    />
        <result property="projectManagerId"    column="project_manager_id"    />
        <result property="projectSupervisorId"    column="project_supervisor_id"    />
        <result property="projectAssistantId"    column="project_assistant_id"    />
        <result property="plannedStartAt"    column="planned_start_at"    />
        <result property="plannedFinishBy"    column="planned_finish_by"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="projectAssistantId2"    column="project_assistant_id2"    />
        <result property="projectAssistantId3"    column="project_assistant_id3"    />
        <result property="projectAssistantId4"    column="project_assistant_id4"    />
        <result property="projectAssistantId5"    column="project_assistant_id5"    />
        <result property="salespersonId"    column="salesperson_id"    />
        <result property="province"    column="province_code"    />
        <result property="city"    column="city_code"    />
        <result property="area"    column="area_code"    />
        <result property="address"    column="address"    />
        <result property="currencyCode"    column="curreny_code"    />
        <result property="rate"    column="rate"    />
        <result property="contractAmount"    column="contract_amount"    />
        <result property="taxRate"    column="tax_rate"    />
        <result property="amountExcludingTax"    column="amount_excluding_tax"    />
        <result property="description"    column="description"    />
        <result property="customerId"    column="customer_id"    />
        <result property="companyId"    column="company_id"    />
        <result property="actualStartDate"    column="actual_start_date"    />
        <result property="actualEndDate"    column="actual_end_date"    />
        <result property="projectMode"    column="project_mode"    />
    </resultMap>

    <sql id="selectPmsProjectVo">
        select id, code, name, associated_project_id, project_abbr, category_code, project_status, project_manager_id, project_supervisor_id, project_assistant_id, planned_start_at, planned_finish_by, create_by, create_time, update_by, update_time, project_assistant_id2, project_assistant_id3, project_assistant_id4, project_assistant_id5, salesperson_id, province_code, city_code, area_code, address, curreny_code, rate, contract_amount, tax_rate, amount_excluding_tax, description,customer_id,company_id,actual_start_date,actual_end_date from pms_project
    </sql>

    <select id="findStatUserIdsByTime" resultType="java.lang.Long">
        select * from (
        select distinct pu.user_id
        from pms_project p,pms_project_user pu
        left join sys_user u on pu.user_id = u.user_id
        where p.id = pu.project_id
        and p.planned_start_at &lt;= #{endTime}
        and (p.planned_finish_by >= #{startTime} or p.planned_finish_by is null)
        and pu.status = 'Y'
        <if test="workRecordListDto.userId != null and workRecordListDto.userId != ''">
            AND pu.user_id = #{workRecordListDto.userId}
        </if>
        <if test="workRecordListDto.deptId != null and workRecordListDto.deptId != ''">
            AND u.dept_id = #{workRecordListDto.deptId}
        </if>
        <if test="workRecordListDto.projectName != null and workRecordListDto.projectName != ''">
            AND p.name like concat('%', #{workRecordListDto.projectName}, '%')
        </if>
        UNION
        select distinct t.user_id
        from pms_public_user t
        left join pms_public_user_line tl on t.id = tl.public_user_id
        left join pms_public_user_project tt on tl.id = tt.public_user_line_id
        left join pms_project tp on tt.project_id = tp.id
        left join sys_user u on t.user_id = u.user_id
        where t.deleted = 'N'
        and tl.start_time &lt;= DATE_FORMAT(#{endTime}, '%Y-%m-%d')
        and tl.end_time >= DATE_FORMAT(#{startTime}, '%Y-%m-%d')
        <if test="workRecordListDto.userId != null and workRecordListDto.userId != ''">
            AND t.user_id = #{workRecordListDto.userId}
        </if>
        <if test="workRecordListDto.deptId != null and workRecordListDto.deptId != ''">
            AND u.dept_id = #{workRecordListDto.deptId}
        </if>
        <if test="workRecordListDto.projectName != null and workRecordListDto.projectName != ''">
            AND (tl.allocation_type='all' or tp.name like concat('%', #{workRecordListDto.projectName}, '%'))
        </if>
        UNION
        select wr.user_id
        from sys_work_record wr
        left join pms_project wp on wr.project_id = wp.id
        left join sys_user u on wr.user_id = u.user_id
        where wr.status='audited'
        and wr.work_time &lt;= #{endTime}
        and wr.work_time >= #{startTime}
        <if test="workRecordListDto.userId != null and workRecordListDto.userId != ''">
            and wr.user_id = #{workRecordListDto.userId}
        </if>
        <if test="workRecordListDto.deptId != null and workRecordListDto.deptId != ''">
            AND u.dept_id = #{workRecordListDto.deptId}
        </if>
        <if test="workRecordListDto.projectName != null and workRecordListDto.projectName != ''">
            AND wp.name like concat('%', #{workRecordListDto.projectName}, '%')
        </if>
        group by wr.user_id
        ) as t
        order by t.user_id asc
    </select>

    <select id="findByTime" resultType="com.rcszh.project.domain.PmsProject">
        select * from pms_project p
        where 1=1
        <if test="startTime != null">
            and (p.planned_finish_by >= #{startTime} or p.planned_finish_by is null)
        </if>
        <if test="endTime != null">
            and p.planned_start_at &lt;= #{endTime}
        </if>
        order by p.id asc
    </select>


    <select id="getDepartmentDailyWorkHours" resultType="java.lang.Long">
        SELECT DISTINCT d.dept_id from sys_dept d
        inner join sys_user su on su.dept_id = d.dept_id
        inner join sys_work_record swr on swr.user_id = su.user_id
        inner join pms_project p on p.id = swr.project_id
        where 1=1 and d.del_flag = '0' and d.status = '0'
        <if test="projectName!= null">
            and p.name like concat('%', #{projectName}, '%')
        </if>
        <if test="projectManagerId != null">
            and p.project_manager_id = #{projectManagerId}
        </if>
        <if test="workTimeStartDate != null and workTimeEndDate != null">
            and swr.work_time &lt;= #{workTimeEndDate} and swr.work_time &gt;= #{workTimeStartDate}
        </if>
        <if test="userId != null">
            and su.user_id = #{userId}
        </if>
        <if test="deptId != null">
            and (d.dept_id = #{deptId} OR find_in_set(#{deptId}, ancestors))
        </if>
        order by d.dept_id asc
    </select>

    <select id="getProjectWorkHoursOverview" resultType="java.lang.Long">
        SELECT DISTINCT p.id from pms_project p
        inner join sys_work_record r on p.id = r.project_id
        where 1=1
        <if test="projectName!= null">
            and p.name like concat('%', #{projectName}, '%')
        </if>
        <if test="projectManagerId != null">
            and p.project_manager_id = #{projectManagerId}
        </if>
        <if test="workTimeStartDate != null and workTimeEndDate != null">
            and r.work_time &lt;= #{workTimeEndDate} and r.work_time &gt;= #{workTimeStartDate}
        </if>
        <if test="userId != null">
            and r.user_id = #{userId}
        </if>
        order by p.id asc
    </select>
</mapper>