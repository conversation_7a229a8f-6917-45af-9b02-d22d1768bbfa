<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rcszh.project.mapper.PmsLaborCostRateUserMapper">
    
    <resultMap type="PmsLaborCostRateUser" id="PmsLaborCostRateUserResult">
        <result property="id"    column="id"    />
        <result property="rateId"    column="rate_id"    />
        <result property="userId"    column="user_id"    />
    </resultMap>

    <sql id="selectPmsLaborCostRateUserVo">
        select id, rate_id, user_id from pms_labor_cost_rate_user
    </sql>

    <select id="queryList" resultType="com.rcszh.project.domain.PmsLaborCostRateUser">
        SELECT r.id,
        u.user_id,
        u.user_name,
        u.nick_name,
        h.start_date,
        h.end_date,
        h.normal_cost_rate,
        h.overtime_cost_rate
        FROM pms_labor_cost_rate_user r
        LEFT JOIN sys_user u ON u.user_id = r.user_id
        LEFT JOIN (
        SELECT user_id, start_date,end_date, normal_cost_rate, overtime_cost_rate
        FROM pms_labor_cost_rate_his
        WHERE (user_id, start_date) IN (
        SELECT user_id, MAX(start_date)
        FROM pms_labor_cost_rate_his
        GROUP BY user_id
        )
        ) h ON h.user_id = r.user_id
        WHERE r.rate_id = #{rateId}
        <if test="searchValue != null and searchValue != ''">
            AND (u.user_name LIKE CONCAT('%', #{searchValue}, '%')
            OR u.nick_name LIKE CONCAT('%', #{searchValue}, '%'))
        </if>

    </select>
</mapper>