<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rcszh.project.mapper.PmsProjectUserMapper">
    <select id="selectUnallocatedList" resultType="com.rcszh.base.common.core.domain.entity.SysUser">
        select u.* from sys_user u
        where u.user_id not in (select p.user_id from pms_project_user p where p.project_id = #{projectId})
        and u.status='0'
        and u.del_flag='0'
        and u.user_name != 'admin'
        <if test="searchValue != null">
            and (u.user_name like concat('%',#{searchValue},'%') or u.nick_name like concat('%',#{searchValue},'%'))
        </if>
        <if test="deptId != null and deptId != 0">
            AND (u.dept_id = #{deptId} OR u.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{deptId}, ancestors) ))
        </if>
        <if test="filteredUserIdList != null and filteredUserIdList.size > 0">
            and u.user_id not in
            <foreach collection="filteredUserIdList" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
    </select>

    <select id="findProjectUserList" resultType="com.rcszh.base.common.core.domain.entity.SysUser">
        select u.* from sys_user u
        where u.user_id in (
        select p.user_id from pms_project_user p where p.project_id = #{projectId}
            <if test="filterRoleList != null and filterRoleList.size > 0">
                and p.project_role in
                <foreach collection="filterRoleList" item="role" open="(" separator="," close=")">
                    #{role}
                </foreach>
            </if>
        )
        <if test="searchValue != null">
            and (u.user_name like concat('%',#{searchValue},'%') or u.nick_name like concat('%',#{searchValue},'%'))
        </if>
    </select>

    <select id="findProjectLeaderList" resultType="com.rcszh.base.common.core.domain.entity.SysUser">
        select u.* from sys_user u
        where u.user_id in (select p.user_id from pms_project_user p where p.project_id = #{projectId} and p.user_id =
        p.group_leader_id)
        <if test="searchValue != null">
            and (u.user_name like concat('%',#{searchValue},'%') or u.nick_name like concat('%',#{searchValue},'%'))
        </if>
    </select>

    <select id="findUnallocatedListDirectly" resultType="com.rcszh.base.common.core.domain.entity.SysUser">
        select u.* from sys_user u
        where u.user_id not in (select p.user_id from pms_project_user p where p.project_id = #{projectId})
        and u.status='0'
        and u.del_flag='0'
        and u.user_name != 'admin'
        <if test="searchValue != null">
            and (u.user_name like concat('%',#{searchValue},'%') or u.nick_name like concat('%',#{searchValue},'%'))
        </if>
        <if test="filteredUserIdList != null and filteredUserIdList.size > 0">
            and u.user_id not in
            <foreach collection="filteredUserIdList" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        <if test="deptId != null and deptId != 0">
            AND (u.dept_id = #{deptId} OR u.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{deptId},
            ancestors) ))
        </if>
    </select>
</mapper>